plugins {
    id 'io.spring.dependency-management'
}


dependencies {
    implementation project(':common')

    implementation(enforcedPlatform("com.xiaohongshu:infra-root-pom:${root_pom_version}"))


    implementation('org.apache.commons:commons-lang3:3.6')
    implementation('com.alibaba:druid:1.1.3')
    implementation('ch.qos.logback:logback-classic:1.2.3')
    implementation('ch.qos.logback:logback-core:1.2.3')

    //spring
    implementation('mysql:mysql-connector-java:8.0.16')
    implementation('org.mybatis:mybatis:3.5.5')
    implementation('org.mybatis:mybatis-spring:2.0.5')
    implementation('javax.servlet:javax.servlet-api:4.0.0')

    //utils&middle
    implementation("com.xiaohongshu.infra.midware:redis-spring")
    implementation("com.xiaohongshu.infra.midware:mysql-spring")
    implementation('com.xiaohongshu:infra-framework-log')
    implementation('com.xiaohongshu:utils-log')
    implementation('com.xiaohongshu:gateway-starter')

    implementation('com.xiaohongshu:thrift-springboot') { exclude group: 'com.alibaba', module: 'fastjson' }
    implementation('com.xhs.cache:cache-client:0.0.9-SNAPSHOT') { changing = true }

    implementation('com.xhs.enterprise:erp-common:1.1.4-SNAPSHOT') {
        changing = true
        exclude group: 'com.alibaba', module: 'fastjson'
        exclude group: 'com.ctrip.framework.apollo', module: 'apollo-openapi'
        exclude group: 'com.ctrip.framework.apollo', module: 'apollo-core'
        exclude group: 'com.ctrip.framework.apollo', module: 'apollo-client'
        exclude group: 'com.xuxueli', module: 'xxl-job-core'
        exclude group: 'javax.servlet', module: 'javax.servlet-api'
    }

    implementation('com.github.pagehelper:pagehelper-spring-boot-starter:1.2.3')
    implementation(group: 'org.apache.poi', name: 'poi-ooxml', version: '3.14')
    implementation('org.assertj:assertj-core:3.11.1')
    implementation('com.xhs.enterprise:rbac-client:*******-SNAPSHOT'){force(true)}
    implementation('com.xiaohongshu.fls.thrift:lib-thrift-empservice:master-20221203.033547-49')

    //消息中台
    implementation('com.xiaohongshu.fls.thrift:lib-thrift-oamiddle:master-SNAPSHOT')

    // 短信接入
    implementation('com.xiaohongshu.sns.thrift:sms-api:master-SNAPSHOT')
    //新ehr
    implementation 'com.xiaohongshu.fls.thrift:lib-thrift-ehrservice:1.2.14'
    
    compileOnly 'org.projectlombok:lombok:1.18.2'
    annotationProcessor 'org.projectlombok:lombok:1.18.2'
    
    // 配置中心
    implementation('com.xiaohongshu:infra-redconf-client-all:2.0.0')

    implementation 'io.springfox:springfox-swagger2:2.9.2'
    implementation 'io.springfox:springfox-swagger-ui:2.9.2'

    // 上线修改
    implementation('com.xiaohongshu.fls.thrift:lib-thrift-oa-office-idl:master-SNAPSHOT')

    // MQ消息
    implementation 'com.xiaohongshu:events-client:1.0.7.5'
}