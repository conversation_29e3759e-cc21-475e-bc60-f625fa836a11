<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhs.oa.itemCollection.mapper.TItemsSpecMapper" >

	<sql id="BaseColumn">
          id     id,
          item_id  itemId,
          specification     specification,
          unit  unit,
          is_valid     isValid,
          operator_no     operatorNo,
          operator     operator,
          operate_time     operateTime
    </sql>

	<select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.xhs.oa.itemCollection.model.TItemsSpec">
	    select 
	    <include refid="BaseColumn"/>
	    from t_items_spec
	    where id = #{id}
	</select>

    <select id="selectByItemId" resultType="com.xhs.oa.itemCollection.model.TItemsSpec">
        select <include refid="BaseColumn"></include>
        from t_items_spec
        where item_id=#{itemId} and is_valid=1
    </select>

    <select id="selectFirst" resultType="com.xhs.oa.itemCollection.model.TItemsSpec">
        select <include refid="BaseColumn"></include>
        from t_items_spec
        where is_valid=1
        order by id
        limit 1
    </select>

    <select id="selectSpecByItemIds" resultType="com.xhs.oa.itemCollection.model.TItemsSpec">
        select <include refid="BaseColumn"></include>
        from t_items_spec
        where is_valid=1 and item_id in
        <foreach collection="list" item="itemId" separator="," open="(" close=")">
            #{itemId}
        </foreach>
    </select>

    <select id="queryItemSpecNameBySpecIds" resultType="com.xhs.oa.itemCollection.dto.ItemSpecDTO">
        select
            a.id itemId,
            a.item itemName,
            b.id specId,
            b.specification specName,
            b.unit unit
        from t_items a
        join t_items_spec b on b.item_id=a.id and b.is_valid=1
        where b.id in
        <foreach collection="list" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>


    <insert id="insert" parameterType="com.xhs.oa.itemCollection.model.TItemsSpec" useGeneratedKeys="true" keyProperty="id">
    insert into t_items_spec (
          item_id,
          specification,
          unit,
          is_valid,
          operator_no,
          operator,
          operate_time
      )
    values (
           #{itemId},
           #{specification},
           #{unit},
           1,
           #{operatorNo},
           #{operator},
           now()
      )
  </insert>

  
  <update id="updateByPrimaryKey" parameterType="com.xhs.oa.itemCollection.model.TItemsSpec">
    update t_items_spec
    set 
           id = #{id},

           item_id=#{itemId},

           specification = #{specification},

           is_valid = #{isValid},

           operator_no = #{operatorNo},

           operator = #{operator},

         operate_time = #{operateTime}
    where id = #{id}
  </update>

</mapper>   
