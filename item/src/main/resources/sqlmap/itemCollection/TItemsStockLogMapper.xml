<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhs.oa.itemCollection.mapper.TItemsStockLogMapper" > 

	<sql id="BaseColumn">
          id     id,
          item_area_id     itemAreaId,
          item_spec_id     itemSpecId,
          user_id  userId,
          operate_type     operateType,
          amount     amount,
          reason     reason,
          operator_no     operatorNo,
          operator     operator,
          operate_time     operateTime,
          operator_phone     operatorPhone
    </sql>

	<select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.xhs.oa.itemCollection.model.TItemsStockLog">
	    select 
	    <include refid="BaseColumn"/>
	    from t_items_stock_log
	    where id = #{id}
	</select>

    <select id="selectByUserIdAndDate" resultType="com.xhs.oa.itemCollection.model.TItemsStockLog">
        select <include refid="BaseColumn"></include>
        from t_items_stock_log
        where operate_type=#{paramsMap.operateType} and user_id=#{paramsMap.userId}
        <if test="paramsMap.startTime!=null">
        and <![CDATA[ operate_time>=#{paramsMap.startTime}]]>
        </if>
        <if test="paramsMap.endTime!=null">
        and <![CDATA[ operate_time<=#{paramsMap.endTime}]]>
        </if>
        and item_spec_id in
        <foreach collection="paramsMap.specIds" item="specId" separator="," open="(" close=")">
            #{specId}
        </foreach>
    </select>

    <select id="queryPagedReceiveRecord" resultType="com.xhs.oa.itemCollection.model.TItemsStockLog">
        select
            t.user_id  userId,
            t.amount amount,
            t.operate_time  operateTime,
            t.item_spec_id itemSpecId
        from t_items_stock_log t
        where t.operate_type=#{paramsMap.operateType} and t.item_spec_id in
        <foreach collection="paramsMap.itemSpecIds" item="specId" separator="," open="(" close=")">
            #{specId}
        </foreach>
        <if test="paramsMap.userId!=null and paramsMap.userId!=''">
            and t.user_id=#{paramsMap.userId}
        </if>
        <if test="paramsMap.operatorId!=null and paramsMap.operatorId!=''">
            and t.operator_no=#{paramsMap.operatorId}
        </if>
        order by t.id desc
    </select>

    <select id="queryLogs" resultType="com.xhs.oa.itemCollection.model.TItemsStockLog">
        select <include refid="BaseColumn"></include>
        <include refid="pageLog"></include>
        order by t.id
    </select>

    <select id="queryPagedLogCounts" resultType="java.lang.Integer">
        select count(1)
        <include refid="pageLog"></include>
    </select>

    <sql id="pageLog">
        from t_items_stock_log t
        where t.item_spec_id in
        <foreach collection="specIds" item="specId" separator="," open="(" close=")">
            #{specId}
        </foreach>
        and t.item_area_id in
        <foreach collection="areaIds" item="areaId" separator="," open="(" close=")">
            #{areaId}
        </foreach>
    </sql>

    <insert id="insert" parameterType="com.xhs.oa.itemCollection.model.TItemsStockLog" useGeneratedKeys="true" keyProperty="id">
    insert into t_items_stock_log (
          item_area_id,
          item_spec_id,
          user_id,
          operate_type,
          amount,
          reason,
        <if test="operatorNo!=null">
          operator_no,
        </if>
        <if test="operator!=null">
          operator,
        </if>
        <if test="operatorPhone!=null">
          operator_phone,
        </if>
          operate_time
      )
    values (
           #{itemAreaId},
           #{itemSpecId},
           #{userId},
           #{operateType},
           #{amount},
           #{reason},
        <if test="operatorNo!=null">
           #{operatorNo},
        </if>
        <if test="operator!=null">
           #{operator},
        </if>
        <if test="operatorPhone!=null">
           #{operatorPhone},
        </if>
           now()
      )
  </insert>


</mapper>   
