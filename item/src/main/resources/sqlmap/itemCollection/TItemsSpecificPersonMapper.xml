<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhs.oa.itemCollection.mapper.TItemsSpecificPersonMapper" >

	<sql id="BaseColumn">
          id     id,
          item_id     itemId,
          user_id  userId,
          is_valid     isValid,
          operator_no     operatorNo,
          operator     operator,
          operate_time     operateTime
    </sql>

    <select id="queryUserByItemId" parameterType="int" resultType="com.xhs.oa.itemCollection.model.TItemsSpecificPerson">
        select <include refid="BaseColumn"/>
        from t_items_specific_person where item_id = #{itemId}
    </select>

    <insert id="batchInsert">
    insert into t_items_specific_person (
        item_id,
        user_id,
        is_valid,
        operator_no,
        operator,
        operate_time
      )
    values
        <foreach collection="list" item="item" separator="," >
            (
            #{item.itemId},
            #{item.userId},
            1,
            #{item.operatorNo},
            #{item.operator},
            now()
            )
        </foreach>
  </insert>


</mapper>   
