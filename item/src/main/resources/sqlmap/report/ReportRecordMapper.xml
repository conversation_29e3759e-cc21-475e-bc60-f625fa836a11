<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhs.oa.report.mapper.ReportRecordMapper" > 

	<sql id="BaseColumn">
          id     id,
          report_name     reportName,
          source_name     sourceName,
          report_status     reportStatus,
          creator_no     creator<PERSON>o,
          creator     creator,
          create_time     createTime,
          update_time     updateTime,
          download_url     downloadUrl
    </sql>

	<select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.xhs.oa.report.model.ReportRecord">
	    select 
	    <include refid="BaseColumn"/>
	    from report_record
	    where id = #{id}
	</select>


  <insert id="insert" parameterType="com.xhs.oa.report.model.ReportRecord" useGeneratedKeys="true" keyProperty="id">
    insert into report_record (
          report_name,
          source_name,
          report_status,
          creator_no,
          creator,
          create_time,
          update_time,
          download_url
      )
    values (
           #{reportName},
           #{sourceName},
           #{reportStatus},
           #{creatorNo},
           #{creator},
           now(),
           now(),
          #{downloadUrl}
      )
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.xhs.oa.report.model.ReportRecord">
    update report_record
    <set>
        <if test="id != null">
           id = #{id},
        </if>
        <if test="reportName != null">
           report_name = #{reportName},
        </if>
        <if test="sourceName != null">
           source_name = #{sourceName},
        </if>
        <if test="reportStatus != null">
           report_status = #{reportStatus},
        </if>
        <if test="creatorNo != null">
           creator_no = #{creatorNo},
        </if>
        <if test="creator != null">
           creator = #{creator},
        </if>
        <if test="createTime != null">
           create_time = #{createTime},
        </if>
        <if test="updateTime != null">
           update_time = #{updateTime},
        </if>
        <if test="downloadUrl != null">
           download_url = #{downloadUrl},
        </if>
    </set>
    where id = #{id}
  </update>
  
  <update id="updateByPrimaryKey" parameterType="com.xhs.oa.report.model.ReportRecord">
    update report_record
    set 
           id = #{id},

           report_name = #{reportName},

           source_name = #{sourceName},

           report_status = #{reportStatus},

           creator_no = #{creatorNo},

           creator = #{creator},

           create_time = #{createTime},

           update_time = #{updateTime},

         download_url = #{downloadUrl}
    where id = #{id}
  </update>

    <sql id="reportListQuerySql">
        from report_record
        where creator_no = #{userId}
        <if test="reportQueryParam.reportName != null and reportQueryParam.reportName != '' ">
            and report_name like concat('%',#{reportQueryParam.reportName},'%')
        </if>
        <if test="reportQueryParam.status != null and reportQueryParam.status != '' ">
            and report_status = #{reportQueryParam.status}
        </if>
    </sql>

    <select id="reportListQueryByPage" resultType="com.xhs.oa.report.model.ReportRecord">
        select
        <include refid="BaseColumn"/>
        <include refid="reportListQuerySql"/>
        order by id desc
        limit #{reportQueryParam.start},#{reportQueryParam.pageSize}
    </select>

    <select id="reportListCountQuery" resultType="int">
         select count(1)
        <include refid="reportListQuerySql"/>
    </select>

    <update id="updateReportStatusById" >
        update report_record
        set report_status = #{reportStatus},
        update_time = now()
        <if test="downloadUrl != null and downloadUrl != '' " >
            ,download_url = #{downloadUrl}
        </if>
        where id = #{id}
    </update>

</mapper>   
