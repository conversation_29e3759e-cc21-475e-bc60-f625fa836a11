<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhs.oa.workflow.mapper.ActIdUserMapper" > 

	<sql id="BaseColumn">
          ID_     id,
          REV_     rev,
          FIRST_     first,
          LAST_     last,
          EMAIL_     email,
          RED_NAME   redName,
          RED_MAIL    redMail,
          PWD_     pwd,
          PICTURE_ID_     pictureId,
          AVATAR_URL  avatarUrl,
          LEADER_ID  leaderId,
          DEPARTMENT_ID departmentId,
          WORKING_PLACE workingPlace,
          MO<PERSON>LE_PHONE mobilePhone,
          ID_NUMBER idNumber,
          ACCOUNT_STATUS accountStatus,
          EMPLOY_TYPE employType,
          COMPANY_NAME companyName,
          COMPANY_NAME_NEW companyNameNew,
          IS_HAVE_SUB isHaveSub,
          IS_HAVE_REGULAR_SUB isHasRegularSub,
          LEADER_ID_PATH leaderIdPath,
          BIRTHDAY  birthday,
          ENTRY_DATE entryDate,
          CREATE_TIME createTime,
          UPDATE_TIME updateTime,
          EMPLOYMENT_TYPE employmentType
    </sql>

	<select id="selectByPrimaryKey" parameterType="java.lang.String" resultType="com.xhs.oa.workflow.model.ActIdUser">
	    select 
	    <include refid="BaseColumn"/>
	    from ACT_ID_USER
	    where ID_ = #{id}
	</select>

    <select id="getSubCountByLeaderID" parameterType="java.lang.String" resultType="int">
        select count(1) from ACT_ID_USER where LEADER_ID = #{id} and ACCOUNT_STATUS=1
    </select>


  <insert id="insert" parameterType="com.xhs.oa.workflow.model.ActIdUser">
    insert into ACT_ID_USER (
          ID_,
          REV_,
          FIRST_,
          RED_NAME,
          LAST_,
          EMAIL_,
          RED_MAIL,
          PWD_,
          PICTURE_ID_,
          AVATAR_URL,
          LEADER_ID,
          DEPARTMENT_ID,
          WORKING_PLACE,
          MOBILE_PHONE,
          ID_NUMBER,
          ACCOUNT_STATUS,
          EMPLOY_TYPE,
          COMPANY_NAME,
          BIRTHDAY,
          ENTRY_DATE,
          CREATE_TIME,
          UPDATE_TIME,
          CREATOR_NO,
          CREATOR,
          UPDATOR_NO,
          UPDATOR,
          EMPLOYMENT_TYPE
      )
    values (
           #{id},
           #{rev},
           #{first},
           #{redName},
           #{last},
           #{email},
           #{redMail},
           #{pwd},
          #{pictureId},
          #{avatarUrl},
          #{leaderId},
          #{departmentId},
          #{workingPlace},
          #{mobilePhone},
          #{idNumber},
          #{accountStatus},
          #{employType},
          #{companyName},
          #{birthday},
          #{entryDate},
          #{createTime},
          #{updateTime},
          #{creatorNo},
          #{creator},
          #{updatorNo},
          #{updator},
          #{employmentType}
      )
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.xhs.oa.workflow.model.ActIdUser">
    update ACT_ID_USER
    <set>
        <if test="first != null and first!='' ">
           FIRST_ = #{first},
        </if>
        <if test="last != null">
           LAST_ = #{last},
        </if>
        <if test="email != null and email!='' ">
           EMAIL_ = #{email},
        </if>
        <if test="pwd != null and pwd!='' ">
           PWD_ = #{pwd},
        </if>
        <if test="avatarUrl!=null and avatarUrl!=''">
            AVATAR_URL = #{avatarUrl},
        </if>
        <if test="leaderId != null">
            LEADER_ID = #{leaderId},
        </if>
        <if test="departmentId != null">
            DEPARTMENT_ID = #{departmentId},
        </if>
        <if test="workingPlace != null  and workingPlace!='' ">
            WORKING_PLACE = #{workingPlace},
        </if>
        <if test="mobilePhone != null and mobilePhone!='' ">
            MOBILE_PHONE = #{mobilePhone},
        </if>
        <if test="idNumber != null and idNumber!='' ">
            ID_NUMBER = #{idNumber},
        </if>
        <if test="accountStatus != null">
            ACCOUNT_STATUS = #{accountStatus},
        </if>
        <if test="employType != null">
            EMPLOY_TYPE = #{employType},
        </if>
        <if test="updatorNo != null and updatorNo!='' ">
            UPDATOR_NO = #{updatorNo},
        </if>
        <if test="updator != null and updator!='' ">
            UPDATOR = #{updator},
        </if>
        <if test="companyName != null and companyName!='' ">
            COMPANY_NAME = #{companyName},
        </if>
        <if test="isHaveSub != null ">
            IS_HAVE_SUB = #{isHaveSub},
        </if>
        <if test="birthday != null ">
            BIRTHDAY = #{birthday},
        </if>
        <if test="entryDate != null ">
            ENTRY_DATE = #{entryDate},
        </if>
        <if test="employmentType != null ">
            EMPLOYMENT_TYPE = #{employmentType},
        </if>
    </set>
    where ID_ = #{id}
  </update>

    <update id="batchUpdateActIdUsers" parameterType="java.util.List">
        <foreach collection="list" separator=";" item="item">
            update ACT_ID_USER
            <set>
                <if test="item.first != null and item.first!='' ">
                    FIRST_ = #{item.first},
                </if>
                <if test="item.last != null">
                    LAST_ = #{item.last},
                </if>
                <if test="item.email != null and item.email!='' ">
                    EMAIL_ = #{item.email},
                </if>
                <if test="item.pwd != null and item.pwd!='' ">
                    PWD_ = #{item.pwd},
                </if>
                <if test="item.avatarUrl!=null and item.avatarUrl!=''">
                    AVATAR_URL = #{item.avatarUrl},
                </if>
                <if test="item.leaderId != null">
                    LEADER_ID = #{item.leaderId},
                </if>
                <if test="item.departmentId != null">
                    DEPARTMENT_ID = #{item.departmentId},
                </if>
                <if test="item.workingPlace != null  and item.workingPlace!='' ">
                    WORKING_PLACE = #{item.workingPlace},
                </if>
                <if test="item.mobilePhone != null and item.mobilePhone!='' ">
                    MOBILE_PHONE = #{item.mobilePhone},
                </if>
                <if test="item.idNumber != null and item.idNumber!='' ">
                    ID_NUMBER = #{item.idNumber},
                </if>
                <if test="item.accountStatus != null">
                    ACCOUNT_STATUS = #{item.accountStatus},
                </if>
                <if test="item.employType != null">
                    EMPLOY_TYPE = #{item.employType},
                </if>
                <if test="item.updatorNo != null and item.updatorNo!='' ">
                    UPDATOR_NO = #{item.updatorNo},
                </if>
                <if test="item.updator != null and item.updator!='' ">
                    UPDATOR = #{item.updator},
                </if>
                <if test="item.companyName != null and item.companyName!='' ">
                    COMPANY_NAME = #{item.companyName},
                </if>
                <if test="item.isHaveSub != null ">
                    IS_HAVE_SUB = #{item.isHaveSub},
                </if>
                <if test="item.birthday != null ">
                    BIRTHDAY = #{item.birthday},
                </if>
                <if test="item.entryDate != null ">
                    ENTRY_DATE = #{item.entryDate},
                </if>
                <if test="item.employmentType != null ">
                    EMPLOYMENT_TYPE = #{item.employmentType},
                </if>
            </set>
            where ID_ = #{item.id}
        </foreach>
    </update>

  
  <update id="updateByPrimaryKey" parameterType="com.xhs.oa.workflow.model.ActIdUser">
    update ACT_ID_USER
    set 
           ID_ = #{id},

           REV_ = #{rev},

           FIRST_ = #{first},

           LAST_ = #{last},

           EMAIL_ = #{email},

           PWD_ = #{pwd},

          IS_HAVE_SUB=#{isHaveSub},

         PICTURE_ID_ = #{pictureId}
    where ID_ = #{id}
  </update>

    <update id="updateEmailById" parameterType="com.xhs.oa.workflow.model.ActIdUser">
        update ACT_ID_USER
        set
            EMAIL_ = #{email}
            <if test="redName != null">
                ,RED_NAME = #{redName}
            </if>
            <if test="redMail != null">
                ,RED_MAIL = #{redMail}
            </if>
        where ID_ = #{id}
    </update>

    <select id="selectByEmailAndTypeAndWorkPlace" resultType="com.xhs.oa.workflow.model.ActIdUser">
        select
        <include refid="BaseColumn"/>
        from ACT_ID_USER
        where 1=1
        <if test="email != null and email.size()>0">
            and EMAIL_ in
            <foreach collection="email" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="employTypeList != null and employTypeList.size()>0">
            and EMPLOY_TYPE in
            <foreach collection="employTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="workPlaceList!=null and workPlaceList.size()>0">
            and WORKING_PLACE in
            <foreach collection="workPlaceList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        limit 1000
    </select>

    <select id="selectByEmail" parameterType="java.lang.String" resultType="com.xhs.oa.workflow.model.ActIdUser">
        select
        <include refid="BaseColumn"/>
        from ACT_ID_USER
        where EMAIL_ = #{email}
    </select>

    <select id="getLeaderUserList" resultType="com.xhs.oa.workflow.model.ActIdUser">
        select
        <include refid="BaseColumn"/>
        from ACT_ID_USER
        where IS_HAVE_SUB = 1 and account_status = 1
    </select>
    

    <select id="findUserInfoWithNameMapping" resultType="com.xhs.oa.workflow.model.ActIdUser">
        select
        <include refid="BaseColumn"/>
        from ACT_ID_USER
        where (FIRST_ like concat('%',#{value},'%') or RED_NAME like concat('%',#{value},'%'))
        <if test="accountStatus != null">
            and ACCOUNT_STATUS = #{accountStatus}
        </if>
        <if test="employTypeList != null and employTypeList.size()>0">
            and EMPLOY_TYPE in
            <foreach collection="employTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="workPlaceList!=null and workPlaceList.size()>0">
            and WORKING_PLACE in
            <foreach collection="workPlaceList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        limit #{limit}
    </select>

    <select id="findUserInfoWithEmailMapping" resultType="com.xhs.oa.workflow.model.ActIdUser">
        select
        <include refid="BaseColumn"/>
        from ACT_ID_USER
        where EMAIL_ like concat('%',#{value},'%')
        limit #{limit}
    </select>

    <update id="updateIsHaveSub" >
        update ACT_ID_USER set IS_HAVE_SUB  = #{isHaveSub} where ID_ = #{id}
    </update>

    <select id="findUserInfoByEmailWithLike" resultType="com.xhs.oa.workflow.model.ActIdUser">
        select
        <include refid="BaseColumn"/>
        from ACT_ID_USER
        where EMAIL_ like concat(#{value},'%')
    </select>

    <select id="findUserInfoByRedEmail" resultType="com.xhs.oa.workflow.model.ActIdUser">
        select
        <include refid="BaseColumn"/>
        from ACT_ID_USER
        where RED_MAIL = #{userEmail}
    </select>

    <select id="findUserInfoByRedEmailWithLike" resultType="com.xhs.oa.workflow.model.ActIdUser">
        select
        <include refid="BaseColumn"/>
        from ACT_ID_USER
        where RED_MAIL like concat(#{redMail},'%')
    </select>

    <select id="queryUserListByIds" parameterType="list" resultType="com.xhs.oa.workflow.model.ActIdUser">
        select
          <include refid="BaseColumn" />
        from ACT_ID_USER
        where ID_ in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="findUserInfoByEmailList" parameterType="java.util.List" resultType="com.xhs.oa.workflow.model.ActIdUser">
        select
        <include refid="BaseColumn" />
        from ACT_ID_USER
        where EMAIL_ in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="queryUserWithNameMapping"  resultType="com.xhs.oa.workflow.model.ActIdUser">
        select
        <include refid="BaseColumn" />
        from ACT_ID_USER
        where (FIRST_ like concat('%',#{userName},'%') or RED_NAME like concat('%',#{userName},'%')) and ACCOUNT_STATUS = 1
        and ID_ != #{userId}
        and EMPLOY_TYPE = 0
        order by ID_ asc
        limit #{start}, #{pageSize}
    </select>

    <select id="querySimpleDirectSubByUserId" resultType="com.xhs.oa.workflow.model.ActIdUser">
        select
          ID_     id,
          LEADER_ID  leaderId,
          IS_HAVE_SUB isHaveSub,
          LEADER_ID_PATH leaderIdPath,
          EMPLOY_TYPE employType,
          EMPLOYMENT_TYPE employmentType
        from ACT_ID_USER
        where ACCOUNT_STATUS = 1
        and LEADER_ID = #{userId}
    </select>

    <select id="querySimpleDirectSubByUserIdWithoutCharlwin" resultType="com.xhs.oa.workflow.model.ActIdUser">
        select
        ID_ id,
        LEADER_ID leaderId,
        IS_HAVE_SUB isHaveSub,
        LEADER_ID_PATH leaderIdPath,
        EMPLOY_TYPE employType,
        EMPLOYMENT_TYPE employmentType
        from ACT_ID_USER
        where ACCOUNT_STATUS = 1 and ID_ != *********
        and LEADER_ID = #{userId}
    </select>

    <update id="updateLeaderIdPathByUserId">
        update ACT_ID_USER set
        IS_HAVE_SUB  = #{isHaveSub},
        IS_HAVE_REGULAR_SUB = #{isHaveRegularSub},
        LEADER_ID_PATH = #{leaderIdPath}
        where ID_ = #{userId}
    </update>

    <select id="queryUsersByIds" parameterType="java.util.List" resultType="com.xhs.oa.workflow.model.ActIdUser">
        select
        <include refid="BaseColumn" />
        from ACT_ID_USER
        where ID_ in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        ORDER BY FIELD(ID_,
        <foreach collection="list" index="index" item="item" separator=",">
            #{item}
        </foreach>)
    </select>

    <select id="queryUserVoInfoByEmails" parameterType="java.util.List" resultType="com.xhs.oa.workflow.model.ActIdUser" >
        select
            ID_     id,
            FIRST_     first,
            EMAIL_     email,
            LEADER_ID  leaderId,
            ACCOUNT_STATUS  accountStatus,
            DEPARTMENT_ID  departmentId
        from ACT_ID_USER
        where
            EMAIL_ in
            <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
    </select>

    <select id="queryUserVoInfoByEmailsOrRedMails" parameterType="java.util.List" resultType="com.xhs.oa.workflow.model.ActIdUser">
        select
            ID_     id,
            FIRST_     first,
            EMAIL_     email,
            RED_MAIL   redMail,
            LEADER_ID  leaderId,
            ACCOUNT_STATUS  accountStatus,
            DEPARTMENT_ID  departmentId
        from ACT_ID_USER
        where
            EMAIL_ in
            <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        or  RED_MAIL in
            <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
    </select>

    <select id="findDepartmentNamePathByUserId" resultType="java.lang.String">
        select d.department_name_path
        from ACT_ID_USER u
                 join department d on u.DEPARTMENT_ID=d.department_id and d.is_valid=1 and d.is_deleted=0 and status=1
        where u.ACCOUNT_STATUS=1 and u.ID_=#{userId}
    </select>

    <select id="findDepartmentIdPathByUserId" resultType="java.lang.String">
        select d.department_id_path
        from ACT_ID_USER u
        join department d on u.DEPARTMENT_ID=d.department_id and d.is_valid=1 and d.is_deleted=0 and status=1
        where u.ACCOUNT_STATUS=1 and u.ID_=#{userId}
    </select>

    <select id="findAllValidUserVoInfo" resultType="com.xhs.oa.workflow.model.ActIdUser">
        select
            ID_     id,
            FIRST_     first,
            EMAIL_     email
        from ACT_ID_USER
        where EMPLOY_TYPE = 0 and DEPARTMENT_ID != 149680 and ACCOUNT_STATUS =1 and FIRST_!='毛文超'
              and FIRST_ not like '%测试%'
              and FIRST_ not like '%实施%'
              <![CDATA[ and ENTRY_DATE < '2018-11-01' ]]>
    </select>

    <select id="selectByPhoneNum" resultType="com.xhs.oa.workflow.model.ActIdUser">
        select
        <include refid="BaseColumn"/>
        from ACT_ID_USER
        where MOBILE_PHONE = #{phoneNum} and ACCOUNT_STATUS=1
    </select>

    <select id="selectByIdNumber" resultType="com.xhs.oa.workflow.model.ActIdUser">
        select
        <include refid="BaseColumn"/>
        from ACT_ID_USER
        where ID_NUMBER = #{idNumber} order by ID_ desc limit 1
    </select>

    <sql id="DirectSubSql">
        from ACT_ID_USER
        where ACCOUNT_STATUS = 1
        and LEADER_ID = #{userId}
    </sql>
    <update id="updateRedNameAndEmail">
        update ACT_ID_USER
        set RED_NAME = #{redName},
            RED_MAIL = #{redEmail}
        where ID_ = #{userId}
    </update>

    <update id="updateRedName">
        update ACT_ID_USER
        set RED_NAME = #{redName}
        where ID_ = #{userId}
    </update>

    <update id="updateUsersForCompanyName">
        <foreach collection="list" item="item" separator=";">
            update ACT_ID_USER
            set COMPANY_NAME_NEW =#{item.companyName}
            where ID_ = #{item.id}
        </foreach>
    </update>

    <update id="updateForCompanyNameNew">
        update ACT_ID_USER
        set COMPANY_NAME_NEW = ''
        where COMPANY_NAME_NEW <![CDATA[ <> ]]> ''
    </update>

    <select id="getAllValidInnerUsersList" resultType="com.xhs.oa.workflow.model.ActIdUser">
        select
        <include refid="BaseColumn"/>
        from ACT_ID_USER
        where ACCOUNT_STATUS = 1
          and EMPLOY_TYPE = 0
        order by id_
    </select>

    <sql id="queryParamSql">
        where 1 = 1
        <if test="accountStatus != null">
            and ACCOUNT_STATUS= #{accountStatus}
        </if>
        <if test="employTypes!=null and employTypes.size()>0">
            and EMPLOY_TYPE in
            <foreach collection="employTypes" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        <if test="employmentTypeList!=null and employmentTypeList.size()>0">
            and EMPLOYMENT_TYPE in
            <foreach collection="employmentTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        <!-- 更新时间 -->
        <if test="updateTimeStart != null" >
            and UPDATE_TIME &gt;= #{updateTimeStart}
        </if>

        <if test="updateTimeEnd != null" >
            and UPDATE_TIME &lt;= #{updateTimeEnd}
        </if>

    </sql>


    <select id="findUserByRedName" resultType="com.xhs.oa.workflow.model.ActIdUser">
        select <include refid="BaseColumn"></include>
        from ACT_ID_USER
        where RED_NAME=#{name}
    </select>

    <select id="findUserByUserName" resultType="com.xhs.oa.workflow.model.ActIdUser">
        select <include refid="BaseColumn"></include>
        from ACT_ID_USER
        where FIRST_=#{name}
    </select>

    <select id="findUserByUserNameOrRedName" resultType="com.xhs.oa.workflow.model.ActIdUser">
        select <include refid="BaseColumn"></include>
        from ACT_ID_USER
        where FIRST_ in
        <foreach collection="nameList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        or RED_NAME in
        <foreach collection="nameList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectValidUserByEmail" resultType="com.xhs.oa.workflow.model.ActIdUser">
        select
        <include refid="BaseColumn"/>
        from ACT_ID_USER
        where ACCOUNT_STATUS=1 and EMAIL_ = #{email}
    </select>


    <select id="selectValidUserByEmailOrRedMail" resultType="com.xhs.oa.workflow.model.ActIdUser">
        select
        <include refid="BaseColumn"/>
        from ACT_ID_USER
        where ACCOUNT_STATUS=1 and
        (EMAIL_ = #{email}
        OR RED_MAIL = #{email}
        )
    </select>

    <select id="findValidUserByDepartmentId" resultType="com.xhs.oa.workflow.model.ActIdUser">
        select
        <include refid="BaseColumn"/>
        from ACT_ID_USER
        where ACCOUNT_STATUS = 1
          and EMPLOY_TYPE = 0
          and DEPARTMENT_ID = #{departmentId}
    </select>


    <select id="findExtraUserByDepartmentIds" resultType="com.xhs.oa.workflow.model.ActIdUser">
        select
        <include refid="BaseColumn"/>
        from ACT_ID_USER
        where ACCOUNT_STATUS = 1
        <if test="departmentIdList!=null and departmentIdList.size()>0">
            and DEPARTMENT_ID in
            <foreach collection="departmentIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="employTypeList!=null and employTypeList.size()>0">
            and EMPLOY_TYPE in
            <foreach collection="employTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="workPlaceList!=null and workPlaceList.size()>0">
            and WORKING_PLACE in
            <foreach collection="workPlaceList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <!-- 根据部门ID/人员类型/base地/入职时间查询用户列表 -->
    <select id="findTeamBuildingUsers" resultType="com.xhs.oa.workflow.model.ActIdUser">
        select
        <include refid="BaseColumn"/>
        from ACT_ID_USER
        where ACCOUNT_STATUS = 1
        <if test="departmentIdList!=null and departmentIdList.size()>0">
            and DEPARTMENT_ID in
            <foreach collection="departmentIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="employmentTypeList!=null and employmentTypeList.size()>0">
            and EMPLOYMENT_TYPE in
            <foreach collection="employmentTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="workPlaceList!=null and workPlaceList.size()>0">
            and WORKING_PLACE in
            <foreach collection="workPlaceList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <!-- 与workPlaceList互斥 -->
        <if test="otherWorkPlace != null and otherWorkPlace.size()>0">
            and WORKING_PLACE not in
            <foreach collection="otherWorkPlace" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <!-- 截止地入职日期，入职时间要在其之前 -->
        <if test="endEntryDate != null" >
            and ENTRY_DATE &lt;= #{endEntryDate}
        </if>
    </select>

    <select id="findAllSubUserByLeaderIdPath" resultType="com.xhs.oa.workflow.model.ActIdUser">
        select
        <include refid="BaseColumn"/>
        from ACT_ID_USER
        where ACCOUNT_STATUS = 1
        and LEADER_ID_PATH like concat(#{leaderIdPath},'%')
        and EMPLOY_TYPE = 0
    </select>

    <select id="findValidUserListByEmployType" resultType="com.xhs.oa.workflow.model.ActIdUser">
        select
        <include refid="BaseColumn"/>
        from ACT_ID_USER
        where ACCOUNT_STATUS = 1
        <if test="employTypeList != null and employTypeList.size() > 0">
            and EMPLOY_TYPE in
            <foreach collection="employTypeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectValidUserListByLeaderId" resultType="java.lang.String">
        select ID_
        from ACT_ID_USER
        where ACCOUNT_STATUS = 1 and LEADER_ID = #{leaderId}
        <if test="employTypeList != null and employTypeList.size() > 0">
            and EMPLOY_TYPE in
            <foreach collection="employTypeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="queryCompanyList" resultType="java.lang.String">
        select distinct COMPANY_NAME from ACT_ID_USER where COMPANY_NAME!=''
    </select>

    <select id="queryByMailsAndEmployTypes" resultType="com.xhs.oa.workflow.model.ActIdUser">
        select
            ID_     id,
            FIRST_     first,
            EMAIL_     email,
            RED_MAIL   redMail,
            LEADER_ID  leaderId,
            ACCOUNT_STATUS  accountStatus,
            DEPARTMENT_ID  departmentId
        from ACT_ID_USER
        where
            EMPLOY_TYPE in
            <foreach collection="employTypeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            and (
                EMAIL_ in
                <foreach collection="emailList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                or  RED_MAIL in
                <foreach collection="emailList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            )
    </select>

    <delete id="deleteByPrimaryKey"  parameterType="java.lang.String">
        delete from ACT_ID_USER
        where ID_ = #{id}
    </delete>


    <sql id="OuterBaseColumn">
      aiu.ID_     id,
      aiu.REV_     rev,
      aiu.FIRST_     first,
      aiu.LAST_     last,
      aiu.EMAIL_     email,
      aiu.RED_NAME   redName,
      aiu.RED_MAIL    redMail,
      aiu.PWD_     pwd,
      aiu.PICTURE_ID_     pictureId,
      aiu.AVATAR_URL  avatarUrl,
      aiu.LEADER_ID  leaderId,
      aiu.DEPARTMENT_ID departmentId,
      aiu.WORKING_PLACE workingPlace,
      aiu.MOBILE_PHONE mobilePhone,
      aiu.ID_NUMBER idNumber,
      aiu.ACCOUNT_STATUS accountStatus,
      aiu.EMPLOY_TYPE employType,
      aiu.COMPANY_NAME companyName,
      aiu.IS_HAVE_SUB isHaveSub,
      aiu.LEADER_ID_PATH leaderIdPath,
      aiu.BIRTHDAY  birthday,
      aiu.ENTRY_DATE entryDate,
      aiu.CREATE_TIME createTime,
      aiu.UPDATE_TIME updateTime,
      aiu.EMPLOYMENT_TYPE employmentType,
      asi.gender,
      uli.last_work_day lastWorkDay
    </sql>


    <sql id="conditionSql">
        where 1 = 1
        <if test="accountStatus != null">
            and aiu.ACCOUNT_STATUS= #{accountStatus}
        </if>
        <if test="employTypes!=null and employTypes.size()>0">
            and aiu.EMPLOY_TYPE in
            <foreach collection="employTypes" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        <if test="employmentTypeList!=null and employmentTypeList.size()>0">
            and aiu.EMPLOYMENT_TYPE in
            <foreach collection="employmentTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        <!-- 更新时间 -->
        <if test="updateTimeStart != null" >
            and aiu.UPDATE_TIME &gt;= #{updateTimeStart}
        </if>

        <if test="updateTimeEnd != null" >
            and aiu.UPDATE_TIME &lt;= #{updateTimeEnd}
        </if>
    </sql>



    <select id="getUserForRenewCompanyName" resultType="com.xhs.oa.workflow.model.ActIdUser">
        select
        ID_     id,
        FIRST_     first,
        COMPANY_NAME companyName,
        COMPANY_NAME_NEW companyNameNew
        from ACT_ID_USER
        where EMPLOYMENT_TYPE
        in (1,2,4) and ACCOUNT_STATUS = 1 and COMPANY_NAME <![CDATA[ <> ]]> ''
        order by ID_ desc
    </select>


    <update id="releaseRedNames">
        update ACT_ID_USER set
        RED_NAME = '',
        RED_MAIL = '',
        UPDATOR_NO = #{updatorNo,jdbcType=VARCHAR},
        UPDATOR = #{updator,jdbcType=VARCHAR},
        update_time = now()
        where ID_ in
        <foreach collection="userIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>

    <update id="batchUpdateBpoDept">
        update ACT_ID_USER
        set DEPARTMENT_ID = #{departmentId},
        UPDATOR_NO = #{updatorNo},
        UPDATOR = #{updator}
        where ID_ in
        <foreach collection="userIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and EMPLOYMENT_TYPE = #{employmentType}
    </update>

    <select id="getLeavedUserCount" resultType="java.lang.Integer">
        select count(aiu.ID_) from xhsoa.ACT_ID_USER aiu
            left join account_sync_info asi on aiu.ID_ = asi.user_id
        where aiu.ACCOUNT_STATUS = 0 and asi.emp_status = 8
    </select>


    <select id="getCheckRedMailUser" resultType="com.xhs.oa.workflow.model.ActIdUser">
        select
        <include refid="BaseColumn"/>
        from xhsoa.ACT_ID_USER
        where EMPLOY_TYPE = 0 and ACCOUNT_STATUS = 1
          and LENGTH(RED_MAIL) > 0 AND EMAIL_ != RED_MAIL

    </select>
    <select id="findUserByDepartmentId" resultType="java.lang.String">
        select
            DEPARTMENT_ID
        from xhsoa.ACT_ID_USER
        where ID_ = #{createId}
    </select>
    <select id="findUserById" parameterType="java.lang.String" resultType="com.xhs.oa.workflow.model.ActIdUser">
        select
        <include refid="BaseColumn"/>
        from xhsoa.ACT_ID_USER
        where ID_ = #{personInCharge}
    </select>

    <select id="queryLimitUserByDepartmentId" resultType="com.xhs.oa.workflow.model.ActIdUser">
        select
        <include refid="BaseColumn"/>
        from ACT_ID_USER
        where ACCOUNT_STATUS = 1
        and DEPARTMENT_ID = #{departmentId}
        limit 1
    </select>

    <select id="queryAvailableUserListByIds" parameterType="list" resultType="com.xhs.oa.workflow.model.ActIdUser">
        select
        <include refid="BaseColumn"/>
        from ACT_ID_USER
        where ACCOUNT_STATUS = 1
        and EMPLOY_TYPE = 0
        and
        ID_ in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>


    </select>


    <update id="batchUpdateAvatar" parameterType="java.util.List">
        <foreach collection="list" separator=";" item="item">
            update ACT_ID_USER
            <set>
                <if test="item.avatarUrl!=null and item.avatarUrl!=''">
                    AVATAR_URL = #{item.avatarUrl}
                </if>
            </set>
            where ID_ = #{item.id}
        </foreach>
    </update>


</mapper>
