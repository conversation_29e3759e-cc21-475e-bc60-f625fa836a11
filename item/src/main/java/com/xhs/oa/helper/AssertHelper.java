package com.xhs.oa.helper;

import com.xhs.finance.exception.BusinessException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collection;
import java.util.Objects;


/**
 * @Description : 断言工具类
 * @Date : 2018/1/2
 */
public class AssertHelper {

    private static final Logger log = LoggerFactory.getLogger(AssertHelper.class);

    public static void notNull(Object data, String message) {
        if (Objects.isNull(data)) {
            throw new BusinessException(message);
        }
    }

    public static void notBlank(String data, String message) {
        if (StringUtils.isBlank(data)) {
            throw new BusinessException(message);
        }
    }

    public static void check(boolean expression, String message) {
        if (!expression) {
            throw new BusinessException(message);
        }
    }

    public static void equals(Object data,Object otherData, String message) {
        if (!Objects.equals(data,otherData)) {
            throw new BusinessException(message);
        }
    }

    public static void isNull(Object data, String message) {
        if (!Objects.isNull(data)) {
            throw new BusinessException(message);
        }
    }

    public static void notEmpty(Collection<?> c, String message) {
        if (c == null || c.isEmpty()) {
            throw new BusinessException(message);
        }
    }
}
