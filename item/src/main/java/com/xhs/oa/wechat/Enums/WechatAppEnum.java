package com.xhs.oa.wechat.Enums;

import com.xhs.finance.exception.BusinessException;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

@Getter
public enum WechatAppEnum {

    OA("oa","oasis的配置文件app前缀定义", "oa.agentid"),
    OKR("okr","ork的配置文件app前缀定义", "okr.agentid"),
    PERFORMANCE("performance","performance的配置文件app前缀定义", "performance.agentid"),
    REDNAME("redName","薯名的配置文件app前缀定义","redName.agentid"),
    ITEM("item","物品领用的配置文件app前缀定义","item.agentid"),
    PROJECTMANAGER("projectmanager","周报的配置文件app前缀定义","projectmanager.agentid"),
    SHUXIAOBAO("sxb", "薯小宝管家的配置文件app前缀定义", "sxb.agentid")
    ;

    private String code;

    private String name;

    private String agentKey;

    WechatAppEnum(String code, String name, String agentKey){
        this.code = code;
        this.name = name;
        this.agentKey = agentKey;
    }

    public static WechatAppEnum getEnumByCode(String code){
        WechatAppEnum[] enums = WechatAppEnum.values();
        List<WechatAppEnum> enumList = Arrays.asList(enums);
        return enumList.stream().filter(a->a.getCode().equals(code)).
                findFirst().orElseThrow(() -> new BusinessException(code));
    }
}
