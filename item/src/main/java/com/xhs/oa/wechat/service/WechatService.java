package com.xhs.oa.wechat.service;

import com.alibaba.fastjson.JSONObject;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.xhs.cache.RedisClient;
import com.xhs.finance.exception.BusinessException;
import com.xhs.finance.sso.model.UserInfo;
import com.xhs.finance.utils.HttpClientUtils;
import com.xhs.oa.beiseng.enums.EmploymentTypeEnum;
import com.xhs.oa.common.constant.CommonConstant;
import com.xhs.oa.login.service.LoginService;
import com.xhs.oa.wechat.Enums.WechatAppEnum;
import com.xhs.oa.workflow.model.ActIdUser;
import com.xhs.oa.workflow.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
public class WechatService
{


	@Autowired
	private LoginService loginService;

	@Autowired
	private Environment environment;

	@Autowired
	private RedisClient redisClient;

	@Autowired
	private UserService userService;


	/**
	 * response 成功返回码
	 */
	private final String SUCCESS_RESPONSE_CODE = "0";

	/**
	 * response access_token无效 错误码
	 */
	private final String ERROR_ACCESS_TOKEN_CODE = "40014";

	/**
	 * access_token 缓存key
	 */
	private final String WECHAT_ACCESS_TOKEN_KEY = "_wechat_access_token_key";


	/**
	 * 缓存有效时间
	 */
	private final long BUS_TIME_OUT = 30 * 60;


	@ApolloJsonValue("${oa_red_name_apply_send_wechat_tester_emails:[]}")
	private List<String> wechatTesters;




	/**
	 * 根据企业微信返回的code获取用户信息并进行模拟登陆
	 *
	 * @param code
	 * @return
	 */
	public UserInfo getUserInfoByCode(String code, String appType)
	{
		UserInfo userInfo = null;
		try
		{
			//log.error("当前登录的code" + code);
			//获取token
			String userInfoUrl = CommonConstant.ENTERPRISE_WECHAT_USERINFO_URL + "&code=" + code;
			JSONObject jsonObject = this.sendWechartRequest(appType, CommonConstant.METHOD_GET, userInfoUrl, null);
			String userEmail = jsonObject.getString("UserId");
			userInfo = loginService.simulationLogin(userEmail);
			//log.error("当前登录的userEmail:" + userEmail + "userInfo:" + JSONObject.toJSONString(userInfo));
		} catch (BusinessException e)
		{
			throw e;
		} catch (Exception e)
		{
			log.error("模拟登陆异常", e);
			throw new BusinessException("模拟登陆异常");
		}

		return userInfo;
	}

	/**
	 * 获取access_token
	 *
	 * @param appType
	 * @return
	 */
	private String getWechartToken(String appType)
	{
		String key = appType + WECHAT_ACCESS_TOKEN_KEY;
		String accessToken = (String) redisClient.get(key);
		if (StringUtils.isNotBlank(accessToken))
		{
			return accessToken;
		}
		String tokenUrl;
		if (WechatAppEnum.OA.getCode().equals(appType))
		{
			tokenUrl = CommonConstant.ENTERPRISE_WECHAT_TOKEN_URL_PRE + environment.getProperty("oa.corpsecret");
		} else if (WechatAppEnum.OKR.getCode().equals(appType))
		{
			tokenUrl = CommonConstant.ENTERPRISE_WECHAT_TOKEN_URL_PRE + environment.getProperty("okr.corpsecret");
		} else if (WechatAppEnum.PERFORMANCE.getCode().equals(appType))
		{
			tokenUrl = CommonConstant.ENTERPRISE_WECHAT_TOKEN_URL_PRE + environment.getProperty("performance.corpsecret");
		} else if (WechatAppEnum.REDNAME.getCode().equals(appType))
		{
			tokenUrl = CommonConstant.ENTERPRISE_WECHAT_TOKEN_URL_PRE + environment.getProperty("redName.corpsecret");
		} else if (WechatAppEnum.ITEM.getCode().equals(appType))
		{
			tokenUrl = CommonConstant.ENTERPRISE_WECHAT_TOKEN_URL_PRE + environment.getProperty("item.corpsecret");
		} else if (WechatAppEnum.PROJECTMANAGER.getCode().equals(appType))
		{
			tokenUrl = CommonConstant.ENTERPRISE_WECHAT_TOKEN_URL_PRE + environment.getProperty("projectmanager.corpsecret");
		} else if (WechatAppEnum.SHUXIAOBAO.getCode().equals(appType))
		{
			tokenUrl = CommonConstant.ENTERPRISE_WECHAT_TOKEN_URL_PRE + environment.getProperty("sxb.corpsecret");
		}
		else
		{
			throw new BusinessException("未知的app类型");
		}
		String result = null;
		try
		{
			result = HttpClientUtils.doGet(tokenUrl);
			JSONObject resultData = JSONObject.parseObject(result);
			String errorCode = resultData.getString("errcode");
			if (!SUCCESS_RESPONSE_CODE.equals(errorCode))
			{
				throw new BusinessException("获取企业微信token失败：" + resultData.getString("errmsg"));
			}
			JSONObject jsonObject = JSONObject.parseObject(result);
			accessToken = jsonObject.getString("access_token");
			redisClient.set(key, accessToken, BUS_TIME_OUT);
			return accessToken;
		} catch (BusinessException e)
		{
			throw e;
		} catch (Exception e)
		{
			log.error("获取企业微信token异常:" + result, e);
			throw new BusinessException("获取企业微信token异常：" + result, e);
		}
	}

	/**
	 * 调用企业微信接口 并对返回结果进行验证
	 *
	 * @param appType
	 * @param requestType
	 * @param url
	 * @param param
	 * @return
	 */
	private JSONObject sendWechartRequest(String appType, String requestType, String url, String param)
	{
		JSONObject resultData = null;
		try
		{

			// 测试环境 只给白名单用户推送企业微信
			String env = StringUtils.isEmpty(System.getProperty("user.env")) ? "local" : System.getProperty("user.env");
			log.info("sendWechartRequest env is {}" , env );
			JSONObject paramObject = JSONObject.parseObject(param);
			List<String> toUsers = Optional.ofNullable(paramObject).filter(item -> item.containsKey("touser")).map(item -> item.getString("touser"))
					.map(touser -> Arrays.asList(touser.split("\\|"))).orElse(Collections.EMPTY_LIST);
			if (!"beta".equals(env) && !"prod".equals(env) && StringUtils.isNoneBlank(param)) {
				// 测试环境只给白名单用户发送企业微信，白名单用户列表 与 toUserEmails 取交集
				List<String> intersection = wechatTesters.stream().filter(item -> toUsers.contains(item)).collect(Collectors.toList());
				log.info(" MailSendService.sendMailWithLocalTemplateAsync 测试环境白名单用户 交集 intersection = {} , toUsers = {} ,  发送企业微信白名单列表 wechatTesterEmails = {} " , intersection , toUsers , wechatTesters );

				if (CollectionUtils.isNotEmpty(intersection)) {
					paramObject.put("touser", StringUtils.join(intersection.toArray(), "|"));
					param =paramObject.toJSONString();
				}else {
					log.info(" 该用户 {} 不属于测试环境白名单列表，请先在Apollo配置测试环境白名单 oa_red_name_apply_send_wechat_tester_emails " , toUsers );
					return new JSONObject();
				}
			}

			//如果是BPO，就不发送微信提醒
			if(isBPO(toUsers)){
				log.info("该用户 {} 是BPO，不发送微信提醒", toUsers);
				return new JSONObject();
			}

			resultData = sendRequest(appType, requestType, url, param);
			log.info(" WechatService.sendWechartRequest 发送邮件参数 param = {} , resultData = {}  " , param , resultData );

			String errorCode = resultData.getString("errcode");
			if (!SUCCESS_RESPONSE_CODE.equals(errorCode))
			{
				if (ERROR_ACCESS_TOKEN_CODE.equals(errorCode))
				{
					//如果错误信息是token无效 则清楚token缓存 重新获取token
					String key = appType + WECHAT_ACCESS_TOKEN_KEY;
					redisClient.remove(key);
					resultData = sendRequest(appType, requestType, url, param);
					errorCode = resultData.getString("errcode");
					if (!SUCCESS_RESPONSE_CODE.equals(errorCode))
					{
						log.error("企业微信接口调用失败:" + resultData);
						throw new BusinessException("企业微信接口调用失败：" + errorCode + "errmsg:" + resultData.getString("errmsg"));
					}
				} else
				{
					log.error("企业微信接口调用失败:" + resultData);
					throw new BusinessException("企业微信接口调用失败：" + errorCode + "errmsg:" + resultData.getString("errmsg"));
				}
			}
			return resultData;
		} catch (BusinessException e)
		{
			throw e;
		} catch (Exception e)
		{
			log.error("企业微信接口调用出现异常:" + resultData, e);
			throw new BusinessException("企业微信接口调用出现异常：" + resultData, e);
		}
	}

	/**
	 * 判断是否是BPO账号
	 * @param toUsers
	 * @return
	 */
	private Boolean isBPO(List<String> toUsers){
		if(CollectionUtils.isEmpty(toUsers)){
			return false;
		}
		List<ActIdUser> actIdUsers = userService.queryUsersByIds(toUsers);
		List<ActIdUser> bpoList = actIdUsers.stream()
				.filter(v -> v.getEmploymentType().equals(EmploymentTypeEnum.OUTER_BPO.getCode())).collect(Collectors.toList());
		return CollectionUtils.isEmpty(bpoList) ? false : true;
	}

	/**
	 * 企业微信请求封装
	 *
	 * @param appType
	 * @param requestType
	 * @param url
	 * @param param
	 * @return
	 */
	public JSONObject sendRequest(String appType, String requestType, String url, String param)
	{
		String accessToken = getWechartToken(appType);
		log.info("accessToken:{}", accessToken);
		if (url.indexOf("?") != -1)
		{
			url = url + "&access_token=" + accessToken;
		} else
		{
			url = url + "?access_token=" + accessToken;
		}
		String result;
		log.info("微信企业(sendRequest),请求参数,appType:{},requestType:{},url:{},param:{}", appType, requestType, url, param);
		if (CommonConstant.METHOD_GET.equals(requestType))
		{
			result = HttpClientUtils.doGet(url);
		} else if (CommonConstant.METHOD_POST.equals(requestType))
		{
			result = HttpClientUtils.doPost(url, param);
		} else
		{
			result = HttpClientUtils.service(requestType, url, null, param);
		}
		log.info("微信企业(sendRequest),返回结果:{}", result);
		return JSONObject.parseObject(result);
	}

}
