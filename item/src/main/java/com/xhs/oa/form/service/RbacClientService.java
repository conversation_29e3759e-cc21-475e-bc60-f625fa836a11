package com.xhs.oa.form.service;

import com.xhs.rbac.client.common.RbacApiConstant;
import com.xhs.rbac.client.dto.meta.RbacApiCallMetaInfo;
import com.xhs.rbac.client.service.v2.IRbacClientCoreService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2021-07-06 17:13
 * @Version 1.0
 **/
@Slf4j
@Service
public class RbacClientService {
    final static public RbacApiCallMetaInfo META_INFO;
    final static private String PERMISSION_RBAC_DEBUG = "rbac-debug";


    static {
        META_INFO = new RbacApiCallMetaInfo();
        META_INFO.setAppName("xhsoa");
        META_INFO.setAppToken("xhsoa");
        META_INFO.setApiVersion(RbacApiConstant.API_VERSION_2);
    }

    @Autowired
    protected IRbacClientCoreService rbacClientCoreService;

    public List<String> getPermissionCodeList(String email) {
        if (StringUtils.isBlank(email)) {
            return Lists.newArrayList(PERMISSION_RBAC_DEBUG);
        }

        List<String> strings = rbacClientCoreService.listAllPermissionCodeByEmail(META_INFO, email);
        if (CollectionUtils.isEmpty(strings)) {
            return Lists.newArrayList(PERMISSION_RBAC_DEBUG);
        }
        strings.add(PERMISSION_RBAC_DEBUG);
        return strings;
    }
}
