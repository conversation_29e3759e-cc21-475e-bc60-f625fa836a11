package com.xhs.oa.login.service;

import com.xhs.cache.RedisClient;
import com.xhs.finance.exception.BusinessException;
import com.xhs.finance.sso.model.UserInfo;
import com.xhs.oa.form.service.RbacClientService;
import com.xhs.oa.login.enumCenter.AuthCodeEnum;
import com.xhs.oa.workflow.model.ActIdUser;
import com.xhs.oa.workflow.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@Slf4j
public class LoginService {

    @Autowired
    private UserService userService;

    @Autowired
    private RedisClient redisClient;

    @Autowired
    protected RbacClientService rbacClientService;


    private static final String token_key_ex = "token_oa_";

    private static final long tokenExpire = 3 * 60 * 60;


    //通用的token的前缀
    public final static String COMMON_TOKEN_KEY_PRE_ = "common_token_key_pre_";

    //通用的前缀
    public final static String OASIS_PRE = "oasis";

    private static final String email_audit_key_ex = "email_audit_oa_";

    /**
     *  待入职员工的token 前缀
     */
    private static final String email_red_name_apply_key_ex = "email_red_name_apply_";




    private String getTokenKey(String token){
        return token_key_ex+token;
    }

    private String getCommonTokenKey(String token){
        return COMMON_TOKEN_KEY_PRE_+token;
    }

    public void setCommonToken(UserInfo userInfo){
        log.info("---设置通用的token");
        //设置公用的token
        redisClient.set(OASIS_PRE,getCommonTokenKey(userInfo.getToken()),userInfo, tokenExpire);
    }

    public String genToken(String param){
        if(StringUtils.isBlank(param)){
            throw new BusinessException("param is empty");
        }
        try{
            String uuid = UUID.randomUUID().toString().replace("-", "");
            return Base64.getEncoder().encodeToString(uuid.getBytes("utf-8"));
        }catch (Exception e){
           log.error("genToken failed:"+param, e);
           throw new BusinessException("genToken failed:"+e.getMessage());
        }
    }


    //企业微信根据拿到的用户邮箱模拟登录
    public UserInfo simulationLogin(String userEmail){
        if(StringUtils.isBlank(userEmail) || !userEmail.contains("@")){
            throw new BusinessException("无法获取用户信息 ！");
        }
        List<ActIdUser> userList = userService.findUserInfoByEmailWithLike(userEmail.split("@")[0]+"@");
        if(CollectionUtils.isEmpty(userList)){
            throw new BusinessException("员工信息暂未同步，请稍后再试");
        }
        List<ActIdUser> validUserList = userList.stream().filter(a->a.getAccountStatus()==1).collect(Collectors.toList());
        if(validUserList.size() != 1){
            throw new BusinessException("未查询到对应的用户");
        }
        ActIdUser actIdUser = validUserList.get(0);
        UserInfo userInfo = new UserInfo();
        userInfo.setEmail(userEmail);
        userInfo.setUserId(actIdUser.getId());
        userInfo.setName(actIdUser.getFirst());
        userInfo.setToken(genToken(userEmail));
        List<String> ps = AuthCodeEnum.getAllCommonPageCodes();
        List<String> permissions = rbacClientService.getPermissionCodeList(userEmail);
        ps.addAll(permissions);
        userInfo.setPermissions(ps);
        redisClient.set(getTokenKey(userInfo.getToken()), userInfo, tokenExpire);
        //设置公用的token
        setCommonToken(userInfo);
        userInfo.setOthers(actIdUser.getRedName());
        return userInfo;

    }

    public UserInfo getAndRefreshLoginUserInfo(String token) {
        UserInfo user = (UserInfo)redisClient.get(getTokenKey(token));
        if(null != user){
            redisClient.expair(getTokenKey(token), tokenExpire);
        }else{
            user = (UserInfo)redisClient.get(OASIS_PRE,getCommonTokenKey(token));
            if(null != user){
                redisClient.expair(getCommonTokenKey(token), tokenExpire);
            }
        }
        return user;
    }

    /**
     * 待入职员工署名的申请时的用户信息
     * @param token
     * @return
     */
    public UserInfo getRedNameLoginUserInfo(String token) {
        UserInfo user = (UserInfo)redisClient.get(getRedNameApplyTokenKey(token));
        return user;
    }

    /**
     * 待入职员工署名的申请时的token
     * @param token
     * @return
     */
    public String getRedNameApplyTokenKey(String token){
        return email_red_name_apply_key_ex + token;
    }

    public UserInfo getEmailLoginUserInfo(String token) {
        UserInfo user = (UserInfo)redisClient.get(getEmailTokenKey(token));
        return user;
    }

    public String getEmailTokenKey(String token){
        return email_audit_key_ex + token;
    }

    /***
     * @return java.util.List<java.lang.String> TODO
     * <AUTHOR>
     * @Description 获取给名单，对于黑名单用户，不允许操作内部系统
     * @Date 2022-04-20 14:02:41
     **/
    public List<String> getBlackUserIdList()
    {
        Object obj = null;
        try
        {
            obj = redisClient.get("intercept", "blackList");
            log.debug("get blacklist detail " + obj);
            if (obj == null)
            {
                return new ArrayList<>();
            } else
            {
                return (List<String>) obj;
            }
        } catch (Exception e)
        {
            log.error("getBlackUserIdList failed,", e);
        }
        return new ArrayList<>();
    }
}
