package com.xhs.oa.login.enumCenter;

import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 权限code 定义
 */
@Getter
public enum AuthCodeEnum {

    //==================== 用户默认拥有的菜单权限 返回给前端 后端不做控制 =================
    OA_INDEX_PAGE("oa_index_page","系统首页", 1),
    OA_MESSAGE_NOTIFY_PAGE("oa_message_notify_page","消息通知", 1),
    OA_TODO_TASK_PAGE("oa_todo_task_page","待办任务", 1),
    OA_COMMON_FORM_PAGE("oa_common_form_page","费用报销", 1),
    OA_MY_FORM_PAGE("oa_my_form_page","我的单据", 1),
    OA_ENTRUSTED_PAGE("oa_entrusted_manage_page","任务委托", 1),
    OA_FYBX_FORM_PAGE("oa_fybx_form_page","费用报销单", 1),
    //OA_YDFQJTBX_FORM_PAGE("oa_ydfqjtbx_form_page","异地夫妻交通报销单", 1),
    OA_YDFQZZSQ_FORM_PAGE("oa_ydfqzzsq_form_page","异地夫妻资质申请单", 1),
    OA_YDFQBX_FORM_PAGE("oa_ydfqbx_form_page","异地夫妻交通报销单", 1),
    // OA_ZFBT_FORM_PAGE("oa_zfbt_form_page","租房补贴申请单", 1),
    // OA_TJFYBX_FORM_PAGE("oa_tjfybx_form_page","团建费用报销单", 1),
    OA_HTSP_FORM_PAGE("oa_htsp_form_page","合同审批单", 1),
    OA_BYJSQD_FORM_PAGE("oa_byjsqd_form_page", "备用金申请单", 1),
    OA_HKD_FORM_PAGE("oa_hkd_form_page", "还款单", 1),
    OA_JNFK_FORM_PAGE("oa_jnfk_form_page", "境内付款", 1),
    OA_JWFK_FORM_PAGE("oa_jwfk_form_page", "境外付款", 1),
    OA_JL_FORM_PAGE("oa_jl_form_page", "激励奖励类申请", 1),
    OA_ITSBCG_FORM_PAGE("oa_itsbcg_form_page", "IT 设备采购", 1),
    OA_XZFK_FORM_PAGE("oa_xzfk_form_page", "行政类费用付款", 1),
    OA_KJDSSCZ_FORM_PAGE("oa_kjdsscz_form_page", "跨境电商税充值", 1),
    OA_YZZZ_FORM_PAGE("oa_yzzz_form_page", "印章证照申请单", 1),
    // OA_ITSBLY_FORM_PAGE("oa_itsbly_form_page", "IT设备领用申请单", 1),
    OA_SKKP_FORM_PAGE("oa_skkp_form_page", "收款开票申请单", 1),
    OA_XZSBLY_FORM_PAGE("oa_xzsbly_form_page", "行政设备领用申请单", 1),
    OA_CKSBLY_FORM_PAGE("oa_cksbly_form_page", "仓库设备领用申请单", 1),
    OA_HZFP_FORM_PAGE("oa_hzfp_form_page", "红字发票申请单", 1),
    OA_FPD_FORM_PAGE("oa_fpd_form_page", "发票申请单", 1),
    OA_XZYFK_FORM_PAGE("oa_xzyfk_form_page", "行政类费用预付款", 1),
    OA_ITYFK_FORM_PAGE("oa_ityfk_form_page", "IT设备采购预付款", 1),
    OA_YBYFK_FORM_PAGE("oa_ybyfk_form_page", "一般预付款", 1),
    OA_YGQZ_FORM_PAGE("oa_ygqz_form_page", "因公签证申请单", 1),
    OA_JSQLYD_FORM_PAGE("oa_jsqlyd_form_page", "健身券领用单", 1),
    OA_MPSQD_FORM_PAGE("oa_mpsqd_form_page", "名片申请单", 1),
    OA_SXSKQ_FORM_PAGE("oa_sxskq_form_page", "实习生考勤单", 1),
    OA_SQSQD_FORM_PAGE("oa_sqsqd_form_page", "薯券申请单", 1),
    OA_PQXSQD_FORM_PAGE("oa_porchau_form_page", "porch权限申请单", 1),
    // OA_ZBSQD_FORM_PAGE("oa_zbsqd_form_page", "REDesign周边申请单", 1),
    OA_RSYZSQD_FORM_PAGE("oa_rsyzsqd_form_page", "人事印章申请单", 1),
    // OA_TJFYYFKD_FORM_PAGE("oa_tjfyyfkd_form_page","团建费用预付款单",1),
    // OA_TJFYFK_FORM_PAGE("oa_tjfyfk_form_page","团建费用付款单",1),
    OA_FCXLCG_FORM_PAGE("oa_fcxlcg_form_page","采购申请单",1),
    OA_GGHTSP_FORM_PAGE("oa_gghtsp_form_page","广告合同审批单",1),
    OA_SMTZSQD_FORM_PAGE("oa_sticker_form_page","薯名贴纸申请单",1),
    OA_DWXXFB_FORM_PAGE("oa_dwxxfb_form_page","对外宣传发布申请",1),
    OA_IPSB_FORM_PAGE("oa_ipsb_form_page","商标申请",1),
    OA_IPZL_FORM_PAGE("oa_ipzl_form_page","专利申请",1),
    OA_IPZZQ_FORM_PAGE("oa_ipzzq_form_page","著作权申请",1),
    OA_ZCASSIGN_FORM_PAGE("oa_zcassign_form_page", "IT设备领用申请单", 1),
    OA_MY_EXPENSE_PAGE("oa_my_expense_page","我的费用",1),
    OA_CL_EXPENSE_PAGE("oa_cl_form_page","差旅报销",1),
    OA_SNJT_EXPENSE_PAGE("oa_snjt_form_page","市内交通报销",1),
    OA_COMMON_EXPENSE_PAGE("oa_ybfy_form_page","一般费用报销",1),
    OA_NZFBT_FORM_PAGE("oa_nzfbt_form_page", "新租房补贴", 1),
    OA_NTJFYBX_FORM_PAGE("oa_ntjfybx_form_page", "新团建费用报销单", 1),
    OA_NTJFYFK_FORM_PAGE("oa_ntjfyfk_form_page", "新团建费用付款单", 1),
    OA_NTJFYYFK_FORM_PAGE("oa_ntjfyyfk_form_page", "新团建费用预付款单", 1),
    // OA_FLFYGL_FORM_PAGE("oa_flfygl_form_page", "福利费用管理", 1),
    OA_NZBSQD_FORM_PAGE("oa_nzbsqd_form_page", "新REDesign周边申请单", 1),
    OA_HZXXB_FORM_PAGE("oa_hzxxb_form_page", "红字信息申请表", 1),
    OA_REDREAD_FORM_PAGE("oa_redread_form_page", "新RED&READ申请单", 1),
    OA_TRAVEL_APPLY_FORM_PAGE("oa_clsqd_form_page", "差旅申请单", 1),
	OA_AJSBD_FORM_PAGE("oa_ajsbd_form_page", "案件上报申请单", 1),
	OA_XYWJSFK_FORM_PAGE("oa_xywjsfk_form_page", "新业务结算付款单", 1),
	OA_DWSJCD_FORM_PAGE("oa_dwsjcd_form_page", "对外数据传递申请", 1),
	OA_DWSJSJHQ_FORM_PAGE("oa_dwsjsjhq_form_page", "对外商家数据获取申请", 1),
    OA_PERSONDEMAND_FORM_PAGE("oa_yrxqsq_form_page", "用人需求申请单", 1),
    OA_OUTTERONSITE_FORM_PAGE("oa_wbryzc_form_page", "外包人员驻场申请单", 1),
    OA_OUTTERLEAVE_FORM_PAGE("oa_wbrylc_form_page", "外包人员离场申请单", 1),
    OA_QYWXYY_FORM_PAGE("oa_qywxyy_form_page","企业微信应用创建申请单",1),
    OA_QYWXZXH_FORM_PAGE("oa_qywxzxh_form_page","企业微信坐席号创建申请",1),

    //==================== 接口权限 =================
    CREATE_PROCESS("oa_create_process_interface", "创建流程Model接口", 3),
    QUERY_PROCESS_MODEL("oa_query_model_interface", "流程分页查询接口", 3),
    SAVE_PROCESS("oa_save_process_interface", "流程定义修改接口", 3),
    DEPLOY_PROCESS("oa_deploy_process_interface", "流程发布接口", 3),
    QUERY_PROCESS_ROLE("oa_query_process_role_interface", "流程角色分页查询接口", 3),
    ADD_PROCESS_ROLE("oa_add_process_role_interface", "新增流程角色接口", 3),
    EDIT_PROCESS_ROLE("oa_edit_process_role_interface", "修改流程角色接口", 3),
    DELETE_PROCESS_ROLE("oa_delete_process_role_interface", "删除流程角色接口", 3),
    EDIT_PROCESS_ROLE_DETAIL("oa_edit_process_role_detail_interface", "流程角色分配人员接口", 3),
    QUERY_PROCESS_ROLE_DETAIL("oa_query_process_role_detail_interface", "查询流程角色明细接口", 3),
    CREATE_USER("oa_create_user_interface", "用户创建修改接口", 3),
    SYNC_USER("oa_sync_user_interface", "同步北森用户信息接口", 3),
    SYNC_DEPARTMENT("oa_sync_department_interface", "同步北森部门信息接口", 3),
    EXPORT_USER("oa_export_user_interface", "用户信息导出接口", 3),
    EDIT_SUPPLIER_ACCOUNT("oa_edit_supplier_account_interface", "新增更新供应商账号信息接口", 3),
    EDIT_SUPPLIER_STATUS("oa_edit_supplier_status_interface", "启用禁用供应商账号接口", 3),
    QUERY_SYS_LOG("oa_sys_log_query_interface", "系统日志查询接口", 3),
    EDIT_SYS_FORM_PRIVILEGE("oa_sys_form_privilege_interface", "系统单据权限分配接口", 3),
    IMPORT_BUDGET("oa_budget_import_interface", "预算导入接口", 3),
    EXPORT_BUDGET("oa_budget_export_interface", "预算详情导出接口", 3),
    QUERY_BUDGET("oa_budget_query_interface", "预算信息查询接口", 3),
    IMPORT_COST_CATEGORY("oa_cost_category_import_interface", "预算一级费用导入接口", 3),
    ADD_GZFF_FORM("oa_gzff_form_interface", "工资发放单新增接口", 3),
    ADD_WBLWFK_FORM("oa_wblwfk_form_interface", "外包劳务付款单新增接口", 3),
    QUERY_SYS_FORM("oa_query_sys_form_interface", "系统单据查询接口", 3),
    EXPORT_SYS_FORM("oa_export_sys_form_interface", "系统单据导出接口", 3),
    ADD_DKD_FORM("oa_dkd_form_interface", "贷款单新增更新接口", 3),
    ;

    private String code;

    private String desc;
    /**
     * 1: 菜单（所有用户拥有的默认权限）  3：接口
     */
    private Integer type;

    AuthCodeEnum(String code, String desc, Integer type){
        this.code = code;
        this.desc = desc;
        this.type = type;
    }

    /**
     * 获取所有普通菜单code
     * @return
     */
    public static List<String> getAllCommonPageCodes(){
        AuthCodeEnum[] enums = AuthCodeEnum.values();
        List<String> pageCodes = new ArrayList<>();
        for(int i=0; i<enums.length; i++){
            if(enums[i].getType() == 1){
                pageCodes.add(enums[i].getCode());
            }
        }
        return pageCodes;
    }
}
