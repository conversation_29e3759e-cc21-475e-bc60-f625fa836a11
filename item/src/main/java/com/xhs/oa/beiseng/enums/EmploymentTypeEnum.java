package com.xhs.oa.beiseng.enums;

import lombok.Getter;

@Getter
public enum EmploymentTypeEnum {

    FORMAL_EMP(1,"正式员工"),
    INTERNSHIP_EMP(2,"实习员工"),
    SIDE_EMP(3,"编外人员"),
    DISPATCHED_EMP(4, "派遣员工"),
    OUTER_BPO(5, "BPO"),
    ;

    private Integer code;

    private String desc;

    EmploymentTypeEnum(Integer code,String desc){
        this.code = code;
        this.desc = desc;
    }
}
