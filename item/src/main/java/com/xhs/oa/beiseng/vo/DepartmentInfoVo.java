package com.xhs.oa.beiseng.vo;

import com.xhs.ehr.rpc.response.AuditInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 部门信息
 */
@Data
public class DepartmentInfoVo {

    public final static Long ROOT_DEPARTMENT_ID = 900106108L;

    /**
     * 待入职人员默认部门
     */
    public final static Long DEFAULT_DEPARTMENT_ID = 0L;

    public final static Long NULL_ID = -9999l;

    public final static String XHS_ROOT_DEPARTMENT_PATH = "900106108/149680/";
    public final static String XHS_ROOT_DEPARTMENT_NODEPT_PATH = "900106108/";

    @ApiModelProperty(notes = "部门id")
    private Long departmentId;

    @ApiModelProperty(notes = "部门名称")
    private String departmentName;

    @ApiModelProperty(notes = "是否删除")
    private Boolean isDeleted;

    @ApiModelProperty(notes = "状态 0：停用 1：启用")
    private Integer status;

    @ApiModelProperty(notes = "部门负责人id")
    private Long personInCharge;

    @ApiModelProperty(notes = "部门对应的hrbp")
    private Long hrbpPerson;

    @ApiModelProperty(notes = "部门对应的hrbp负责人")
    private Long hrbpLeader;

    @ApiModelProperty(notes = "对应一级部门id")
    private Long firstLevelDepartmentId;

    @ApiModelProperty(notes = "对应二级部门id")
    private Long secondLevelDepartmentId;

    @ApiModelProperty(notes = "部门Id路径")
    private String departmentIdPath;

    @ApiModelProperty(notes = "部门名称路径")
    private String departmentNamePath;

    @ApiModelProperty(notes = "上级部门id")
    private Long parentId;

    @ApiModelProperty(notes = "部门层级")
    private Integer level;

    @ApiModelProperty(notes = "是否叶子节点 1：是 0：否")
    private Integer isLeaf;

    private Date createTime;

    @ApiModelProperty(notes = "当前部门各级部门审核人列表")
    private List<AuditInfo> auditInfoList;
}
