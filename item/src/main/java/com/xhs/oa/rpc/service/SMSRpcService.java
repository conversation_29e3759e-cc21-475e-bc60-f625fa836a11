package com.xhs.oa.rpc.service;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.xhs.oa.rpc.param.SmsSendParam;
import com.xiaohongshu.erp.common.exception.BusinessException;
import com.xiaohongshu.infra.rpc.base.Context;
import com.xiaohongshu.infrastructure.sms.api.TSendSmsFlag;
import com.xiaohongshu.infrastructure.sms.api.TSmsSendReq;
import com.xiaohongshu.infrastructure.sms.api.TSmsSendRsp;
import com.xiaohongshu.infrastructure.sms.api.TSmsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Description
 * @Date 2021/10/21
 **/
@Service
@Slf4j
public class SMSRpcService {

    @Autowired
    private TSmsService.Iface smsService;


    /**
     * 发送通知类短信
     */
    public void sendTemplateSms(SmsSendParam param) {
        TSmsSendRsp response;

        log.info("短信发送:param {}", JSON.toJSONString(param));
        try {
            // 构建入参
            TSmsSendReq request = new TSmsSendReq();
            request.setPhoneNumber(param.getPhoneNumber());
            request.setTemplateId(param.getTemplateId());
            request.setParams(param.getParams());
            response = smsService.sendTemplateSms(new Context(), request);
        } catch (Exception e) {
            log.error(" sendTemplateSms 发送通知类短信异常：" + e.getMessage(), e);
            Cat.logEvent("sendTemplateSms", "发送通知类短信异常");
            throw new BusinessException("发送短信异常", e);
        }

        if (response == null || TSendSmsFlag.SUCCESS != response.getCode()) {
            log.error("sendTemplateSms failed, the response is {}", JSON.toJSONString(response));
            Cat.logEvent("sendTemplateSms", "发送通知类短信异常");
            throw new BusinessException("发送短信失败");
        }
    }

}