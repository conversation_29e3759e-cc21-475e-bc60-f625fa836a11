package com.xhs.oa.rpc.service;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.xhs.oa.office.utils.Env;
import com.xhs.oa.rpc.param.SmsSendParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description: 短信消息推送
 * @Author: yzhang14
 * @Date: 2019/3/27
 */
@Service
@Slf4j
public class SmsService {


    @Autowired
    private SMSRpcService smsRpcService;



    @ApolloJsonValue("${test_accept_mobile_list:[]}")
    private List<String> testAcceptMobileList;


    public void sendTemplateSms(SmsSendParam param){
        // 测试环境进群接收验证码
        if (Env.PROD.equals(Env.getCurrentEnv()) || testAcceptMobileList.contains(param.getPhoneNumber())) {
            smsRpcService.sendTemplateSms(param);
        }
    }

}
