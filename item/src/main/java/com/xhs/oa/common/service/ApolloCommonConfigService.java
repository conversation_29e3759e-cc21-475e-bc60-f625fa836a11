package com.xhs.oa.common.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfig;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
public class ApolloCommonConfigService {
    @ApolloConfig
    private Config config;
    /**
     * 通过key获取map类型的
     * @param key
     * @return
     */
    public Map<String,String> getMapDataByKey(String key){
        Map<String,String> valueMap = new LinkedHashMap<>();
        if(StringUtils.isBlank(key)){
            return valueMap;
        }
        try{
            String configValue = config.getProperty(key, "");
            valueMap = JSON.parseObject(configValue, LinkedHashMap.class);
        }catch (Exception e){
            log.error(" getMapDataByType 获取配置项目异常" + key,e);
        }
        return valueMap;
    }

}
