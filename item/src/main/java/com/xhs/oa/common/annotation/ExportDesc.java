package com.xhs.oa.common.annotation;

import com.xhs.oa.common.constant.CommonConstant;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface  ExportDesc {

    /**
     * 列名
     * @return
     */
    String fieldName() default "";

    /**
     * 列宽
     * @return
     */
    int width() default 30;

    /**
     * 类型
     * @return
     */
    int type() default CommonConstant.EXPORT_COMMON_FIELD;
}
