package com.xhs.oa.common.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xhs.finance.exception.BusinessException;
import com.xhs.finance.utils.AssertUtils;
import com.xhs.finance.utils.DateUtil;
import com.xhs.finance.utils.HttpClientUtils;
import com.xhs.oa.common.annotation.ExportDesc;
import com.xhs.oa.common.constant.CommonConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.xssf.usermodel.*;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Description:
 * @Author: yzhang14
 * @Date: 2019/3/28
 */
@Slf4j
@Component
public class ExportUtil {


    private static String financeUploadPath;

    @Value("${finance.upload.path}")
    public void  setFinanceUploadPath(String financeUploadPath){
        ExportUtil.financeUploadPath = financeUploadPath;
    }



    /**
      * description: 支持List中的对象嵌套List结构，并纵向扩展
      * @author: yzhang14
      * @date: 2019/3/28
     */
    public static XSSFWorkbook genLongitudinalExcel(List<?> list, Class classType, Class specificClassType){
        try{
            XSSFWorkbook xssfWorkbook = new XSSFWorkbook();
            XSSFSheet sheet = xssfWorkbook.createSheet("导出数据");
            List<String> fieldNames = new ArrayList<>();
            getLongitudinalTitles(classType.newInstance(), specificClassType==null?null:specificClassType.newInstance(), fieldNames);
            List<List<String>> rows = new ArrayList<>();
            list.forEach(a->{
                List<List<String>> subRows = new ArrayList<>();
                genLongitudinalCells(a, subRows);
                rows.addAll(subRows);
            });
            rows.add(0, fieldNames);
            XSSFCellStyle bodyStyle = genBodyStyle(xssfWorkbook);
            XSSFCellStyle headStyle = genHeadStyle(xssfWorkbook);
            for(int i=0;i<rows.size();i++){
                addRow(sheet, i, rows.get(i), headStyle, bodyStyle);
            }
            return xssfWorkbook;
        }catch (Exception e){
            log.error("生成纵向Excel异常：", e);
            throw new BusinessException("生成纵向Excel异常", e);
        }
    }

    private static List<Field> getFieldsRecursively(Object object)
    {
        List<Field> fields = new ArrayList<>();
        Class<?> aClass = object.getClass();
        while (null != aClass)
        {
            fields.addAll(0, Lists.newArrayList(aClass.getDeclaredFields()));
            aClass = aClass.getSuperclass();
        }
        return fields;
    }

    private static void getLongitudinalTitles(Object object, Object specificObject, List<String> fieldNames){
        try {
            List<Field> fields = ExportUtil.getFieldsRecursively(object);
            for(Field field: fields) {
                ExportDesc exportDesc = field.getAnnotation(ExportDesc.class);
                if (exportDesc == null) {
                    continue;
                }
                field.setAccessible(true);
                if (exportDesc.type() == CommonConstant.EXPORT_COMMON_FIELD || exportDesc.type() == CommonConstant.EXPORT_BOOLEAN_TO_NUM_FIELD) {
                    fieldNames.add(exportDesc.fieldName());
                }
                if (exportDesc.type() == CommonConstant.EXPORT_OBJECT_FIELD) {
                    getLongitudinalTitles(field.getType().newInstance(), specificObject, fieldNames);
                }
                if (exportDesc.type() == CommonConstant.EXPORT_LIST_FIELD) {
                    Type genericType = field.getGenericType();
                    if (genericType == null) {
                        throw new BusinessException("List集合未指定泛型类型");
                    }
                    // 如果是泛型参数的类型
                    if (genericType instanceof ParameterizedType) {
                        ParameterizedType pt = (ParameterizedType) genericType;
                        //得到泛型里的class类型对象
                        Class<?> genericClazz = (Class<?>) pt.getActualTypeArguments()[0];
                        getLongitudinalTitles(genericClazz.newInstance(), specificObject, fieldNames);
                    } else {
                        throw new BusinessException("List集合未指定泛型类型");
                    }
                }
                if (exportDesc.type() == CommonConstant.EXPORT_FATHER_OBJECT_FIELD) {
                    getLongitudinalTitles(specificObject, specificObject, fieldNames);
                }
                if (exportDesc.type() == CommonConstant.EXPORT_FILE) {
                    Type genericType = field.getGenericType();
                    if (genericType == null) {
                        throw new BusinessException("List集合未指定泛型类型");
                    }
                    // 如果是泛型参数的类型
                    if (genericType instanceof ParameterizedType) {
                        fieldNames.add(exportDesc.fieldName());
                    } else {
                        throw new BusinessException("List集合未指定泛型类型");
                    }
                }
            }
        }catch (Exception e){
            log.error("解析Excel title异常", e);
            throw new BusinessException("解析Excel title异常", e);
        }
    }

    private static void genLongitudinalCells(Object object, List<List<String>> rows){
        try {
            List<String> values;
            if(rows.size() == 0){
                values = new ArrayList<>();
                rows.add(values);
            }else{
                values = rows.get(0);
            }
            List<Field> fields = ExportUtil.getFieldsRecursively(object);
            for(Field field: fields){
                ExportDesc exportDesc = field.getAnnotation(ExportDesc.class);
                if(exportDesc == null){
                    continue;
                }
                field.setAccessible(true);
                if(exportDesc.type() == CommonConstant.EXPORT_COMMON_FIELD){
                    values.add(getSimpleColumn(field, object));
                }
                if(exportDesc.type()==CommonConstant.EXPORT_BOOLEAN_TO_NUM_FIELD)
                {
                    values.add(getSimpleColumn(field, object).equalsIgnoreCase("true")?"1":"0");
                }
                if(exportDesc.type()==CommonConstant.EXPORT_OBJECT_FIELD || exportDesc.type()==CommonConstant.EXPORT_FATHER_OBJECT_FIELD){
                    Object comObj = field.get(object);
                    if(comObj != null){
                        genLongitudinalCells(comObj, rows);
                    }else{
                        genLongitudinalCells(field.getType().newInstance(), rows);
                    }
                }
                if(exportDesc.type() == CommonConstant.EXPORT_LIST_FIELD || exportDesc.type() == CommonConstant.EXPORT_FILE){
                    List list = (List)field.get(object);
                    if(CollectionUtils.isEmpty(list)) {
                        Type genericType = field.getGenericType();
                        if (genericType == null) {
                            throw new BusinessException("List集合未指定泛型类型");
                        }
                        // 如果是泛型参数的类型
                        if (genericType instanceof ParameterizedType) {
                            ParameterizedType pt = (ParameterizedType) genericType;
                            //得到泛型里的class类型对象
                            Class<?> genericClazz = (Class<?>) pt.getActualTypeArguments()[0];
                            List<String> cells = new ArrayList<>();
                            getSimpleObjectCells(genericClazz.newInstance(), cells);

                            for(int i=0;i<rows.size();i++){
                                rows.get(i).addAll(cells);
                            }
                            continue;
                        }else {
                            throw new BusinessException("List集合未指定泛型类型");
                        }
                    }
                    int size = values.size();
                    int row = rows.size();
                    for(int index=0;index<list.size();index++){
                        Object o = list.get(index);
                        List<String> cells = new ArrayList<>();
                        getSimpleObjectCells(o, cells);
                        if(CollectionUtils.isEmpty(cells)){
                            continue;
                        }else {
                            //获取文件下载地址
                            if (CommonConstant.EXPORT_FILE == exportDesc.type()){
                                List<String> cellsNew = new ArrayList<>();
                                for (String file:cells) {
                                    if (StringUtils.isNotEmpty(file)){
                                        file = file.replace("https://","http://");
                                        String downLoadUrl = getDownloadUrl(file,60*60*24);
                                        cellsNew.add(downLoadUrl);
                                    }else {
                                        cellsNew.add(file);
                                    }

                                }
                                cells = cellsNew;
                            }
                        }
                        if(index <= (row-1)){
                            rows.get(index).addAll(cells);
                        }else{
                            List l = new ArrayList();
                            //补位前面的空行
                            genEmptyList(l, size);
                            l.addAll(cells);
                            rows.add(l);

                        }
                    }
                    if(rows.size() > list.size()){
                        int lastIndex = rows.get(0).size();
                        for(int i=list.size();i<rows.size();i++){
                            List<String> currentRow = rows.get(i);
                            //如果前面有List集合比当前list集合size大，则补位后面的空行
                            genEmptyList(currentRow, lastIndex-currentRow.size());
                        }
                    }
                }
            }
        }catch (Exception e){
            log.error("导出对象解析异常", e);
            throw new BusinessException("导出对象解析异常", e);
        }
    }

    private static void getSimpleObjectCells(Object o, List<String> cells) throws IllegalAccessException, InstantiationException {
        Field[] fields = o.getClass().getDeclaredFields();
        for(Field field: fields) {
            ExportDesc exportDesc = field.getAnnotation(ExportDesc.class);
            if (exportDesc == null) {
                continue;
            }
            field.setAccessible(true);
            if(exportDesc.type() == CommonConstant.EXPORT_COMMON_FIELD){
                cells.add(getSimpleColumn(field, o));
            }
            if(exportDesc.type()==CommonConstant.EXPORT_BOOLEAN_TO_NUM_FIELD)
            {
                cells.add(getSimpleColumn(field, o).equalsIgnoreCase("true")?"1":"0");
            }
            if(exportDesc.type() == CommonConstant.EXPORT_OBJECT_FIELD){
                Object comObj = field.get(o);
                if(comObj != null){
                    getSimpleObjectCells(comObj, cells);
                }else{
                    getSimpleObjectCells(field.getType().newInstance(), cells);
                }
            }
        }
    }

    private static String getSimpleColumn(Field field, Object object) throws IllegalAccessException {
        Object value = field.get(object);
        String cellValue;
        if(value != null){
            Type genericType = field.getGenericType();
            if (genericType.toString().equals(
                    "class java.util.Date")) {
                Date val = (Date) value;
                cellValue = DateUtil.formatDate(val, "yyyy-MM-dd HH:mm:ss");
            }else if ("java.util.List<java.lang.String>".equals(genericType.toString()))
            {
                List<String> val = (List<String>) value;
                cellValue = String.join("\n", val);
            } else
            {
                cellValue = value.toString();
            }
        }else {
            cellValue = "";
        }
        return cellValue;
    }

    private static void genEmptyList(List<String> list, int size){
        for(int i=0;i<size;i++){
            list.add("");
        }
    }

    private static void addRow(XSSFSheet sheet, int rowNum, List<String> list, XSSFCellStyle headStyle, XSSFCellStyle bodyStyle){
        XSSFRow row = sheet.createRow(rowNum);
        if(rowNum == 0){
            row.setHeight((short)(200*4));
        }
        for(int i=0;i<list.size();i++){
            XSSFCell cell = row.createCell(i);
            if(rowNum == 0){
                sheet.setColumnWidth(i,256*25+184);
                cell.setCellStyle(headStyle);
            }else {
                cell.setCellStyle(bodyStyle);
            }
            cell.setCellValue(list.get(i));
        }
    }

    private static XSSFCellStyle genBodyStyle(XSSFWorkbook workbook){
        XSSFCellStyle bodyStyle = workbook.createCellStyle();
        bodyStyle.setWrapText(true);
        bodyStyle.setAlignment(HSSFCellStyle.ALIGN_LEFT);
        bodyStyle.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
        return bodyStyle;
    }

    public static XSSFCellStyle genHeadStyle(XSSFWorkbook workbook){
        XSSFCellStyle headStyle = workbook.createCellStyle();
        headStyle.setWrapText(true);
        headStyle.setAlignment(HSSFCellStyle.ALIGN_LEFT);
        headStyle.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
        headStyle.setFillPattern(HSSFCellStyle.FINE_DOTS );
        headStyle.setFillForegroundColor(new HSSFColor.PALE_BLUE().getIndex());
        headStyle.setBorderBottom(BorderStyle.THIN);// 底部边框线样式(细实线)
        headStyle.setBottomBorderColor(new HSSFColor.BLACK().getIndex());// 底部边框线颜色
        headStyle.setBorderLeft(BorderStyle.THIN);// 左边框线样式(细实线)
        headStyle.setLeftBorderColor(new HSSFColor.BLACK().getIndex());// 左边框线颜色
        headStyle.setBorderRight(BorderStyle.THIN);// 右边框线样式(细实线)
        headStyle.setRightBorderColor(new HSSFColor.BLACK().getIndex());// 右边框线颜色
        headStyle.setBorderTop(BorderStyle.THIN);// 顶部边框线样式(细实线)
        headStyle.setTopBorderColor(new HSSFColor.BLACK().getIndex());// 顶部边框线颜色
        return headStyle;
    }


    public static String getDownloadUrl(String originUrl, long expairSecond) {
        AssertUtils.notNull(originUrl, "文件地址不能为空", new Object[0]);
        String json = HttpClientUtils.doGet(financeUploadPath + "/upload/getDownloadUrl" + "?originUrl=" + encode(originUrl) + "&expairSecond=" + expairSecond);
        JSONObject jsonObj = JSON.parseObject(json);
        if (200 != jsonObj.getInteger("statusCode")) {
            String errMsg = jsonObj.getString("alertMsg");
            throw new BusinessException(StringUtils.isEmpty(errMsg) ? "获取下载连接失败" : errMsg);
        } else {
            return jsonObj.getString("data");
        }
    }

    private static String encode(String url) {
        try {
            return URLEncoder.encode(url, "UTF-8");
        } catch (UnsupportedEncodingException var3) {
            throw new BusinessException(var3.getMessage(), var3);
        }
    }
}
