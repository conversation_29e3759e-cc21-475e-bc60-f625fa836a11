package com.xhs.oa.common.service;

import com.xhs.finance.exception.BusinessException;
import com.xhs.finance.upload.model.UploadFile;
import com.xhs.oa.common.util.ExportUtil;
import com.xhs.oa.upload.service.OAUploadService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.List;

@Slf4j
@Service
public class ExportService {

    @Autowired
    private OAUploadService uploadService;


    /**
     * 无抽象类纵向导出
     * @param list 数据
     * @param exportFileName 导出的文件名
     * @param classType list集合中的数据类型
     * @return
     */
    public String export(List<?> list, String exportFileName, Class classType){
        try{
            XSSFWorkbook xssfWorkbook = ExportUtil.genLongitudinalExcel(list, classType, null);
            ByteArrayOutputStream byteOut = new ByteArrayOutputStream(8192);
            xssfWorkbook.write(byteOut);
            UploadFile uf = uploadService.upload(new ByteArrayInputStream(byteOut.toByteArray()), exportFileName);
            return uf.getDownloadUrl();
        }catch (Exception e){
            log.error("导出异常", e);
            throw new BusinessException("导出异常", e);
        }
    }




}
