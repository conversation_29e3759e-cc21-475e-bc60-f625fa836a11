package com.xhs.oa.common.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFDateUtil;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.Date;

@Slf4j
public class ExcelUtil {

    public static final String EXCEL_SUFFIX_XLSX = ".xlsx";

    public static final String EXCEL_SUFFIX_XLS = ".xls";

    public static Workbook transforWorkbook(InputStream in) {
        BufferedInputStream bufIn = new BufferedInputStream(in);
        Workbook wb = null;
        try {
            wb = new XSSFWorkbook(bufIn);
        } catch (IOException e) {
            return null;
        } finally {
            if (null != bufIn) {
                try {
                    bufIn.close();
                } catch (IOException e) {

                }
            }
        }
        return wb;
    }

    /**
     * description: 判断一行是否为空行
     * @author: qiaodeng
     * @date: 2018/12/18
     */
    public static boolean excelIsBankStringRow(Row row){
        if(row == null){
            return true;
        }
        for (int c = row.getFirstCellNum(); c < row.getLastCellNum(); c++) {
            Cell cell = row.getCell(c);
            if (cell != null &&cell.getCellType() != Cell.CELL_TYPE_BLANK && StringUtils.isNotBlank(getStringValue(cell)) ){
                return false;
            }
        }
        return true;
    }

    public static boolean excelIsBankStringRow(Row row, int ignoreCellIndex)
    {
        if (row == null)
        {
            return true;
        }
        for (int c = row.getFirstCellNum(); c < row.getLastCellNum(); c++)
        {
            if (c == ignoreCellIndex)
            {
                continue;
            }
            Cell cell = row.getCell(c);
            if (cell != null && cell.getCellType() != Cell.CELL_TYPE_BLANK && StringUtils.isNotBlank(getStringValue(cell)))
            {
                return false;
            }
        }
        return true;
    }


    public static String getStringValue(Cell cell){
        if(cell == null){
            return null;
        }

        int cellType = cell.getCellType();

        String value = null;
        switch (cellType) {
            case Cell.CELL_TYPE_NUMERIC:
                if(HSSFDateUtil.isCellDateFormatted(cell)){
                    //用于转化为日期格式
                    Date d = cell.getDateCellValue();
                    DateFormat formater = new SimpleDateFormat("yyyy-MM-dd");
                    value = formater.format(d);
                }else{
                    value = new Double(cell.getNumericCellValue()).longValue()+"";
                }
                break;
            case Cell.CELL_TYPE_STRING:
                value = cell.getStringCellValue().trim();
                break;
            case Cell.CELL_TYPE_BLANK:
                value = "";
                break;
            default:
                value = String.valueOf(cell.getStringCellValue());

        }
        return value;
    }

    public static String dealWithDoubleStr(String source){
        try {
            DecimalFormat format = new DecimalFormat("0.##");
            String newSource = format.format(Double.valueOf(source));
            return newSource;
        }catch (NumberFormatException e){
            log.error("单元格转换异常:"+source,e);
            return source;
        }
    }

    public static String getStringValueWithPrecision(Cell cell){
        if(cell == null){
            return null;
        }

        int cellType = cell.getCellType();

        String value = null;
        switch (cellType) {
            case Cell.CELL_TYPE_NUMERIC:
                cell.setCellType(Cell.CELL_TYPE_STRING);
                value = cell.getStringCellValue();
                break;
            case Cell.CELL_TYPE_STRING:
                value = cell.getStringCellValue().trim();
                break;
            case Cell.CELL_TYPE_BLANK:
                value = "";
                break;
            case Cell.CELL_TYPE_FORMULA:
                int cachedFormulaResultType = cell.getCachedFormulaResultType();
                if (Cell.CELL_TYPE_NUMERIC == cachedFormulaResultType)
                {
                    value = cell.getNumericCellValue() + "";
                } else if (Cell.CELL_TYPE_STRING == cachedFormulaResultType)
                {
                    value = cell.getRichStringCellValue() + "";
                }
                break;
            default:
                value = String.valueOf(cell.getStringCellValue());

        }
        return value;
    }

}
