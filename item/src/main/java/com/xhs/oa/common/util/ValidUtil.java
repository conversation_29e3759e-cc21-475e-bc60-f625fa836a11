package com.xhs.oa.common.util;

import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.constraints.NotNull;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.Set;

public class ValidUtil {

    public static String validObject(Object obj){
        if(obj == null){
            return null;
        }
        Set<ConstraintViolation<Object>> validResult = Validation.buildDefaultValidatorFactory().getValidator().validate(obj);
        StringBuilder sb = new StringBuilder();
        if (null != validResult && validResult.size() > 0) {
            Iterator<ConstraintViolation<Object>> iterator = validResult.iterator();
            while (iterator.hasNext()) {
                ConstraintViolation<Object> constraintViolation = iterator.next();
                sb.append(constraintViolation.getMessage()).append("、");
            }
            if(sb.lastIndexOf("、") == sb.length()-1){
                sb.delete(sb.length()-1, sb.length());
            }
        }
        String result = sb.toString();
        if(StringUtils.isNotBlank(result)){
            return result;
        }

        return null;
    }

    public static String validObjectByGroup(Object obj, Class<?>... groups){
        if(obj == null){
            return null;
        }
        Set<ConstraintViolation<Object>> validResult = Validation.buildDefaultValidatorFactory().getValidator().validate(obj, groups);
        StringBuilder sb = new StringBuilder();
        if (null != validResult && validResult.size() > 0) {
            Iterator<ConstraintViolation<Object>> iterator = validResult.iterator();
            while (iterator.hasNext()) {
                ConstraintViolation<Object> constraintViolation = iterator.next();
                sb.append(constraintViolation.getMessage()).append("、");
            }
            if(sb.lastIndexOf("、") == sb.length()-1){
                sb.delete(sb.length()-1, sb.length());
            }
        }
        String result = sb.toString();
        if(StringUtils.isNotBlank(result)){
            return result;
        }

        return null;
    }

    /**
     * 获取校验未通过的字段名称
     *
     * @param obj    校验对象
     * @param groups validate groups
     * @return ConstraintViolation#getPropertyPath
     */
    public static List<String> getValidFailedProperties(Object obj, Class<?>... groups)
    {
        if (obj == null)
        {
            return Lists.newArrayList();
        }

        List<String> properties = Lists.newArrayList();
        Set<ConstraintViolation<Object>> validResult = Validation.buildDefaultValidatorFactory().getValidator()
                .validate(obj, groups);
        if (CollectionUtils.isNotEmpty(validResult))
        {
            for (ConstraintViolation<Object> constraintViolation : validResult)
            {
                properties.add(constraintViolation.getPropertyPath().toString());
            }
        }
        return properties;

    }


    /**
     * 校验单据
     */
    public static String validForm(Object object, Class<?> group){
        if(object == null){
            return null;
        }
        Set<ConstraintViolation<Object>> validResult = Validation.buildDefaultValidatorFactory().getValidator().validate(object);
        if(CollectionUtils.isEmpty(validResult)){
            if(group != null){
                validResult = Validation.buildDefaultValidatorFactory().getValidator().validate(object, group);
            }
        }
        StringBuilder sb = new StringBuilder();
        if (null != validResult && validResult.size() > 0) {
            Iterator<ConstraintViolation<Object>> iterator = validResult.iterator();
            while (iterator.hasNext()) {
                ConstraintViolation<Object> constraintViolation = (ConstraintViolation<Object>) iterator.next();
                sb.append(constraintViolation.getMessage()).append("、");
            }
            if(sb.lastIndexOf("、") == sb.length()-1){
                sb.delete(sb.length()-1, sb.length());
            }
        }
        String result = sb.toString();
        if(StringUtils.isNotBlank(result)){
            return result;
        }

        return null;
    }

    public static boolean needFieldValid(Field field, Class<?> group)
    {
        if (!field.isAccessible())
        {
            field.setAccessible(true);
        }

        NotNull notNull = field.getAnnotation(NotNull.class);
        if (notNull != null && ArrayUtils.isNotEmpty(notNull.groups()) && Arrays.asList(notNull.groups()).contains(group))
        {
            return true;
        }

        NotBlank notBlank = field.getAnnotation(NotBlank.class);
        return notBlank != null && ArrayUtils.isNotEmpty(notBlank.groups()) && Arrays.asList(notBlank.groups()).contains(group);

    }

}
