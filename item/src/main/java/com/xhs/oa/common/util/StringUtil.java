package com.xhs.oa.common.util;

import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import java.text.DecimalFormat;
import java.util.*;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2019/11/5
 **/
@Slf4j
public class StringUtil
{

	public static final Splitter COMMA_SPLITTER = Splitter.on(",").trimResults();

	public static final Splitter LINE_SPLITTER = Splitter.on("-").trimResults();

	private static final String NAME_PATTEN = "%s(%s)";

	@SuppressWarnings("UnstableApiUsage")
	public static final Splitter.MapSplitter MAP_SPLITTER = Splitter.on(",").trimResults().withKeyValueSeparator(":");

	public static final Joiner.MapJoiner MAP_JOINER = Joiner.on(",").withKeyValueSeparator(":");

	public static final Joiner COMMA_JOINER = Joiner.on(",").skipNulls();

	public static final Joiner UNDER_LINE_JOINER = Joiner.on("_").skipNulls();

	public static final Joiner LINE_JOINER = Joiner.on("-").skipNulls();

	public static final Joiner CN_JOINER = Joiner.on("、").skipNulls();

	public static final Joiner SHOW_DATE_JOINER = Joiner.on("~").skipNulls();

	public static final Joiner SEMICOLON_JOINER = Joiner.on("；").skipNulls();

	private static final DateTimeFormatter YEAR_MONTH_FORMATTER = DateTimeFormat.forPattern("yyyy-MM");

	private static final String ENG_LEFT_PARENTHESES = "\\(";
	private static final String ENG_RIGHT_PARENTHESES = "\\)";
	private static final String CHI_LEFT_PARENTHESES = "（";
	private static final String CHI_RIGHT_PARENTHESES = "）";

	private static final Pattern pattern = Pattern.compile("[()（）]+");

	public static String getYear(String dateStr)
	{
		try
		{
			DateTime parse = DateTime.parse(dateStr, YEAR_MONTH_FORMATTER);
			if (parse == null)
			{
				return "";
			}
			return String.valueOf(parse.getYear());
		} catch (Exception e)
		{
			log.error("getYear parse error, msg ", e);
			return "";
		}
	}

	public static Date getDate(String dateStr) {
		try
		{
			DateTime parse = DateTime.parse(dateStr, YEAR_MONTH_FORMATTER);
			if (parse == null)
			{
				return null;
			}
			return parse.toDate();
		} catch (Exception e)
		{
			log.error("getYear parse error, msg ", e);
			return null;
		}
	}

	public static String getUserName(String redName, String realName) {
		if (StringUtils.isEmpty(redName)){
			return realName;
		}
		return String.format(NAME_PATTEN,redName,realName);
	}

	public String getMonthFirstDay()
	{
		Calendar cal = Calendar.getInstance();
		int month = cal.get(Calendar.MONTH) + 1;
		int year = cal.get(Calendar.YEAR);

		return year + "-" + month + "-" + " 00:00:00";
	}

	public String getNextFirstDay()
	{
		Calendar cal = Calendar.getInstance();
		int month = cal.get(Calendar.MONTH) + 1;
		int year = cal.get(Calendar.YEAR);
		int day = cal.get(Calendar.DAY_OF_MONTH);

		return year + "-" + month + "-" + day + "01 00:00:00";
	}

	public static List<String> parseStringToList(String targetString){
		try{
			String[] splitArray = targetString.split(",");
			return Arrays.asList(splitArray);
		}catch (Exception e){
			log.error("解析单据类型字符串异常",e);
		}
		return new ArrayList<>();
	}

	/**
	 * 将字符串数字转成千分位显示。
	 */
	public static String comdify(String value) {
		DecimalFormat df = null;
		if (value.indexOf(".") > 0) {
			int i = value.length() - value.indexOf(".") - 1;
			switch (i) {
				case 0:
					df = new DecimalFormat("###,##0");
					break;
				case 1:
					df = new DecimalFormat("###,##0.0");
					break;
				case 2:
					df = new DecimalFormat("###,##0.00");
					break;
				case 3:
					df = new DecimalFormat("###,##0.000");
					break;
				case 4:
					df = new DecimalFormat("###,##0.0000");
					break;
				default:
					df = new DecimalFormat("###,##0.00000");
					break;
			}
		} else {
			df = new DecimalFormat("###,##0");
		}
		double number = 0.0;
		try {
			number = Double.parseDouble(value);
		} catch (Exception e) {
			number = 0.0;
		}
		return df.format(number);
	}

	/**
	 * 删除中英文小括号
	 */
	public static String removeParentheses(String targetString)
	{
		return StringUtils.replaceAll(targetString, ENG_LEFT_PARENTHESES, "").replaceAll(ENG_RIGHT_PARENTHESES, "")
				.replaceAll(CHI_LEFT_PARENTHESES, "").replaceAll(CHI_RIGHT_PARENTHESES, "");
	}

	/**
	 * 字符串转换unicode
	 * @param string
	 * @return
	 */
	public static String string2Unicode(String string) {
		StringBuffer unicode = new StringBuffer();
		for (int i = 0; i < string.length(); i++) {
			// 取出每一个字符
			char c = string.charAt(i);
			// 转换为unicode
			unicode.append("\\u" + Integer.toHexString(c));
		}

		return unicode.toString();
	}


	/**
	 * 	为null返回默认值
	 * @param value
	 * @param defaultValue
	 * @return
	 */
	public static String getDefaultValue(String value,String defaultValue) {
		return value == null ? defaultValue : value;
	}

	public static String removeDeptPathFirst(String dept) {
		List<String> idOrNameList = new ArrayList<>();
		String[] split = dept.split(",");
		for (int i = 1;i<= split.length-1;i++){
			idOrNameList.add(split[i]);
		}
		return String.join(",", idOrNameList);
	}

	public static String getHalfSymbol(String str) {
		if (Objects.isNull(str)){
			return "";
		}
		if (StringUtils.isEmpty(str)){
			return str;
		}
		if(str.contains("【")){
			str = str.replaceAll("【","[");
		}
		if(str.contains("】")){
			str = str.replaceAll("】","]");
		}
		if(str.contains("、")){
			str = str.replaceAll("、","\\\\");
		}
		if(str.contains("—")){
			str = str.replaceAll("—","-");
		}
		if(str.contains("，")){
			str = str.replaceAll("，",",");
		}
		if(str.contains("：")){
			str = str.replaceAll("：",":");
		}
		if(str.contains("（")){
			str = str.replaceAll("（","(");
		}
		if(str.contains("）")){
			str = str.replaceAll("）",")");
		}
		if(str.contains("。")){
			str = str.replaceAll("。",".");
		}
		if(str.contains("‘")){
			str = str.replaceAll("‘","'");
		}
		if(str.contains("’")){
			str = str.replaceAll("’","'");
		}
		if(str.contains("「")){
			str = str.replaceAll("「","{");
		}
		if(str.contains("」")){
			str = str.replaceAll("」","}");
		}
		if(str.contains("？")){
			str = str.replaceAll("？","?");
		}
		if(str.contains("……")){
			str = str.replaceAll("……","...");
		}
		return str;
	}


	/**
	 * 去掉中英文括号比较是否相等
	 */
	public static boolean ignoreCommaEq(String strA, String strB) {
		if (pattern.matcher(strA).find() && pattern.matcher(strB).find()){
			String our = strA.replaceAll("[()（）]+", "");
			String payment = strB.replaceAll("[()（）]+", "");
			return our.equals(payment);
		}
		return strA.equals(strB);
	}

}
