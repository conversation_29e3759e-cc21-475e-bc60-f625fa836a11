package com.xhs.oa.common.constant;


public class CommonConstant {


    public final static String TASK_EMAIL_AUDIT_SECRET_KEY = "task_email_audit_secret_key_pas";

    /**
     * 邮件加密token标记
     */
    public final static String ENCRYPT_EMAIL_TOKEN = "encrypt_email_token";

    //防重复提交时间
    public final static int SUBMIT_SPACE_TIME = 1;

    /**
     * 导出字段类型-普通字段
     */
    public static final int EXPORT_COMMON_FIELD = 0;

    /**
     * 导出字段类型-复杂对象
     */
    public static final int EXPORT_OBJECT_FIELD = 1;

    /**
     * 导出字段类型-LIST集合
     */
    public static final int EXPORT_LIST_FIELD = 2;

    /**
     * 导出字段类型-父对象
     */
    public static final int EXPORT_FATHER_OBJECT_FIELD = 3;

    /**
     * bollean字段转为1/0
     */
    public static final int EXPORT_BOOLEAN_TO_NUM_FIELD = 5;

    /**
     * 附件导出 取list name 为title
     */
    public static final int EXPORT_FILE = 6;


    public final static String USER_INFO_SECRET_KEY = "user_info_secret_key_pas";


    public final static String ENTERPRISE_WECHAT_USERINFO_URL = "https://qyapi.weixin.qq.com/cgi-bin/user/getuserinfo?";

    public final static String ENTERPRISE_WECHAT_TOKEN_URL_PRE = "https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=wx2a4a6c713327df27&corpsecret=";

    public static final String METHOD_GET = "GET";
    public static final String METHOD_POST = "POST";


    // 敏感字段表#reddw@dw_user_day 角色code
    @Deprecated  // @see apollo配置
    public final static String RBAC_VENUS_REDDW_DW_USER_DAY_ROLE_CODE = "730";
    // 敏感字段表#reddw@dw_user_phone_passport_day 角色code(2021-01-06新增 by 席恒昌)
    @Deprecated  // @see apollo配置
    public final static String RBAC_VENUS_REDDW_DW_USER_PHONE_PASSPORT_DAY_ROLE_CODE = "1729";


}
