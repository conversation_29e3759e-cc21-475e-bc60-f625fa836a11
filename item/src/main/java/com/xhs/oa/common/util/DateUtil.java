package com.xhs.oa.common.util;

import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.xhs.finance.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.joda.time.DateTime;
import org.joda.time.Interval;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
public class DateUtil
{

	public static final String FORMATTER_STR_M = "yyyy-MM";

	public static final String FORMATTER_STR_D = "yyyy-MM-dd";

	public static final String CONTRACT_PRE_PATTERN = "yyyyMM";

	public static final String SHORT_PATTERN = "yyyy-MM-dd";

	public static final String MAIL_PATTERN = "yyyy/MM/dd";

	public static final String VIEW_PATTERN = "yyyy.MM.dd";

	public static final String NO_SIGN_PATTEN = "yyyyMMdd";

	public static final String LONG_PATTERN = "yyyy-MM-dd HH:mm:ss.SSS";

	public static final String YEAR_TO_MINUTE = "yyyy-MM-dd HH:mm";

	public static final String YEAR_TO_SECOND = "yyyy-MM-dd HH:mm:ss";

	public static final String FALL_PATTEN = "dd/MM/yyyy";


	//一天的毫秒值
	public static final long MILLISECOND_OF_DAY = 86400000L;

	public static final DateTimeFormatter SHOW_FORMATTER = DateTimeFormat.forPattern("yyyy.MM.dd");

	public static final DateTimeFormatter FORMATTER = DateTimeFormat.forPattern(SHORT_PATTERN);

	public static final DateTimeFormatter SIMPLE_FORMATTER = DateTimeFormat.forPattern("yyyyMMdd");

	public static final DateTimeFormatter MAIL_FORMATTER = DateTimeFormat.forPattern(MAIL_PATTERN);

	public static final DateTimeFormatter LONG_FORMATTER = DateTimeFormat.forPattern(LONG_PATTERN);

	public static final DateTimeFormatter SECOND_FORMATTER = DateTimeFormat.forPattern(YEAR_TO_SECOND);

	private static final Splitter DATE_SPLITTER = Splitter.on("~").trimResults();

	private static final long TICKS_AT_EPOCH_NT = 116444736000000000L;
	private static final long TICKS_PER_MILLISECOND = 10000;

	private static final Map<String, SimpleDateFormat> SIMPLE_DATE_FORMAT_MAP;

	static {
		SIMPLE_DATE_FORMAT_MAP = new HashMap<>();
		SIMPLE_DATE_FORMAT_MAP.put(SHORT_PATTERN, new SimpleDateFormat(SHORT_PATTERN));
		SIMPLE_DATE_FORMAT_MAP.put(YEAR_TO_SECOND, new SimpleDateFormat(YEAR_TO_SECOND));
		SIMPLE_DATE_FORMAT_MAP.put(MAIL_PATTERN, new SimpleDateFormat(MAIL_PATTERN));
	}

	public static Optional<Date> parseDateString(String dateStr, DateTimeFormatter formatter)
	{
		try
		{
			DateTime parse = DateTime.parse(dateStr, formatter);
			if (parse != null)
			{
				return Optional.of(parse.toDate());
			}
		} catch (Exception e)
		{
			log.error("parseDateString error , msg ", e);
		}
		return Optional.empty();
	}

	public static String dateToStrWithPatten(Date dateDate,String patten) {
		try{
			SimpleDateFormat formatter = new SimpleDateFormat(patten);
			return formatter.format(dateDate);
		}catch (Exception e){
			return null;
		}
	}

	/***
	 * 获取二个时间点的日期
	 */
	public static List<String> findDates(String beginTime, String endTime) {
		try {
			List<String> allDate = new ArrayList();
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
			Date dBegin = sdf.parse(beginTime);
			Date dEnd = sdf.parse(endTime);
			allDate.add(sdf.format(dBegin));
			Calendar calBegin = Calendar.getInstance();
			// 使用给定的 Date 设置此 Calendar 的时间
			calBegin.setTime(dBegin);
			Calendar calEnd = Calendar.getInstance();
			// 使用给定的 Date 设置此 Calendar 的时间
			calEnd.setTime(dEnd);
			// 测试此日期是否在指定日期之后
			while (dEnd.after(calBegin.getTime())) {
				// 根据日历的规则，为给定的日历字段添加或减去指定的时间量
				calBegin.add(Calendar.DAY_OF_MONTH, 1);
				allDate.add(sdf.format(calBegin.getTime()));
			}
			return allDate;
		} catch (Exception e) {
			log.error("获取时间天数异常{}", e.getMessage());
		}
		return new ArrayList<>();
	}
	/**
	 * 获得本周的第一天
	 */
	public static Date startOfWeek(Date date)
	{
		SimpleDateFormat shortSdf = new SimpleDateFormat(SHORT_PATTERN);
		SimpleDateFormat longSdf = new SimpleDateFormat(LONG_PATTERN);
		Calendar c = Calendar.getInstance();
		c.setTime(date);
		try
		{
			int dayWeek = c.get(Calendar.DAY_OF_WEEK);// 获得当前日期是一个星期的第几天
			if (1 == dayWeek)
			{
				c.add(Calendar.DAY_OF_WEEK, -1);
			}
			c.set(Calendar.DAY_OF_WEEK, 2);
			c.setTime(longSdf.parse(shortSdf.format(c.getTime()) + " 00:00:00.000"));
		} catch (Exception e)
		{
			log.error("获得本周的第一天异常", e);
			throw new BusinessException("获得本周的第一天失败");
		}
		return c.getTime();
	}

	/**
	 * 用于计算1900-0-1之后的day天日期是哪天
	 * 举例：1900-0-1之后的44326天日期是2021/5/10
	 * @return
	 */
	public static String dayToDate(int day){
		Calendar calendar = new GregorianCalendar(1900,0,-1);
		Date d = calendar.getTime();

		Date date= DateUtils.addDays(d,day);

		SimpleDateFormat simpleDateFormat=new SimpleDateFormat(SHORT_PATTERN);

		return simpleDateFormat.format(date);
	}

	/***
	 * 日期增加天数
	 */
	public static String addDays(String dateStr, int days) {
		String endTime = "";
		try {
			Date startTime = null;
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
			Date date = sdf.parse(dateStr);
			Calendar rightNow = Calendar.getInstance();
			rightNow.setTime(date);
			rightNow.add(Calendar.DAY_OF_YEAR, days);
			endTime = sdf.format(rightNow.getTime());
		} catch (Exception e) {
			log.error("addDays error", e);
		}
		return endTime;
	}

	public static String formatDate(Date date) {
		return new SimpleDateFormat(SHORT_PATTERN).format(date);
	}

	public static String formatSecondDate(Date date) {
		return new SimpleDateFormat(YEAR_TO_SECOND).format(date);
	}

	public static String formatMailDate(Date date) {
		return new SimpleDateFormat(MAIL_PATTERN).format(date);
	}

	public static String formatString(String dateStr) {
		return Optional.ofNullable(dateStr).filter(StringUtils::isNoneBlank)
				.map(item -> parseDateString(item, SECOND_FORMATTER).orElse(null))
				.map(DateUtil::formatMailDate).orElse("");

	}


	public static String formatDate(Date date, String pattern)
	{
		return new SimpleDateFormat(pattern).format(date);
	}

	public static Date parseDate(String date, String pattern) {
		try {
			return SIMPLE_DATE_FORMAT_MAP.containsKey(pattern)
					? SIMPLE_DATE_FORMAT_MAP.get(pattern).parse(date)
					: new SimpleDateFormat(pattern).parse(date);
		} catch (ParseException e) {
			return null;
		}
	}

	/**
	 * 获得本周的最后一天
	 */
	public static Date endOfWeek(Date date)
	{
		SimpleDateFormat shortSdf = new SimpleDateFormat(SHORT_PATTERN);
		SimpleDateFormat longSdf = new SimpleDateFormat(LONG_PATTERN);
		Calendar c = Calendar.getInstance();
		c.setTime(date);
		try
		{
			int dayWeek = c.get(Calendar.DAY_OF_WEEK);// 获得当前日期是一个星期的第几天
			if (1 == dayWeek)
			{
				c.add(Calendar.DAY_OF_WEEK, -1);
			}
			c.set(Calendar.DAY_OF_WEEK, 2);
			c.set(Calendar.DATE, c.get(Calendar.DATE) + 6);
			c.setTime(longSdf.parse(shortSdf.format(c.getTime()) + " 23:59:59.999"));
		} catch (Exception e)
		{
			log.error("获得本周的最后一天异常", e);
			throw new BusinessException("获得本周的最后一天失败");
		}
		return c.getTime();
	}

	/**
	 * 获取下一个周
	 *
	 * @param date
	 * @return
	 */
	public static Date getNextWeekDate(Date date) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		cal.set(Calendar.DAY_OF_MONTH, 7);
		return cal.getTime();
	}

	/**
	 * 获取下一个月
	 *
	 * @param date
	 * @return
	 */
	public static Date getNextMonthDate(Date date) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		cal.add(Calendar.MONTH, 1);
		return cal.getTime();
	}

	/**
	 * 获取上一个月
	 *
	 * @param date
	 * @return
	 */
	public static Date getLastMonthDate(Date date){
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		cal.add(Calendar.MONTH, -1);
		return cal.getTime();
	}

	public static Date getFirstDateOfMonth(Date date) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		cal.set(Calendar.DAY_OF_MONTH, cal.getActualMinimum(Calendar.DAY_OF_MONTH));
		return cal.getTime();
	}

	public static Date getLastDateOfMonth(Date date) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH));
		return cal.getTime();
	}

	/**
	 * 获取两个月
	 *
	 * @param date
	 * @return
	 */
	public static Date getTwoMonthDate(Date date) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		cal.add(Calendar.MONTH, 2);
		return cal.getTime();
	}

	/**
	 * 获取三个月
	 *
	 * @param date
	 * @return
	 */
	public static Date getThreeMonthDate(Date date) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		cal.add(Calendar.MONTH, 3);
		return cal.getTime();
	}

	/**
	 * 获得本周的第一天，周日为第一天
	 */
	public static Date startOfWeekFromSunday(Date date)
	{
		SimpleDateFormat shortSdf = new SimpleDateFormat(SHORT_PATTERN);
		SimpleDateFormat longSdf = new SimpleDateFormat(LONG_PATTERN);
		Calendar c = Calendar.getInstance();
		c.setTime(date);
		try
		{
			c.set(Calendar.DAY_OF_WEEK, 1);
			c.setTime(longSdf.parse(shortSdf.format(c.getTime()) + " 00:00:00.000"));
		} catch (Exception e)
		{
			log.error("获得本周的第一天异常", e);
			throw new BusinessException("获得本周的第一天失败");
		}
		return c.getTime();
	}

	/**
	 * 获得本周的最后一天，周日为第一天
	 */
	public static Date endOfWeekFromSunday(Date date)
	{
		SimpleDateFormat shortSdf = new SimpleDateFormat(SHORT_PATTERN);
		SimpleDateFormat longSdf = new SimpleDateFormat(LONG_PATTERN);
		Calendar c = Calendar.getInstance();
		c.setTime(date);
		try
		{
			c.set(Calendar.DAY_OF_WEEK, 1);
			c.set(Calendar.DATE, c.get(Calendar.DATE) + 6);
			c.setTime(longSdf.parse(shortSdf.format(c.getTime()) + " 23:59:59.999"));
		} catch (Exception e)
		{
			log.error("获得本周的最后一天异常", e);
			throw new BusinessException("获得本周的最后一天失败");
		}
		return c.getTime();
	}

	/**
	 * 半年前的今天
	 */
	public static Date halfYearAgo(Date date)
	{
		Calendar c = Calendar.getInstance();
		c.setTime(date);
		c.set(Calendar.MONTH, c.get(Calendar.MONTH) - 6);
		return c.getTime();
	}

	/**
	 * 获取前/后半年的开始时间
	 */
	public static Date halfYearStartTime(Date date)
	{
		SimpleDateFormat shortSdf = new SimpleDateFormat(SHORT_PATTERN);
		SimpleDateFormat longSdf = new SimpleDateFormat(LONG_PATTERN);
		Calendar c = Calendar.getInstance();
		c.setTime(date);
		int currentMonth = c.get(Calendar.MONTH) + 1;
		try
		{
			if (currentMonth <= 6)
			{
				c.set(Calendar.MONTH, 0);
			} else if (currentMonth <= 12)
			{
				c.set(Calendar.MONTH, 6);
			}
			c.set(Calendar.DATE, 1);
			return longSdf.parse(shortSdf.format(c.getTime()) + " 00:00:00.000");
		} catch (Exception e)
		{
			log.error("获取前/后半年的开始时间异常", e);
			throw new BusinessException("获取前/后半年的开始时间失败");
		}
	}

	/**
	 * 获取前/后半年的结束时间
	 */
	public static Date halfYearEndTime(Date date)
	{
		SimpleDateFormat shortSdf = new SimpleDateFormat(SHORT_PATTERN);
		SimpleDateFormat longSdf = new SimpleDateFormat(LONG_PATTERN);
		Calendar c = Calendar.getInstance();
		c.setTime(date);
		int currentMonth = c.get(Calendar.MONTH) + 1;
		try
		{
			if (currentMonth <= 6)
			{
				c.set(Calendar.MONTH, 5);
				c.set(Calendar.DATE, 30);
			} else if (currentMonth <= 12)
			{
				c.set(Calendar.MONTH, 11);
				c.set(Calendar.DATE, 31);
			}
			return longSdf.parse(shortSdf.format(c.getTime()) + " 23:59:59.999");
		} catch (Exception e)
		{
			log.error("获取前/后半年的结束时间异常", e);
			throw new BusinessException("获取前/后半年的结束时间失败");
		}
	}

	public static boolean isToday(Date date)
	{
		SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
		String now = dateFormat.format(new Date());
		String dest = dateFormat.format(date);
		return now.equalsIgnoreCase(dest);
	}

	public static boolean isPast(String date) {
		if (StringUtils.isBlank(date)) {
			return Boolean.FALSE;
		}
		try {
			SimpleDateFormat dateFormat = new SimpleDateFormat(YEAR_TO_SECOND);
			Date parse = dateFormat.parse(date);
			return parse.getTime() < System.currentTimeMillis();
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return Boolean.FALSE;
	}

	public static boolean isFuture(String date) {
		return !isPast(date);
	}

	public static Optional<Interval> parseDateRange(String dateRange)
	{
		int pair = 2;
		if (Strings.isNullOrEmpty(dateRange))
		{
			return Optional.empty();
		}
		List<String> range = Lists.newArrayList(DATE_SPLITTER.split(dateRange));
		if (CollectionUtils.isEmpty(range))
		{
			return Optional.empty();
		}

		if (range.size() == 1)
		{
			DateTime start = DateTime.parse(range.get(0), FORMATTER);
			if (start != null)
			{
				return Optional.of(new Interval(start, start));
			}

			return Optional.empty();
		}

		DateTime start = DateTime.parse(range.get(0), FORMATTER);

		DateTime end = DateTime.parse(range.get(1), FORMATTER);

		if (start == null || end == null)
		{
			return Optional.empty();
		}
		return Optional.of(new Interval(start, end));
	}

	/**
	 * 多个时间段去重，已按照开始时间排序
	 * 算法：多重插入区间，O(n^2)
	 *
	 * @param appliedIntervalList 用户存在的多个时间段，可能有重复
	 * @return 去重时间段
	 */
	public static List<Interval> mergeInterval(List<Interval> appliedIntervalList)
	{
		List<Interval> mergedIntervalList = Lists.newArrayList(appliedIntervalList.get(0));
		for (int i = 1; i < appliedIntervalList.size(); i++)
		{
			mergedIntervalList = mergeInterval(mergedIntervalList, appliedIntervalList.get(i));
		}
		return mergedIntervalList;
	}

	private static List<Interval> mergeInterval(List<Interval> intervalList, Interval targetInterval)
	{
		List<Interval> mergedInterval = Lists.newLinkedList();
		int i = 0;
		while (i < intervalList.size() && intervalList.get(i).isBefore(targetInterval.getStart()))
		{
			mergedInterval.add(intervalList.get(i));
			i++;
		}

		if (CollectionUtils.isEmpty(mergedInterval) || mergedInterval.get(mergedInterval.size() - 1).getEnd()
				.isBefore(targetInterval.getStart()))
		{
			mergedInterval.add(targetInterval);
		} else
		{
			Interval lastInterval = mergedInterval.remove(mergedInterval.size() - 1);
			Interval newLastInterval = new Interval(lastInterval.getStart(),
					lastInterval.getEnd().isBefore(targetInterval.getEnd()) ? targetInterval.getEnd() : lastInterval.getEnd());
			mergedInterval.add(newLastInterval);
		}

		return mergedInterval;
	}

	public static DateTime getEarliestDate(List<String> dateStrList, String pattern)
	{
		DateTime earliest = null;
		for (String dateStr : dateStrList)
		{
			DateTime dateTime = DateTime.parse(dateStr, DateTimeFormat.forPattern(pattern));
			if (earliest == null || dateTime.isBefore(earliest))
			{
				earliest = dateTime;
			}
		}
		return earliest;
	}

	public static DateTime getLatestDate(List<String> dateStrList, String pattern)
	{
		DateTime latest = null;
		for (String dateStr : dateStrList)
		{
			DateTime dateTime = DateTime.parse(dateStr, DateTimeFormat.forPattern(pattern));
			if (latest == null || dateTime.isAfter(latest))
			{
				latest = dateTime;
			}
		}
		return latest;
	}

	/**
	 * 获取下一个工作日的结束时间
	 */
	public static Date getNextWorkDay()
	{
		Date nextDay;
		Calendar calendar = Calendar.getInstance();
		int weekDay = calendar.get(Calendar.DAY_OF_WEEK);
		if (weekDay == Calendar.FRIDAY)
		{
			nextDay = DateUtils.addDays(new Date(), 3);
		} else if (weekDay == Calendar.SATURDAY)
		{
			nextDay = DateUtils.addDays(new Date(), 2);
		} else
		{
			nextDay = DateUtils.addDays(new Date(), 1);
		}
		return com.xhs.finance.utils.DateUtil.endOfDay(nextDay);
	}

	/**
	 * 获取下2个自然日的结束时间
	 */
	public static Date getNextTwoDay()
	{
		Date nextTwoDay = DateUtils.addDays(new Date(), 2);
		return com.xhs.finance.utils.DateUtil.endOfDay(nextTwoDay);
	}

	/**
	 * 获取下个自然日的结束时间
	 */
	public static Date getNextDay()
	{
		Date nextTwoDay = DateUtils.addDays(new Date(), 1);
		return com.xhs.finance.utils.DateUtil.endOfDay(nextTwoDay);
	}

	/**
	 * 下周结束日期（下周六），周日为第一天
	 */
	public static Date lastDayOfNextWeek(Date date)
	{
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.DAY_OF_WEEK, 7 - calendar.get(Calendar.DAY_OF_WEEK) + 7);
		return calendar.getTime();
	}

	/**
	 * .Net时间戳转Java时间戳
	 * @param timestamp
	 * @return
	 */
	public static Date parseDnetToJavaDate(Long timestamp) {
		if (null != timestamp && timestamp >= TICKS_AT_EPOCH_NT) {
			return new Date((timestamp - TICKS_AT_EPOCH_NT)/TICKS_PER_MILLISECOND);
		}
		return null;
	}

	/**
	 * 获取星期几
	 * @param date
	 * @return
	 */
	public static int dayOfWeek(Date date) {
		Calendar c=Calendar.getInstance();
		c.setTime(date);
		return c.get(Calendar.DAY_OF_WEEK);
	}


	/**
	 * pass
	 * 返回当前季度的开始时间
	 * @return Date
	 */
	public static Date startOfQuarter(Date date){
		SimpleDateFormat shortSdf = new SimpleDateFormat(SHORT_PATTERN);
		SimpleDateFormat longSdf = new SimpleDateFormat(LONG_PATTERN);
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		try
		{
			int month = calendar.get(Calendar.MONTH);
			int quarter = month / 3 + 1;
			int startMonth = 1;
			if (quarter == 2){
				startMonth = 4;
			}else if(quarter == 3){
				startMonth = 7;
			}else if(quarter == 4){
				startMonth = 10;
			}
			calendar.set(Calendar.MONTH,startMonth - 1);
			calendar.set(Calendar.DAY_OF_MONTH,1);

			calendar.setTime(longSdf.parse(shortSdf.format(calendar.getTime()) + " 00:00:00.000"));
		} catch (Exception e)
		{
			log.error("获得本季度的第一天异常", e);
			throw new BusinessException("获得本季度第一天失败");
		}

		return calendar.getTime();

	}
	/**
	 * pass
	 * 返回当前季度结束时间
	 * @return Date
	 */
	public static Date endOfQuarter(Date date){
		SimpleDateFormat shortSdf = new SimpleDateFormat(SHORT_PATTERN);
		SimpleDateFormat longSdf = new SimpleDateFormat(LONG_PATTERN);
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		int month = calendar.get(Calendar.MONTH);
		int quarter = month / 3 + 1;
		int endMonth = 3;
		if (quarter == 2){
			endMonth = 6;
		}else if(quarter == 3){
			endMonth = 9;
		}else if(quarter == 4){
			endMonth = 12;
		}
		calendar.set(Calendar.MONTH,endMonth - 1);
		int lastDay = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
		calendar.set(Calendar.DAY_OF_MONTH,lastDay);
		try
		{
			calendar.setTime(longSdf.parse(shortSdf.format(calendar.getTime()) + " 23:59:59.999"));
		} catch (Exception e)
		{
			log.error("获得本季度的最后一天异常", e);
			throw new BusinessException("获得本季度最后一天失败");
		}

		return calendar.getTime();
	}

	/**
	 * pass
	 * 返回当前季度的开始时间
	 * @return Date
	 */
	public static Date startOfMonth(Date date) {
		SimpleDateFormat shortSdf = new SimpleDateFormat(SHORT_PATTERN);
		SimpleDateFormat longSdf = new SimpleDateFormat(YEAR_TO_SECOND);
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.set(Calendar.DAY_OF_MONTH,1);
		Date time = calendar.getTime();
		String timeString = shortSdf.format(time);
		timeString = timeString + " 00:00:00";
		Date parse = null;
		try {
			parse = longSdf.parse(timeString);
		} catch (ParseException e) {
			throw new RuntimeException(e);
		}
		return parse;
	}

	public static Date endOfMonth(Date date)  {
		SimpleDateFormat shortSdf = new SimpleDateFormat(SHORT_PATTERN);
		SimpleDateFormat longSdf = new SimpleDateFormat(YEAR_TO_SECOND);
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.set(Calendar.DAY_OF_MONTH,calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
		Date time = calendar.getTime();
		String timeString = shortSdf.format(time);
		timeString = timeString + " 23:59:59";
		Date parse = null;
		try {
			parse = longSdf.parse(timeString);
		} catch (ParseException e) {
			throw new RuntimeException(e);
		}
		return parse;
	}

	/**
	 * 将短时间格式时间转换为字符串 yyyy-MM-dd
	 *
	 * @param dateDate
	 * @return
	 */
	public static String dateToStr(Date dateDate) {
		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
		String dateString = formatter.format(dateDate);
		return dateString;
	}
	/**
	 * 计算两个日期之间相差的天数
	 * @param smdate 较小的时间
	 * @param bdate  较大的时间
	 * @return 相差天数
	 * @throws ParseException
	 */
	public static int daysBetween(Date smdate,Date bdate) throws ParseException
	{
		SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd");
		smdate=sdf.parse(sdf.format(smdate));
		bdate=sdf.parse(sdf.format(bdate));
		Calendar cal = Calendar.getInstance();
		cal.setTime(smdate);
		long time1 = cal.getTimeInMillis();
		cal.setTime(bdate);
		long time2 = cal.getTimeInMillis();
		long between_days=(time2-time1)/(1000*3600*24);

		return Integer.parseInt(String.valueOf(between_days));
	}

}
