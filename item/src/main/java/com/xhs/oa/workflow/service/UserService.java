package com.xhs.oa.workflow.service;

import com.google.common.collect.Lists;
import com.xhs.oa.workflow.mapper.ActIdUserMapper;
import com.xhs.oa.workflow.model.ActIdUser;
import com.xhs.oa.workflow.vo.UserVo;
import com.xiaohongshu.fls.rpc.finance.employee.EmployeeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @Package name : com.xhs.oa.workflow.service
 * @Description : 用户相关service
 * @Date : 2018/3/7
 */
@Slf4j
@Service
public class UserService {

    @Value("${mock_user_pwd}")
    public String mock_user_pwd;

    @Autowired
    private ActIdUserMapper actIdUserMapper;


    @Autowired
    EmployeeService.Iface employeeService;



    /**
     * @Description : 根据邮箱查询用户信息，过滤无效用户
     */

    public ActIdUser selectById(String userId) {
        return actIdUserMapper.selectByPrimaryKey(userId);
    }


    /**
     * 批量查询用户信息
     */
    public List<ActIdUser> queryUsersByIds(List<String> userIds) {
        List<ActIdUser> users = new ArrayList<>();
        if (CollectionUtils.isEmpty(userIds)) {
            return users;
        }
        List<List<String>> userPage = Lists.partition(userIds, 500);
        for (List<String> partUsers : userPage) {
            List<ActIdUser> queryResults = actIdUserMapper.queryUsersByIds(partUsers);
            if (CollectionUtils.isEmpty(queryResults)) {
                continue;
            }
            users.addAll(queryResults);
        }
        return users;
    }

    /**
     * 查询用户基本信息
     * @param userIds
     * @param isValid
     * @return
     */
    public List<UserVo> findSimpleUserInfoByUserIds(List<String> userIds, Boolean isValid) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.EMPTY_LIST;
        }
        List<ActIdUser> actIdUsers = actIdUserMapper.queryUsersByIds(userIds);
        return convertFullUserVos(actIdUsers, isValid);
    }


    public List<UserVo> findSimpleUserEmailUserByEmails(List<String> emailList) {
        if (CollectionUtils.isEmpty(emailList)) {
            return new ArrayList<>();
        }
        List<ActIdUser> actIdUserList = Lists.newArrayList();
        List<List<String>> listList = Lists.partition(emailList, 200);
        for (List<String> emails : listList) {
            //批量分页查询
            List<ActIdUser> actIdUsers = actIdUserMapper.queryUserVoInfoByEmails(emails);
            if (CollectionUtils.isNotEmpty(actIdUsers)) {
                actIdUserList.addAll(actIdUsers);
            }
        }
        if (CollectionUtils.isEmpty(actIdUserList)) {
            return new ArrayList<>();
        }
        List<UserVo> resultUserVos = new ArrayList<>();
        for (ActIdUser actIdUser : actIdUserList) {
            UserVo userVo = new UserVo();
            userVo.setUserID(actIdUser.getId());
            userVo.setUserName(actIdUser.getFirst());
            userVo.setEmail(actIdUser.getEmail());
            userVo.setLeaderId(actIdUser.getLeaderId());
            userVo.setAccountStatus(actIdUser.getAccountStatus());
            userVo.setDepartmentId(actIdUser.getDepartmentId());
            resultUserVos.add(userVo);
        }

        return resultUserVos;
    }








    private List<UserVo> convertFullUserVos(List<ActIdUser> actIdUsers, boolean isValid) {
        if (CollectionUtils.isEmpty(actIdUsers)) {
            return Lists.newArrayList();
        }

        List<UserVo> userVos = Lists.newArrayList();
        for (ActIdUser actIdUser : actIdUsers) {
            if (isValid && actIdUser.getAccountStatus() == 0) {
                continue;
            }
            UserVo userVo = new UserVo(actIdUser);
            userVos.add(userVo);
        }
        return userVos;
    }

    /**
     * mailPrefix + "@"
     *
     * @param email mailPrefix + "@"
     * @return
     */
    public List<ActIdUser> findUserInfoByEmailWithLike(String email) {
        if (StringUtils.isBlank(email)) {
            return Collections.EMPTY_LIST;
        }
        return actIdUserMapper.findUserInfoByEmailWithLike(email);
    }


    public String findEmailById(String userId) {
        if (StringUtils.isBlank(userId)) {
            return null;
        }
        ActIdUser actIdUser = actIdUserMapper.selectByPrimaryKey(userId);
        if (actIdUser == null) {
            return null;
        }
        return actIdUser.getEmail();
    }

}

