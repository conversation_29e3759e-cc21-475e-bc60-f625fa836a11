package com.xhs.oa.workflow.model;

import com.xhs.oa.workflow.vo.UserVo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * <br>
 * <b>功能：</b>ActIdUser 实体类<br>
 * <b>日期：</b> 2018-03-07 10:38:17 <br>
 */
@Data
@EqualsAndHashCode(exclude = {"rev","last","pwd","pictureId","createTime","updateTime","creatorNo","updatorNo","creator","updator","isHaveSub", "LeaderIdPath"})
@SuppressWarnings("serial")
public class ActIdUser implements Serializable {

	public final static Long NULL_LEADER_ID = -9999l;

	/**
	 * 登录名称
	 */
	private String id;
	
	/**
	 * 
	 */
	private Integer rev;
	
	/**
	 * 用户名
	 */
	private String first;
	
	/**
	 * 
	 */
	private String last;
	
	/**
	 * 邮箱
	 */
	private String email;

	/**
	 * 薯名
	 */
	private String redName;

	/**
	 * 薯名邮箱
	 */
	private String redMail;
	
	/**
	 * 登录密码
	 */
	private String pwd;
	
	/**
	 * 
	 */
	private String pictureId;

	/**
	 * 用户头像url
	 */
	private String avatarUrl;

	/**
	 * 直线经理
	 */
	private Long leaderId;

	/**
	 * 部门
	 */
	private Long departmentId;

	/**
	 * 员工状态 0：停用 1：开通
	 */
	private Integer accountStatus;

	/**
	 * 工作地点
	 */
	private String workingPlace;

	/**
	 * 手机号码
	 */
	private String mobilePhone;

	/**
	 * 身份证号
	 */
	private String idNumber;

	/**
	 * 创建时间
	 */
	private Date createTime;

	/**
	 * 更新时间
	 */
	private Date updateTime;

	/**
	 * 创建人id
	 */
	private String creatorNo;

	/**
	 * 创建人名称
	 */
	private String creator;

	/**
	 * 更新人id
	 */
	private String updatorNo;

	/**
	 * 更新人名称
	 */
	private String updator;

	/**
	 * 雇佣类型:0 内部员工 1 外部人员 2 实习生 默认查询内部员工
	 */
	private Integer employType;

	/**
	 * 公司名称
	 */
	private String companyName;

	/**
	 * 公司名称(新)
	 */
	private String companyNameNew;

	/**
	 * 是否有子团队成员
	 */
	private Integer isHaveSub;

	/**
	 * 是否有正式子团队成员
	 */
	private Integer isHasRegularSub = 0;

	/**
	 * 上级领导ID的路径
	 */
	private String LeaderIdPath;

	/**
	  * description: 出生日期
	  * @author: yzhang14
	  * @date: 2018/12/11
	 */
	private String birthday;

	/**
	  * description: 入职日期
	  * @author: yzhang14
	  * @date: 2018/12/11
	 */
	private String entryDate;

	/**
	 * 人员类型
	 * @see com.xhs.oa.beiseng.enums.EmploymentTypeEnum
	 */
	private Integer employmentType;

	/**
	 * 自定义equals方法 清除部分字段为空情况的干扰
	 * @param OldUser
	 * @param newUser
	 * @return
	 */
	public static Boolean isCustomEquals(ActIdUser OldUser, ActIdUser newUser){
		if(OldUser==null || newUser==null){
			return false;
		}
		if(StringUtils.isBlank(newUser.getCompanyName())){
			newUser.setCompanyName(OldUser.getCompanyName());
		}
		if(StringUtils.isBlank(newUser.getAvatarUrl())){
			newUser.setAvatarUrl(OldUser.getAvatarUrl());
		}
		return OldUser.equals(newUser);
	}

	public String genRedName(){
		if(StringUtils.isBlank(redName)){
			return first;
		}
		return redName+"("+first+")";
	}

	public String getRedNameOrFirst() {
		return StringUtils.isBlank(redName) ? first : redName;
	}

	public UserVo transformToUserVo()
	{
		UserVo userVo = new UserVo();
		userVo.setUserID(id);
		userVo.setUserName(first);
		userVo.setRedName(redName);
		userVo.setMobilePhone(mobilePhone);
		userVo.setEmail(email);
		userVo.setRedEmail(redMail);
		userVo.setLeaderId(leaderId);
		userVo.setAccountStatus(accountStatus);
		userVo.setDepartmentId(departmentId);
		userVo.setWorkingPlaceCode(workingPlace);
		userVo.setCompanyName(companyName);
		return userVo;
	}
}

