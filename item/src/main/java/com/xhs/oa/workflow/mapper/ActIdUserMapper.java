package com.xhs.oa.workflow.mapper;

import com.xhs.oa.workflow.model.ActIdUser;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * 
 * <br>
 * <b>功能：</b>ActIdUserMapper 类<br>
 * <b>日期：</b> 2018-03-07 10:38:17 <br>
 */
public interface ActIdUserMapper {

    /**
     * 主键查询数据
     */
    ActIdUser selectByPrimaryKey(String id);

    List<ActIdUser>  queryUserListByIds(List<String> userIDList);

    /**
     * 新增数据
     */
    int insert(ActIdUser record);

    int updateUsersForCompanyName(List<ActIdUser> list);

    /**
     * 清空数据
     * @return
     */
    int updateForCompanyNameNew();

    /**
     * 主键动态更新数据
     */
    int updateByPrimaryKeySelective(ActIdUser record);

    /**
     * 批量更新数据
     * @param records
     * @return
     */
    int batchUpdateActIdUsers(List<ActIdUser> records);

    /**
     * 主键更新数据
     */
    int updateByPrimaryKey(ActIdUser record);

    /**
     * 主键更新数据
     */
    int updateEmailById(ActIdUser record);

    /**
     * 主键删除数据
     * @param id
     */
    int deleteByPrimaryKey(String id);

    /**
     * 根据邮箱地址查询用户信息
     * @param email
     * @return
     */
    List<ActIdUser> selectByEmailAndTypeAndWorkPlace(@Param("email")List<String> email, @Param("employTypeList") List<String> employTypeList,
                            @Param("workPlaceList") List<String> workPlace);

    /**
     * 根据邮箱地址查询用户信息
     * @param email
     * @return
     */
    ActIdUser selectByEmail(String email);

    /**
     * 根据身份证号查询用户信息
     * @param idNumber
     * @return
     */
    ActIdUser selectByIdNumber(String idNumber);
    ActIdUser selectValidUserByEmail(String email);

    ActIdUser selectValidUserByEmailOrRedMail(String email);

    /**
     * @description: 根据电话号码查询用户信息
     * @author: tongjingqian
     * @date: 2019/3/6
     */
    ActIdUser selectByPhoneNum(String phoneNum);


    /**
     * 根据名称动态模糊查询用户信息
     * @param value
     * @param limit
     * @return
     */
    List<ActIdUser> findUserInfoWithNameMapping(@Param("value") String value, @Param("limit") Integer limit,
            @Param("accountStatus") Integer accountStatus, @Param("employTypeList") List<String> employTypeList,
            @Param("workPlaceList") List<String> workPlace);

    /**
     * 根据薯名搜索
     */
    List<ActIdUser> findUserByRedName(String name);

    /**
     * 根据姓名搜索
     */
    List<ActIdUser> findUserByUserName(String name);

    /***
     * <AUTHOR>
     * @Description 根据真名或者薯名查询用户信息
     * @Date 2021-05-24 17:10:51
     **/
    List<ActIdUser> findUserByUserNameOrRedName(@Param("nameList") List<String> nameList);

    /**
     * 根据邮箱动态模糊查询用户信息
     * @param value
     * @param limit
     * @return
     */
    List<ActIdUser> findUserInfoWithEmailMapping(@Param("value") String value, @Param("limit") Integer limit);

    List<ActIdUser> findUserInfoByEmailList(List<String> emailList);

    /**
     * 通过邮箱地址 左匹配
     * @param email
     * @return
     */
    List<ActIdUser> findUserInfoByEmailWithLike(String email);

    /**
     * @param redEmail
     * @return
     */
    List<ActIdUser> findUserInfoByRedEmail(String redEmail);

    /**
     * 根据姓名\薯名模糊搜索
     * 不含实习生
     * @return
     */
    List<ActIdUser> queryUserWithNameMapping(@Param("userName") String userName,@Param("userId")String userId,@Param("start")Integer start,@Param("pageSize")Integer pageSize);

    /**
     * 通过leaderId获取直属下级-返回部分字段
     * @param userId
     * @return
     */
    List<ActIdUser> querySimpleDirectSubByUserId(String userId);

    /**
     * 通过leaderId获取直属下级(排除星矢)-返回部分字段
     *
     * @param userId
     * @return
     */
    List<ActIdUser> querySimpleDirectSubByUserIdWithoutCharlwin(String userId);

    /**
     *
     * @param userId
     * @param isHaveSub
     * @param leaderIdPath
     * @return
     */
    int updateLeaderIdPathByUserId(@Param("userId")String userId,
            @Param("isHaveSub")Integer isHaveSub,
            @Param("isHaveRegularSub")Integer isHaveRegularSub,
            @Param("leaderIdPath")String leaderIdPath);

    /**
     * 批量查询用户信息
     */
    List<ActIdUser> queryUsersByIds(List<String> userIds);

    /**
      * description: 批量通过邮箱查询用户名称
      * @author: qiaodeng
      * @date: 2018/12/10
     */
    List<ActIdUser> queryUserVoInfoByEmails(List<String> emailList);

    /**
      * description: 批量通过邮箱或者薯名邮箱查询用户信息
      * @author: qiaodeng
      * @date: 2019/4/4
     */
    List<ActIdUser> queryUserVoInfoByEmailsOrRedMails(List<String> emailList);

    /**
     * 根据邮箱+员工类型查询
     */
    List<ActIdUser> queryByMailsAndEmployTypes(@Param("emailList") List<String> emailList, @Param("employTypeList") List<Integer> employTypeList);

    String findDepartmentIdPathByUserId(String userId);


    String findDepartmentNamePathByUserId(String userId);

    // 环评生成测试用
    List<ActIdUser> findAllValidUserVoInfo();

    /**
      * description: 审核通过后 更新用户的薯名和邮箱
      * @author: qiaodeng
      * @date: 2018/12/25
     */
    int updateRedNameAndEmail(@Param("userId")String userId,@Param("redName")String redName,@Param("redEmail")String redEmail);

    /**
     * 更新用户的薯名
     *
     * @param userId
     * @param redName
     * @return
     */
    int updateRedName(@Param("userId") String userId, @Param("redName") String redName);

    /**
     * 获取所有有效的内部员工的数据
     * @return
     */
    List<ActIdUser> getAllValidInnerUsersList();

    /**
     * 根据部门id查找用户，只查询该部门直属的，不包含子部门的
     *
     * @param departmentId departmentId
     * @return ActIdUser
     */
    List<ActIdUser> findValidUserByDepartmentId(@Param("departmentId") Long departmentId);

    /**
     * 根据部门，员工类型和工作地点查找员工
     *
     * @param departmentIds 部门
     * @param employType   员工类型
     * @param workPlace    工作地点
     * @return ActIdUser
     */
    List<ActIdUser> findExtraUserByDepartmentIds(@Param("departmentIdList") List<Long> departmentIds,
            @Param("employTypeList") List<String> employType, @Param("workPlaceList") List<String> workPlace);

    /**
     * 获取所有的下属人员
     * @param leaderIdPath
     * @return
     */
    List<ActIdUser> findAllSubUserByLeaderIdPath(@Param("leaderIdPath") String leaderIdPath);

    /**
     * 搜索特定聘用类型的有效员工
     *
     * @param employTypeList 聘用类型
     * @return 有效员工
     */
    List<ActIdUser> findValidUserListByEmployType(@Param("employTypeList") List<Integer> employTypeList);

    /**
     * 搜索直属下属id
     *
     * @param leaderId       领导id
     * @param employTypeList 聘用类型
     * @return 直属下属
     */
    List<String> selectValidUserListByLeaderId(@Param("leaderId") String leaderId,
            @Param("employTypeList") List<String> employTypeList);

    /**
     * 获取所有员工所属分公司
     */
    List<String> queryCompanyList();

    /**
     * 根据部门，人员类型和工作地点、费用日期查找员工
     *
     * @param departmentIds      部门
     * @param employmentTypeList 人员类型
     * @param workPlace          工作地点
     * @param otherWorkPlace     其他工作地点（not in 操作 与workPlace互斥）
     * @param endEntryDate       入职截止时间
     * @return ActIdUser
     */
    List<ActIdUser> findTeamBuildingUsers(@Param("departmentIdList") List<Long> departmentIds,
                                          @Param("employmentTypeList") List<String> employmentTypeList,
                                          @Param("workPlaceList") List<String> workPlace,
                                          @Param("otherWorkPlace") List<String> otherWorkPlace,
                                          @Param("endEntryDate") Date endEntryDate);



    List<ActIdUser> getUserForRenewCompanyName();

    int releaseRedNames(@Param("userIds") List<String> userIds , @Param("updatorNo") String updatorNo , @Param("updator") String updator);

    int batchUpdateBpoDept(@Param("userIds") List<String> userIds,
                           @Param("departmentId") Long departmentId,
                           @Param("employmentType") Integer employmentType,
                           @Param("updatorNo") String updatorNo,
                           @Param("updator") String updator);



    /**
     * 获取总数
     * @return
     */
    Integer getLeavedUserCount();


    List<ActIdUser> getCheckRedMailUser();

    String findUserByDepartmentId(String createId);

    ActIdUser findUserById(@Param("personInCharge") String personInCharge);

    /**
     * 根据部门id查找用户
     *
     * @param departmentId departmentId
     * @return ActIdUser
     */
    ActIdUser queryLimitUserByDepartmentId(@Param("departmentId") Long departmentId);


    /**
     * 批量查询在职正式员工用户信息
     */
    List<ActIdUser> queryAvailableUserListByIds(List<String> userIds);

    /**
     * 批量更新头像
     * @param list
     * @return
     */
    int batchUpdateAvatar(List<ActIdUser> list);

}
