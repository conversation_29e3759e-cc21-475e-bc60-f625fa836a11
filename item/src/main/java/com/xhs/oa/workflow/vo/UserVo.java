package com.xhs.oa.workflow.vo;

import com.xhs.oa.workflow.model.ActIdUser;
import com.xiaohongshu.fls.rpc.finance.employee.response.EmployeeInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Data
@NoArgsConstructor
public class UserVo {

    /**
     * 人员根节点id
     */
    public static final String ROOT_LEADER_ID = "111425930";

    @ApiModelProperty(notes = "用户id")
    private String userID;

    @ApiModelProperty(notes = "姓名")
    private String userName;

    @ApiModelProperty(notes = "薯名")
    private String redName;

    @ApiModelProperty(notes = "邮箱")
    private String email;

    @ApiModelProperty(notes = "薯名邮箱")
    private String redEmail;

    @ApiModelProperty(notes = "部门")
    private String department;

    @ApiModelProperty(notes = "部门Id")
    private Long departmentId;

    @ApiModelProperty(notes = "通用一级部门Id")
    private Long commonFirstDepartmentId;

    @ApiModelProperty(notes = "一级部门Id")
    private Long firstDepartmentId;

    @ApiModelProperty(notes = "三级部门Id")
    private Long thirdDepartmentId;

    @ApiModelProperty(notes = "四级部门Id")
    private Long forthDepartmentId;

    @ApiModelProperty(notes = "五级部门Id")
    private Long fifthDepartmentId;

    @ApiModelProperty(notes = "工作地点")
    private String workingPlace;

    @ApiModelProperty(notes = "工作地点Code")
    private String workingPlaceCode;

    @ApiModelProperty(notes = "员工状态")
    private Integer accountStatus;

    @ApiModelProperty(notes = "员工状态描述")
    private String accountStatusDesc;

    /*@ApiModelProperty(notes = "角色")
    private String roles;*/

    @ApiModelProperty(notes = "直属上级")
    private String leader;

    @ApiModelProperty(notes = "直属上级Id")
    private Long leaderId;

    @ApiModelProperty(notes = "手机号码")
    private String mobilePhone;
    @ApiModelProperty(notes = "身份证号")
    private String idNumber;
    @ApiModelProperty(notes = "分公司code")
    private String companyCode;

    @ApiModelProperty("string类型部门路径")
    private String deptIdsPath;

    @ApiModelProperty(notes = "部门id路径")
    private String[] departmentIdsPath;

    @ApiModelProperty(notes = "部门名称路径")
    private String[] departmentNamesPath;

    @ApiModelProperty(notes = "leadId路径")
    private String leaderIdsPath;

    @ApiModelProperty(notes = "公司名称")
    private String companyName;

    @ApiModelProperty(notes = "微信头像url")
    private String avatarUrl;

    @ApiModelProperty(notes = "是否有子团队成员")
    private Integer isHaveSub;

    @ApiModelProperty(notes = "0 内部员工 1 外部人员 2 实习生 默认查询内部员工")
    private Integer employType;

    @ApiModelProperty(notes = "入职日期")
    private String entryDate;

    @ApiModelProperty(notes = "入职天数")
    private Long entryDays;

    @ApiModelProperty(notes = "性别")
    private Integer gender;

    @ApiModelProperty(notes = "人员类型 1:正式员工 2:实习生 3:编外人员 4:供应商派遣")
    private Integer employmentType;

    @ApiModelProperty("HRBP人员")
    private String hrbp;

    @ApiModelProperty("HRBP负责人")
    private String hrpbLeader;

    @ApiModelProperty("一级部门负责人")
    private String firstDepartmentLeader;

    @ApiModelProperty("二级部门负责人")
    private String secondDepartmentLeader;

    @ApiModelProperty("三级部门负责人")
    private String thridDepartmentLeader;

    @ApiModelProperty("四级部门负责人")
    private String forthDepartmentLeader;

    @ApiModelProperty("五级部门负责人")
    private String fifthDepartmentLeader;

    @ApiModelProperty("当前部门负责人")
    private String currentDepartmentLeader;

    @ApiModelProperty("权限点")
    private List<String> permissions;

    @ApiModelProperty("个人中心展示语")
    private String showMessage;

    @ApiModelProperty("生日")
    private String birthDay;

    @ApiModelProperty("外包公司名称")
    private String outsourcingName;

    public UserVo(ActIdUser actIdUser){
        this.userID = actIdUser.getId();
        this.userName = actIdUser.getFirst();
        this.redName = actIdUser.getRedName();
        this.email = actIdUser.getEmail();
        this.departmentId = actIdUser.getDepartmentId();
        this.mobilePhone = actIdUser.getMobilePhone();
        this.idNumber = actIdUser.getIdNumber();
        this.companyName = actIdUser.getCompanyName();
        if (StringUtils.isNotBlank(actIdUser.getCompanyNameNew())) {
            this.companyName = actIdUser.getCompanyNameNew();
        }
        this.leaderIdsPath = actIdUser.getLeaderIdPath();
        this.isHaveSub = actIdUser.getIsHaveSub();
        this.avatarUrl = actIdUser.getAvatarUrl();
        this.employType = actIdUser.getEmployType();
        this.accountStatus = actIdUser.getAccountStatus();
        this.workingPlaceCode = actIdUser.getWorkingPlace();
        this.entryDate = actIdUser.getEntryDate();
        this.leaderId = actIdUser.getLeaderId();
        this.employmentType = actIdUser.getEmploymentType();
        this.birthDay = actIdUser.getBirthday();
    }

    public void employeeInfoToUserVo(EmployeeInfo employeeInfo) {
        if (employeeInfo != null) {
            this.userID = employeeInfo.getEmployee_id();
            this.userName = employeeInfo.getEmployee_name();
            this.redName = employeeInfo.getRed_name();
            this.email = employeeInfo.getEmail();
            this.departmentId = employeeInfo.getDepartment_id();
            this.department = employeeInfo.getDepartment_name();
            this.mobilePhone = employeeInfo.getMobile_phone();
            this.companyName = employeeInfo.getCompany_name();
            this.redEmail = employeeInfo.getRed_mail();
            this.leaderIdsPath = employeeInfo.getLeader_id_path();
            this.isHaveSub = Integer.valueOf(employeeInfo.getIs_have_sub());
            this.avatarUrl = employeeInfo.getAvatar_url();
            if (employeeInfo.getEmployee_type() != null) {
                this.employType = employeeInfo.getEmployee_type().getValue();
            }
            if (employeeInfo.getEmployment_type() != null) {
                this.employmentType = employeeInfo.getEmployment_type().getValue();
            }
            if (employeeInfo.getAccount_status() != null) {
                this.accountStatus = employeeInfo.getAccount_status().getValue();
            }
            this.workingPlaceCode = employeeInfo.getWorking_place_code();
            this.entryDate = employeeInfo.getEntry_date();
            if (StringUtils.isNotBlank(employeeInfo.getLeader_id())) {
                this.leaderId = Long.valueOf(employeeInfo.getLeader_id());
            }
            this.birthDay = employeeInfo.getBirthday();
            if (employeeInfo.getGender() != null) {
                this.gender = employeeInfo.getGender().getValue();
            }
        }
    }

    public String genRedName(){
        if(StringUtils.isBlank(redName)){
            return userName;
        }
        return redName+"("+userName+")";
    }

    public String getRedNameOrFirst(){
        if(StringUtils.isBlank(redName)){
            return userName;
        }
        return redName;
    }

}
