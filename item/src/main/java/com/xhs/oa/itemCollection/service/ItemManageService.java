package com.xhs.oa.itemCollection.service;

import com.github.pagehelper.PageHelper;
import com.xhs.finance.exception.BusinessException;
import com.xhs.finance.sso.model.UserInfo;
import com.xhs.oa.common.util.ExcelUtil;
import com.xhs.oa.itemCollection.constant.ItemConstant;
import com.xhs.oa.itemCollection.dto.AreaStockDTO;
import com.xhs.oa.itemCollection.enums.OperateType;
import com.xhs.oa.itemCollection.mapperProcessor.TItemsMapperProcessor;
import com.xhs.oa.itemCollection.model.*;
import com.xhs.oa.itemCollection.param.AreaStockParam;
import com.xhs.oa.itemCollection.param.DownloadParam;
import com.xhs.oa.itemCollection.param.SpecStockParam;
import com.xhs.oa.itemCollection.param.UpdateStockParam;
import com.xhs.oa.workflow.mapper.ActIdUserMapper;
import com.xhs.oa.workflow.model.ActIdUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 库存管理
 */
@Slf4j
@Service
public class ItemManageService
{
	@Autowired
	private TItemsMapperProcessor itemsMapperProcessor;

	@Autowired
	private ItemService itemService;
	@Autowired
	private ActIdUserMapper actIdUserMapper;

	@Autowired
	private ItemSpecificPersonService itemSpecificPersonService;


	/**
	 * 分页查询业务部门下的地区
	 */
	public List<TItemsArea> queryPagedArea(Long departmentId, Integer pageNum, Integer pageSize)
	{
		PageHelper.startPage(pageNum, pageSize);
		return itemsMapperProcessor.queryAreasByDepartmentId(departmentId);
	}

	/**
	 * 查询地区的各规格库存
	 */
	public List<AreaStockDTO> queryAreaStock(List<Long> areaIds)
	{
		if (CollectionUtils.isEmpty(areaIds))
		{
			return new ArrayList<>();
		}
		return itemsMapperProcessor.queryAreaStock(areaIds);
	}

	/**
	 * 新增物品、地区、库存
	 */
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, transactionManager = "oaTransactionManager")
	public void addAreaStock(AreaStockParam param, UserInfo userInfo)
	{
		List<TItems> tItems = itemService.queryItemsByDeptIdAndItemName(param.getDepartmentId(), param.getItemName());
		if (tItems.size() > 1)
		{
			throw new BusinessException("部门下存在相同物品：" + param.getItemName());
		}
		List<SpecStockParam> specStockParams = param.getSpecStockParams();
		Long itemId;
		Long specId;
		if (tItems.size() == 1)
		{
			itemId = tItems.get(0).getId();
			List<TItemsSpec> tItemsSpecs = itemService.querySpecs(itemId);
			specId = tItemsSpecs.get(0).getId();
		} else
		{ // 新增物品及规格
			TItems newItems = new TItems();
			newItems.setItem(param.getItemName());
			newItems.setDepartmentId(param.getDepartmentId());
			newItems.setOperatorNo(userInfo.getUserId());
			newItems.setOperator(userInfo.getName());
			newItems.setPopUpMessageStatus(param.getPopUpMessageStatus());
			newItems.setHeadline(param.getHeadline());
			newItems.setPopUpContent(param.getPopUpContent());
			newItems.setItemEmployeeType(String.join(",",param.getItemEmployeeType()));
			itemsMapperProcessor.insertItems(newItems);
			itemId = newItems.getId();

			TItemsSpec itemsSpec = new TItemsSpec();
			itemsSpec.setItemId(itemId);
			itemsSpec.setSpecification(ItemConstant.DEFAULT_ITEM_SPEC);
			itemsSpec.setUnit(specStockParams.get(0).getUnit());
			itemsSpec.setOperatorNo(userInfo.getUserId());
			itemsSpec.setOperator(userInfo.getName());
			itemsMapperProcessor.insertItemsSpec(itemsSpec);
			specId = itemsSpec.getId();
		}
		//指定名单员工物资
		List<String> userIds = param.getUserIds();
		if (CollectionUtils.isNotEmpty(userIds)) {
			List<TItemsSpecificPerson> specificList =new ArrayList<>();
			userIds.stream().forEach(v-> specificList.add(new TItemsSpecificPerson(itemId,v,userInfo.getUserId(),userInfo.getName())));
			itemSpecificPersonService.batchInsert(specificList);
		}
		TItemsArea area = new TItemsArea();
		area.setItemId(itemId);
		area.setArea(param.getAreaName());
		area.setOperatorNo(userInfo.getUserId());
		area.setOperator(userInfo.getName());
		itemsMapperProcessor.addArea(area);

		List<TItemsStock> stocks = new ArrayList<>();
		for (SpecStockParam specStockParam : specStockParams)
		{
			TItemsStock stock = new TItemsStock();
			stock.setItemAreaId(area.getId());
			stock.setItemSpecId(specId);
			stock.setTotalStock(specStockParam.getStock());
			stock.setStock(specStockParam.getStock());
			stock.setOperatorNo(userInfo.getUserId());
			stock.setOperator(userInfo.getName());
			stocks.add(stock);
		}
		itemsMapperProcessor.addAreaStocks(stocks);
	}

	/**
	 * 删除地区
	 */
	@Transactional(propagation = Propagation.REQUIRED, transactionManager = "oaTransactionManager")
	public void deleteArea(Long areaId)
	{
		TItemsArea area = itemService.queryAreaById(areaId);
		if (null == area)
		{
			return;
		}
		itemsMapperProcessor.deleteArea(areaId);

		// 若对应物品的地区都被删除，则删除物品
		List<TItemsArea> tItemsAreas = itemService.queryAreasByItemIds(Lists.newArrayList(area.getItemId()));
		if (CollectionUtils.isEmpty(tItemsAreas))
		{
			itemsMapperProcessor.deleteItem(area.getItemId());
		}
	}

	/**
	 * 调整地区库存
	 */
	@Transactional(propagation = Propagation.REQUIRED, transactionManager = "oaTransactionManager")
	public void updateAreaStock(UpdateStockParam param, UserInfo userInfo)
	{
		Long areaId = param.getAreaId();
		Long specId = param.getSpecId();
		Integer amount = param.getAmount();
		String reason = param.getReason();

		TItemsStock tItemsStock = itemsMapperProcessor.queryStockRecord(areaId, specId);
		if (null == tItemsStock)
		{
			log.error("调整库存，记录不存在，地区id{}，规格id{}", areaId, specId);
			return;
		}

		if (Long.valueOf(tItemsStock.getStock()) + Long.valueOf(param.getAmount()) >= Integer.MAX_VALUE) {
			throw new BusinessException("超过库存可调整最大值");
		}

		// 新增调整记录
		TItemsStockLog stockLog = new TItemsStockLog();
		stockLog.setItemAreaId(areaId);
		stockLog.setItemSpecId(specId);
		stockLog.setUserId("");
		stockLog.setOperateType(amount > 0 ? OperateType.ADD_STOCK.getType() : OperateType.REDUCE_STOCK.getType());
		stockLog.setAmount(Math.abs(amount));
		stockLog.setReason(null==reason ? "" : reason);
		stockLog.setOperatorNo(userInfo.getUserId());
		stockLog.setOperator(userInfo.getName());
		stockLog.setOperatorPhone("");
		itemsMapperProcessor.insertStockLog(stockLog);

		// 更新库存
		int newTotalStock = tItemsStock.getTotalStock() + amount;
		int newStock = tItemsStock.getStock() + amount;
		Map<String, Object> paramsMap = new HashMap<>();
		paramsMap.put("stockId", tItemsStock.getId());
		paramsMap.put("totalStock", tItemsStock.getTotalStock());
		paramsMap.put("newTotalStock", newTotalStock);
		paramsMap.put("stock", tItemsStock.getStock());
		paramsMap.put("newStock", newStock);
		paramsMap.put("operatorNo", userInfo.getUserId());
		paramsMap.put("operator", userInfo.getName());
		paramsMap.put("operatorPhone", "");

		int affectedCount = itemsMapperProcessor.updateStock(paramsMap);
		if (affectedCount != 1)
		{
			throw new BusinessException("库存更新失败");
		}
	}

	/**
	 * 下载领取记录
	 */
	public List<TItemsStockLog> queryLogs(DownloadParam param)
	{
		return itemsMapperProcessor.queryLogs(param);
	}

	/**
	 * 下载领取记录总数
	 */
	public Integer queryPagedLogCounts(DownloadParam param)
	{
		return itemsMapperProcessor.queryPagedLogCounts(param);
	}

	/**
	 * 通过物品名称和地区名称查询是否已存在
	 */
	public Boolean queryItemAreaExist(Long departmentId, String itemName, String areaName)
	{
		int count = itemsMapperProcessor.queryItemAreaExist(departmentId, itemName, areaName);
		return count > 0;
	}

	public  String apolloParam(Workbook wb,int sheetNum,int emailColumn,int itemId){
		Sheet sheetAt = wb.getSheetAt(sheetNum);
		int lastRowNum = sheetAt.getLastRowNum();
		StringBuilder sb = new StringBuilder();
		sb.append("\"");
		sb.append(itemId);
		sb.append("\" [");
		for (int i = 0; i <= lastRowNum; i++) {
			Row row = sheetAt.getRow(i);
			String email = ExcelUtil.getStringValue(row.getCell(emailColumn));
			List<ActIdUser> actIdUsers = actIdUserMapper.queryUserVoInfoByEmails(Collections.singletonList(email));
			String id = actIdUsers.get(0).getId();
			sb.append("\"");
			sb.append(id);
			sb.append("\"");
			if (i != lastRowNum){
				sb.append(",");
			}
		}
		sb.append("]");
		return sb.toString();
	}
}
