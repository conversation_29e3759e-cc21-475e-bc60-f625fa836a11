package com.xhs.oa.itemCollection.mapper;

import com.xhs.oa.itemCollection.model.TItemsArea;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 
 * <br>
 * <b>功能：</b>TItemsAreaMapper 类<br>
 * <b>日期：</b> 2020-02-04 11:05:01 <br>
 */
public interface TItemsAreaMapper {

    /**
     * 主键查询数据
     */
    TItemsArea selectByPrimaryKey(Long id);

    /**
     * 新增数据
     */
    int insert(TItemsArea record);


    /**
     * 主键更新数据
     */
    int updateByPrimaryKey(TItemsArea record);

    List<TItemsArea> queryAreasByDepartmentId(Long departmentId);

    /**
     * 删除地区
     */
    int deleteArea(@Param("id") Long id);

    int queryItemAreaExist(@Param("departmentId") Long departmentId, @Param("itemName") String itemName, @Param("areaName") String areaName);

    List<TItemsArea> queryAreasByItemIds(List<Long> itemIds);

    List<TItemsArea> queryAreaByName(@Param("area") String area);
}
