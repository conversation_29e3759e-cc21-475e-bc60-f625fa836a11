package com.xhs.oa.itemCollection.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * <br>
 * <b>功能：</b>TItems 实体类<br>
 * <b>日期：</b> 2020-02-04 11:05:01 <br>
 */
@Data
@SuppressWarnings("serial")
public class TItemsSpec implements Serializable {
	
	/**
	 * 自增物品id
	 */
	private Long id;

	private Long itemId;
	
	/**
	 * 物品规格名称
	 */
	private String specification;

	private String unit;
	
	/**
	 * 是否有效：0无效，1有效
	 */
	private Integer isValid;
	
	/**
	 * 操作人id
	 */
	private String operatorNo;
	
	/**
	 * 操作人名称
	 */
	private String operator;
	
	/**
	 * 操作时间
	 */
	private Date operateTime;
	

}

