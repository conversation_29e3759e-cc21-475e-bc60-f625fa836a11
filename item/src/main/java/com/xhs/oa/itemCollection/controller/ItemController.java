package com.xhs.oa.itemCollection.controller;

import com.alibaba.fastjson.JSONObject;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.xhs.cache.RedisClient;
import com.xhs.finance.exception.BusinessException;
import com.xhs.finance.framework.page.PageResult;
import com.xhs.finance.framework.resubmit.Resubmit;
import com.xhs.finance.sso.UserInfoBag;
import com.xhs.finance.sso.model.UserInfo;
import com.xhs.oa.common.constant.CommonConstant;
import com.xhs.oa.common.service.ApolloCommonConfigService;
import com.xhs.oa.itemCollection.constant.ItemConstant;
import com.xhs.oa.itemCollection.dto.ItemOutsiderDTO;
import com.xhs.oa.itemCollection.dto.ItemSpecDTO;
import com.xhs.oa.itemCollection.enums.ReceiveCycleType;
import com.xhs.oa.itemCollection.model.TItems;
import com.xhs.oa.itemCollection.model.TItemsArea;
import com.xhs.oa.itemCollection.model.TItemsStockLog;
import com.xhs.oa.itemCollection.mq.producer.ItemProducer;
import com.xhs.oa.itemCollection.service.ItemOutsider;
import com.xhs.oa.itemCollection.service.ItemService;
import com.xhs.oa.itemCollection.utils.JsSdkSignUtils;
import com.xhs.oa.itemCollection.vo.*;
import com.xhs.oa.wechat.Enums.WechatAppEnum;
import com.xhs.oa.wechat.service.WechatService;
import com.xhs.oa.workflow.service.UserService;
import com.xhs.oa.workflow.vo.UserVo;
import com.xiaohongshu.erp.common.framework.resubmit.v2.ResubmitV2;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@Slf4j
@Api(value = "/xhs-oa/items", description = "物品领用相关接口")
@RequestMapping("/xhs-oa/items")
public class ItemController
{

	@Autowired
	private ApolloCommonConfigService apolloCommonConfigService;

	@Autowired
	private WechatService wechatService;

	@Autowired
	private ItemService itemService;

	@Autowired
	private UserService userService;

	@Autowired
	private RedisClient redisClient;

	/**
	 * 外部人员发放物资ID
	 */
	@ApolloJsonValue("${item_external_give_out_id:[]}")
	private List<Long> externalGiveOutItemIdList;

	@ApolloJsonValue("${item_exclude_area_list:[]}")
	private List<String> excludeAreaList;

	@ApiOperation(notes = "/getUserInfoByCodeForH5", value = "根据code获取用户信息")
	@GetMapping(value = "/getUserInfoByCodeForH5")
//	@LogOperatorType(operatorType = OperatorLogTypeEnum.ENTER_WECHAT_SIMULATION_LOGIN)
	public UserInfo getUserInfoByCode(@RequestParam(name = "code") String code)
	{
		UserInfo userInfo = wechatService.getUserInfoByCode(code, WechatAppEnum.ITEM.getCode());
		return userInfo;
	}

	@ApiOperation(notes = "/queryItems", value = "查询物品")
	@GetMapping(value = "/queryItems")
	public List<ItemVO> queryItems(@RequestParam(required = false) Long departmentId)
	{
		if (null == departmentId)
		{
			departmentId = ItemConstant.DEFAULT_DEPARTMENT_ID;
		}
		UserInfo userInfo = UserInfoBag.get();
		List<TItems> tItems = itemService.queryItemsByDeptId(departmentId);
		List<ItemVO> result = new ArrayList<>();
		tItems.forEach(v -> {
			if (itemService.receivable(v.getId(), userInfo.getUserId()))
			{
				result.add(ItemVO.toItemVO(v));
			}
		});
		return result;
	}

	@ApiOperation(notes = "/introSentence", value = "查询领用说明文字")
	@GetMapping(value = "/introSentence")
	public String introSentence(@RequestParam Long itemId)
	{
		Map<String, String> itemIntroMap = apolloCommonConfigService.getMapDataByKey(ItemConstant.INTO_KEY);
		return itemIntroMap.get(itemId.toString());
	}

	@ApiOperation(notes = "/queryUserReceivedCount", value = "查询当前用户领用数量")
	@GetMapping(value = "/queryUserReceivedCount")
	public ReceiveCountVO queryUserReceivedCount(@RequestParam Long itemId)
	{
		ReceiveCycleType receiveCycleType = itemService.queryReceiveCycle(itemId);

		Integer count = itemService.queryUserReceivedCount(UserInfoBag.get().getUserId(), itemId, receiveCycleType);
		ReceiveCountVO receiveCountVO = new ReceiveCountVO();
		receiveCountVO.setCount(count);
		receiveCountVO.setUnit(itemService.queryUnit(itemId));
		receiveCountVO.setCycle(receiveCycleType.getMsg());
		Integer maxReceivedCount = itemService.getMaxReceivedCount(itemId, receiveCycleType.getType());
		if (maxReceivedCount != null) {
			receiveCountVO.setAvailableCount(maxReceivedCount);
		}
		return receiveCountVO;
	}

	@ApiOperation(notes = "/queryUserQRCode", value = "获取当前用户二维码code")
	@GetMapping(value = "/queryUserQRCode")
	public String queryUserQRCode(@RequestParam Long itemId)
	{
		if (null == itemId)
		{
			throw new BusinessException("物品id为空");
		}
		return itemService.queryUserQRCode(UserInfoBag.get().getUserId(), itemId);
	}

	@ApiOperation(notes = "/queryUserReceiveResult", value = "生成二维码后，前端轮询用户领用状态，领取成功后展示领取结果信息")
	@GetMapping(value = "/queryUserReceiveResult")
	public Boolean queryUserReceiveResult(@RequestParam String code) {
		return itemService.hasReceivedRecent(code);
	}

	@ApiOperation(notes = "/queryPagedMyReceiveRecord", value = "查看自己的领取记录")
	@GetMapping(value = "/queryPagedMyReceiveRecord")
	public PageResult<UserReceiveVO> queryPagedMyReceiveRecord(@RequestParam Integer pageNum,
																@RequestParam Integer pageSize,
																@RequestParam(required = false) Long departmentId)
	{
		if (null == departmentId)
		{
			departmentId = ItemConstant.DEFAULT_DEPARTMENT_ID;
		}
		List<TItems> tItems = itemService.queryItemsByDeptId(departmentId);
		List<Long> itemIds = tItems.stream().map(TItems::getId).collect(Collectors.toList());
		List<TItemsStockLog> tItemsStockLogs = itemService
				.queryPagedReceiveRecord(itemIds, pageNum, pageSize, UserInfoBag.get().getUserId());

		return this.fillPageResult(tItemsStockLogs, false);
	}

	private List<UserReceiveVO> transformRecordToVo(List<TItemsStockLog> list, Boolean needRedName)
	{
		if (CollectionUtils.isEmpty(list))
		{
			return new ArrayList<>();
		}
		Map<String, String> redNameMap = null;
		if (needRedName)
		{
			// 设置薯名
			List<String> userIds = list.stream().map(TItemsStockLog::getUserId).distinct().collect(Collectors.toList());
			List<UserVo> simpleUserInfos = userService.findSimpleUserInfoByUserIds(Lists.newArrayList(userIds), false);
			redNameMap = simpleUserInfos.stream().collect(Collectors.toMap(UserVo::getUserID, UserVo::getRedNameOrFirst));
		}

		List<Long> specIds = list.stream().map(TItemsStockLog::getItemSpecId).distinct().collect(Collectors.toList());
		List<ItemSpecDTO> itemSpecDTOs = itemService.queryItemSpecNameBySpecIds(specIds);
		Map<Long, ItemSpecDTO> itemSpecDTOMap = itemSpecDTOs.stream().collect(Collectors.toMap(ItemSpecDTO::getSpecId, v->v));

		List<UserReceiveVO> result = new ArrayList<>();
		for (TItemsStockLog tItemsStockLog : list)
		{
			ItemSpecDTO itemSpecDTO = itemSpecDTOMap.get(tItemsStockLog.getItemSpecId());

			UserReceiveVO userReceiveVO = new UserReceiveVO();
			userReceiveVO.setDate(tItemsStockLog.getOperateTime());
			userReceiveVO.setReceiveTime(tItemsStockLog.getOperateTime());
			userReceiveVO.setAmount(tItemsStockLog.getAmount());
			userReceiveVO.setUnit(itemSpecDTO==null ? "" : itemSpecDTO.getUnit());
			userReceiveVO.setItemName(itemSpecDTO==null ? "" : itemSpecDTO.getItemName());
			if (needRedName)
			{
				userReceiveVO.setRedName(redNameMap.get(tItemsStockLog.getUserId()));
			}
			result.add(userReceiveVO);
		}

		return result;
	}


	@ApiOperation(notes = "/queryPagedAllReceiveRecord", value = "发放人查看发放记录")
	@GetMapping(value = "/queryPagedAllReceiveRecord")
	public PageResult<UserReceiveVO> queryPagedAllReceiveRecord(@RequestParam Integer pageNum,
																	@RequestParam Integer pageSize,
																	@RequestParam(required = false) Long departmentId)
	{
		if (null == departmentId)
		{
			departmentId = ItemConstant.DEFAULT_DEPARTMENT_ID;
		}
		List<TItems> tItems = itemService.queryItemsByDeptId(departmentId);
		List<Long> itemIds = tItems.stream().map(TItems::getId).collect(Collectors.toList());
		List<TItemsStockLog> tItemsStockLogs = itemService.queryPagedGiveOutRecord(itemIds, pageNum, pageSize, UserInfoBag.get().getUserId());

		return this.fillPageResult(tItemsStockLogs, true);
	}

	private PageResult<UserReceiveVO> fillPageResult(List<TItemsStockLog> tItemsStockLogs, Boolean needRedName)
	{
		PageInfo<TItemsStockLog> pageList = new PageInfo<>(tItemsStockLogs);
		List<UserReceiveVO> userReceiveVOs = this.transformRecordToVo(tItemsStockLogs, needRedName);
		PageResult<UserReceiveVO> pageResult = new PageResult<>();
		pageResult.setPageNum(pageList.getPageNum());
		pageResult.setPageSize(pageList.getPageSize());
		pageResult.setList(userReceiveVOs);
		pageResult.setTotalPage(pageList.getPages());
		pageResult.setTotal(Integer.valueOf(Long.valueOf(pageList.getTotal()).toString()));
		return pageResult;
	}

	@ApiOperation(notes = "/queryGiveOutAuth", value = "是否展示“扫码”tab，即当前用户是否具有发放权限")
	@GetMapping(value = "/queryGiveOutAuth")
	public Boolean queryGiveOutAuth() {
		return itemService.checkGiveOutAuth(UserInfoBag.get().getUserId());
	}

	@ApiOperation(notes = "/queryAreas", value = "查询领取地区")
	@GetMapping(value = "/queryAreas")
	public List<TItemsArea> queryAreas(@RequestParam(required = false) Long departmentId)
	{
		if (null == departmentId)
		{
			departmentId = ItemConstant.DEFAULT_DEPARTMENT_ID;
		}
		List<TItems> tItems = itemService.queryItemsByDeptId(departmentId);

		List<Long> itemIdList = tItems.stream().map(TItems::getId).collect(Collectors.toList());
		List<TItemsArea> tItemsAreas = itemService.queryAreasByItemIds(itemIdList);

		List<TItemsArea> result = new ArrayList<>();
		List<String> areaNames = new ArrayList<>();
		for (TItemsArea tItemsArea : tItemsAreas)
		{
			// 地区名称去重
			if (areaNames.contains(tItemsArea.getArea()))
			{
				continue;
			}
			// 排除一些地区比如健身房，后续可改为按item过滤
			if (excludeAreaList.contains(tItemsArea.getArea())) {
				continue;
			}
			areaNames.add(tItemsArea.getArea());
			result.add(tItemsArea);
		}
		return result;
	}

	@ApiOperation(notes = "/queryReceiveInfoByQRCode", value = "发放人扫码后根据code查询领取信息")
	@GetMapping(value = "/queryReceiveInfoByQRCode")
	public UserReceiveVO queryReceiveInfoByQRCode(@RequestParam String code)
	{
		Object o = itemService.queryRedisCache(code);
		if (null == o)
		{
			throw new BusinessException("二维码已过期");
		}
		UserReceiveVO userReceiveVO = (UserReceiveVO) o;
		Date dateCache = userReceiveVO.getDate();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		String cacheFormat = sdf.format(dateCache);
		String nowFormat = sdf.format(new Date());
		if (!cacheFormat.equals(nowFormat))
		{
			throw new BusinessException("日期已更新，请刷新重试");
		}

		Long itemId = userReceiveVO.getItemId();

		List<ItemSpecDTO> itemSpecDTOs = itemService.queryItemSpecNameByItemIds(Lists.newArrayList(itemId));
		if (CollectionUtils.isEmpty(itemSpecDTOs)) {
			throw new BusinessException("物品不存在");
		}
		userReceiveVO.setItemName(itemSpecDTOs.get(0).getItemName());
		userReceiveVO.setUnit(itemSpecDTOs.get(0).getUnit());

		ReceiveCycleType receiveCycleType = itemService.queryReceiveCycle(itemId);
		userReceiveVO.setReceiveCycle(receiveCycleType.getMsg());

		// 领取数量
		int todayReceived = itemService.queryUserReceivedCount(userReceiveVO.getUserId(), itemId, receiveCycleType);
		userReceiveVO.setTodayAmount(todayReceived);

		// 根据物品设置默认领取数量
		userReceiveVO.setAmount(itemService.queryReceiveDefaultAmount(itemId));

		return userReceiveVO;
	}

	@ApiOperation(notes = "/giveOut", value = "填写发放数量，点击“确定发放”")
	@PostMapping(value = "/giveOut")
	@Resubmit(spaceTimeSecond = CommonConstant.SUBMIT_SPACE_TIME)
	public void giveOut(@RequestBody UserReceiveVO userReceiveVO)
	{
		if (StringUtils.isBlank(userReceiveVO.getCode()))
		{
			throw new BusinessException("领取code为空");
		}
		if (StringUtils.isBlank(userReceiveVO.getUserId()))
		{
			throw new BusinessException("用户id为空");
		}
		if (null == userReceiveVO.getAreaName())
		{
			throw new BusinessException("地区为空");
		}
		userReceiveVO.setAreaName(userReceiveVO.getAreaName().trim());
		if (null == userReceiveVO.getAmount())
		{
			throw new BusinessException("领取数量为空");
		}

		List<TItemsArea> tItemsAreas = itemService.queryAreaByName(userReceiveVO.getAreaName());
		if (CollectionUtils.isEmpty(tItemsAreas))
		{
			throw new BusinessException("地区不存在");
		}

		Object o = itemService.queryRedisCache(userReceiveVO.getCode());
		if (null == o)
		{
			throw new BusinessException("二维码已过期");
		}
		UserReceiveVO userReceiveVOCache = (UserReceiveVO) o;

		TItems tItems = itemService.queryItemById(userReceiveVOCache.getItemId());
		if (null == tItems)
		{
			throw new BusinessException("物品不存在");
		}

		List<TItemsArea> itemsAreas = tItemsAreas.stream().filter(v -> userReceiveVOCache.getItemId().equals(v.getItemId()))
				.collect(Collectors.toList());
		if (CollectionUtils.isEmpty(itemsAreas))
		{
			String msg = String.format("%s地区不存在%s物品", userReceiveVO.getAreaName(), tItems.getItem());
			throw new BusinessException(msg);
		}

		/*if (!area.getItemId().equals(userReceiveVOCache.getItemId()))
		{
			throw new BusinessException("领取物品与发放物品不匹配");
		}*/

		userReceiveVO.setAreaId(itemsAreas.get(0).getId());
		userReceiveVO.setItemId(userReceiveVOCache.getItemId());

		if (null == userReceiveVO.getItemSpecId())
		{
			List<Long> specIds = itemService.querySpecIdsByItemId(userReceiveVO.getItemId());
			userReceiveVO.setItemSpecId(specIds.get(0));
		}

		// 是否有发放权限
		boolean haveAuth = itemService.checkGiveOutAuth(UserInfoBag.get().getUserId());
		if (!haveAuth)
		{
			throw new BusinessException("不具有发放权限");
		}

		// 校验领取上限
		verifyLimit(userReceiveVOCache.getItemId(), userReceiveVOCache.getUserId(), userReceiveVO.getAmount());

		// 检查库存
		int stock = itemService.queryStock(userReceiveVO.getAreaId(), userReceiveVO.getItemSpecId());
		if (stock < userReceiveVO.getAmount())
		{
			throw new BusinessException("库存不足，请补充库存");
		}

		// 形成领取记录、扣库存、清缓存
		itemService.giveOut(userReceiveVO, stock, userReceiveVO.getCode(), UserInfoBag.get());
	}

	@ApiOperation(notes = "/out/getMobileCode", value = "获取手机验证码")
	@GetMapping(value = "/out/getMobileCode")
	@ResubmitV2(spaceTimeSecond = CommonConstant.SUBMIT_SPACE_TIME)
	public void getMobileCode(@RequestParam(name = "phone") String phone) {
		itemService.getMobileCode(phone);
	}

	@ApiOperation(notes = "/out/checkMobileCode", value = "验证手机验证码")
	@GetMapping(value = "/out/checkMobileCode")
	public String checkMobileCode(@RequestParam(name = "phone") String phone, @RequestParam(name = "code") String code) {
		return itemService.checkMobileCode(phone, code);
	}

	@ApiOperation(notes = "/out/queryReceiveInfoByQRCode", value = "发放人扫码后根据code查询领取信息")
	@GetMapping(value = "/out/queryReceiveInfoByQRCode")
	public UserReceiveVO queryReceiveInfoByQRCodeV2(@RequestParam String code) {
		return queryReceiveInfoByQRCode(code);
	}


	/**
	 * @param userReceiveVO
	 */
	@ApiOperation(notes = "/out/giveOut", value = "填写发放数量，点击“确定发放”")
	@PostMapping(value = "/out/giveOut")
	@ResubmitV2(spaceTimeSecond = CommonConstant.SUBMIT_SPACE_TIME)
	public void giveOutV2(@RequestBody UserReceiveVO userReceiveVO) {

		// 校验参数
		verifyParam(userReceiveVO);

		ItemOutsiderDTO itemOutsiderDTO = ItemOutsider.get();

		List<TItemsArea> tItemsAreas = itemService.queryAreaByName(userReceiveVO.getAreaName());
		if (CollectionUtils.isEmpty(tItemsAreas)) {
			throw new BusinessException("地区不存在");
		}

		Object o = itemService.queryRedisCache(userReceiveVO.getCode());
		if (null == o) {
			throw new BusinessException("二维码已过期");
		}
		UserReceiveVO userReceiveVOCache = (UserReceiveVO) o;

		TItems tItems = itemService.queryItemById(userReceiveVOCache.getItemId());
		if (null == tItems) {
			throw new BusinessException("物品不存在");
		}
		if (!externalGiveOutItemIdList.contains(userReceiveVOCache.getItemId())) {
			throw new BusinessException("二维码错误，你扫的码是:" + tItems.getItem());
		}

		List<TItemsArea> itemsAreas = tItemsAreas.stream().filter(v -> userReceiveVOCache.getItemId().equals(v.getItemId()))
				.collect(Collectors.toList());
		if (CollectionUtils.isEmpty(itemsAreas)) {
			String msg = String.format("%s地区不存在%s物品", userReceiveVO.getAreaName(), tItems.getItem());
			throw new BusinessException(msg);
		}

		// 校验领取上限
		verifyLimit(userReceiveVOCache.getItemId(), userReceiveVOCache.getUserId(), userReceiveVO.getAmount());

		userReceiveVOCache.setAreaId(itemsAreas.get(0).getId());
		userReceiveVOCache.setAreaName(userReceiveVO.getAreaName());
		userReceiveVOCache.setOperator(userReceiveVO.getOperator());
		userReceiveVOCache.setItemSpecId(userReceiveVO.getItemSpecId());
		userReceiveVOCache.setAmount(userReceiveVO.getAmount());

		if (null == userReceiveVOCache.getItemSpecId()) {
			List<Long> specIds = itemService.querySpecIdsByItemId(userReceiveVOCache.getItemId());
			userReceiveVOCache.setItemSpecId(specIds.get(0));
		}

		// 检查库存
		int stock = itemService.queryStock(userReceiveVOCache.getAreaId(), userReceiveVOCache.getItemSpecId());
		if (stock < userReceiveVO.getAmount()) {
			throw new BusinessException("库存不足，请补充库存");
		}

		// 形成领取记录、扣库存、清缓存
		itemService.giveOut(userReceiveVOCache, stock, userReceiveVO.getCode(), itemOutsiderDTO.getPhone());
	}

	/**
	 * 校验领取上限
	 * @param itemId
	 * @param userId
	 */
	private void verifyLimit(Long itemId, String userId, Integer receivedAmount) {
		ReceiveCycleType receiveCycleType = itemService.queryReceiveCycle(itemId);
		Integer maxReceivedCount = itemService.getMaxReceivedCount(itemId, receiveCycleType.getType());
		int received = itemService.queryUserReceivedCount(userId, itemId, receiveCycleType);
		if (maxReceivedCount != null && maxReceivedCount < received + receivedAmount) {
			throw new BusinessException("领取数量超过上限");
		}
	}

	/**
	 * 校验参数
	 */
	private void verifyParam(UserReceiveVO userReceiveVO){
		if (StringUtils.isBlank(userReceiveVO.getOperator())) {
			throw new BusinessException("发放人为空");
		}
		if (userReceiveVO.getOperator().length() > 45) {
			throw new BusinessException("操作人姓名过长");
		}
		if (StringUtils.isBlank(userReceiveVO.getCode())) {
			throw new BusinessException("code为空");
		}
		if (null == userReceiveVO.getAreaName()) {
			throw new BusinessException("地区为空");
		}
		userReceiveVO.setAreaName(userReceiveVO.getAreaName().trim());
		if (null == userReceiveVO.getAmount()) {
			throw new BusinessException("数量为空");
		}
		if (userReceiveVO.getAmount() <= 0) {
			throw new BusinessException("数量必须大于0");
		}
	}

	/**
	 * 获取企微js签名
	 * @param url
	 * @return
	 */
	@ApiOperation(notes = "/out/api/js/sign", value = "获取企微js签名")
	@GetMapping("/out/api/js/sign")
	public JsSignVO getJsSign(@ApiParam("RequestBody") @RequestParam String url){

		JsSignVO jsSignVO = new JsSignVO();

		try {
			String nonceStr = JsSdkSignUtils.generateRandomString();
			Long timestamp = System.currentTimeMillis() / 1000;
			String jsApiTicket = getJsApiTicket();
			jsSignVO.setSignature(JsSdkSignUtils.getSign(url, jsApiTicket, timestamp, nonceStr));
			jsSignVO.setCorpId(ItemConstant.ENTERPRISE_WECHAT_CORP_ID);
			jsSignVO.setTimestamp(timestamp);
			jsSignVO.setNonceStr(nonceStr);
		} catch (NoSuchAlgorithmException e) {
			throw new BusinessException("签名获取失败");
		}
		return jsSignVO;
	}


	/**
	 * 获取jsApiTicket
	 * @return
	 */
	private String getJsApiTicket() {
		String key = ItemConstant.TICKET_CACHE_PREFIX;
		Object obj = redisClient.get(key);
		String ticket;
		if (obj == null) {
			JSONObject jsonObject = wechatService.sendRequest(WechatAppEnum.ITEM.getCode(), CommonConstant.METHOD_GET, ItemConstant.ENTERPRISE_WECHAT_JSAPI_TICKET_URL_PRE, "");
			log.info("获取到的jsApiTicket:{}", jsonObject.toJSONString());
			if (jsonObject != null && jsonObject.getInteger("errcode") == 0) {
				ticket = jsonObject.getString("ticket");
				redisClient.set(key, ticket, 3600);
				return ticket;
			}
			throw new BusinessException("jsApiTicket获取失败");
		}
		return (String) obj;
	}


	@ApiOperation(notes = "/out/queryAreas", value = "外部人员查询领取地区")
	@GetMapping(value = "/out/queryAreas")
	public List<TItemsArea> queryAreasV2(@RequestParam(required = false) Long departmentId) {
		if (null == departmentId) {
			departmentId = ItemConstant.DEFAULT_DEPARTMENT_ID;
		}
		List<TItems> tItems = itemService.queryItemsByDeptId(departmentId);
		List<Long> itemIdList = tItems.stream().map(TItems::getId).filter(itemId -> externalGiveOutItemIdList.contains(itemId)).collect(Collectors.toList());
		List<TItemsArea> tItemsAreas = itemService.queryAreasByItemIds(itemIdList);
		List<TItemsArea> result = new ArrayList<>();
		List<String> areaNames = new ArrayList<>();
		for (TItemsArea tItemsArea : tItemsAreas) {
			// 地区名称去重
			if (areaNames.contains(tItemsArea.getArea())) {
				continue;
			}
			areaNames.add(tItemsArea.getArea());
			result.add(tItemsArea);
		}
		return result;
	}
}
