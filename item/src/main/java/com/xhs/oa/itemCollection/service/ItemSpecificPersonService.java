package com.xhs.oa.itemCollection.service;

import com.xhs.oa.itemCollection.model.TItemsSpecificPerson;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/19  2:15 下午
 */
public interface ItemSpecificPersonService {
    /**
     *  查询物资配置人员（如果没有，则全员可见）
     */
    List<TItemsSpecificPerson> queryUserByItemId(Integer itemId);

    /**
     *  批量插入
     */
    int batchInsert(List<TItemsSpecificPerson> list);

    /**
     *  通过文件获取可见人员
     */
    List<String> getFileUserToItem(MultipartFile file);


}
