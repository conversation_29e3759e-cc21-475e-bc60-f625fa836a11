package com.xhs.oa.itemCollection.mapper;

import com.xhs.oa.itemCollection.model.TItemsSpecificPerson;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 
 * <br>
 * <b>功能：</b>TItemsStockLogMapper 类<br>
 * <b>日期：</b> 2020-02-04 11:05:03 <br>
 */
public interface TItemsSpecificPersonMapper {

    /**
     * 通过物资id查询可建人员
     * @param itemId 物资id
     */
    List<TItemsSpecificPerson> queryUserByItemId(@Param("itemId") Integer itemId);

    /**
     * 批量插入
     */
    int batchInsert(@Param("list") List<TItemsSpecificPerson> list);
}
