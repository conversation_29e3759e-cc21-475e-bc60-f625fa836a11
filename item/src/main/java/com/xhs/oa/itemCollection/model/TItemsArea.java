package com.xhs.oa.itemCollection.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * <br>
 * <b>功能：</b>TItemsArea 实体类<br>
 * <b>日期：</b> 2020-02-04 11:05:01 <br>
 */
@Data
@SuppressWarnings("serial")
public class TItemsArea implements Serializable {
	
	/**
	 * 自增地区id
	 */
	private Long id;
	
	/**
	 * 地区名称
	 */
	private String area;

	private Long itemId;
	
	/**
	 * 是否有效：0无效，1有效
	 */
	private Integer isValid;
	
	/**
	 * 操作人id
	 */
	private String operatorNo;
	
	/**
	 * 操作人名称
	 */
	private String operator;
	
	/**
	 * 操作时间
	 */
	private Date operateTime;
	

}

