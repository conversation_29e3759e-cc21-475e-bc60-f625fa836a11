package com.xhs.oa.itemCollection.mapper;

import com.xhs.oa.itemCollection.model.TItemsStockLog;
import com.xhs.oa.itemCollection.param.DownloadParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 
 * <br>
 * <b>功能：</b>TItemsStockLogMapper 类<br>
 * <b>日期：</b> 2020-02-04 11:05:03 <br>
 */
public interface TItemsStockLogMapper {

    /**
     * 主键查询数据
     */
    TItemsStockLog selectByPrimaryKey(Long id);

    /**
     * 新增数据
     */
    int insert(TItemsStockLog record);


    List<TItemsStockLog> selectByUserIdAndDate(@Param("paramsMap") Map<String, Object> map);

    /**
     * 分页查询领取记录
     */
    List<TItemsStockLog> queryPagedReceiveRecord(@Param("paramsMap") Map<String, Object> map);

    /**
     * 分页查询物品记录
     */
    List<TItemsStockLog> queryLogs(DownloadParam param);

    /**
     * 分页查询物品记录总数
     */
    int queryPagedLogCounts(DownloadParam param);
}
