package com.xhs.oa.itemCollection.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 新增地区库存
 */
@Data
public class AreaStockParam {

	@ApiModelProperty("地区id-删除用")
	private Long areaId;

	@ApiModelProperty("部门id")
	@NotNull(message = "部门id不能为空")
	private Long departmentId;

	@ApiModelProperty("地区名称")
	@NotBlank(message = "地区名称不能为空")
	private String areaName;

	@ApiModelProperty("物品名称")
	@NotNull(message = "物品名称不能为空")
	private String itemName;

	@ApiModelProperty("规格库存")
	@NotNull(message = "库存不能为空")
	@Size(min = 1, message = "数量最少为1")
	@Valid
	private List<SpecStockParam> specStockParams;

	@ApiModelProperty("用户id集合")
	private List<String> userIds;

	@ApiModelProperty("物资领取员工类型。0 内部员工 1 外部人员 2 实习生 9 全员")
	private List<String> itemEmployeeType;

	@ApiModelProperty("是否开启弹窗提示")
	private Boolean popUpMessageStatus;

	@ApiModelProperty("标题")
	private String headline;

	@ApiModelProperty("弹窗内容")
	private String popUpContent;
}
