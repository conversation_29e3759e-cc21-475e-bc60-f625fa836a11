package com.xhs.oa.itemCollection.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * <br>
 * <b>功能：</b>TItems 实体类<br>
 * <b>日期：</b> 2020-02-04 11:05:01 <br>
 */
@Data
@SuppressWarnings("serial")
public class TItems implements Serializable {
	
	/**
	 * 自增物品id
	 */
	private Long id;
	
	/**
	 * 物品名称
	 */
	private String item;

	private Long departmentId;

	/**
	 * 是否有效：0无效，1有效
	 */
	private Integer isValid;

	/**
	 * '物资领取员工类型。0 内部员工 1 外部人员 2 实习生 9 全员'
	 */
	private String itemEmployeeType;
	
	/**
	 * 操作人id
	 */
	private String operatorNo;
	
	/**
	 * 操作人名称
	 */
	private String operator;
	
	/**
	 * 操作时间
	 */
	private Date operateTime;

	/**
	 * "是否开启弹窗提示"
	 */
	private Boolean popUpMessageStatus;

	/**
	 * "弹窗标题"
	 */
	private String headline;

	/**
	 * 弹窗内容
	 */
	private String popUpContent;
}

