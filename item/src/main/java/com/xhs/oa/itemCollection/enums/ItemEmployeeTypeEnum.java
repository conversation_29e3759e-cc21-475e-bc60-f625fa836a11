package com.xhs.oa.itemCollection.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022/12/19  2:41 下午
 */
@Getter
public enum ItemEmployeeTypeEnum {
//    0;内部员工 1 外部人员 2 实习生 9 全员'
    inside("0","内部员工"),
    epibolies("1","外部人员"),
    trainee("2","实习生"),
    all("9","全员"),;

    private String code;
    private String value;

    ItemEmployeeTypeEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }
}
