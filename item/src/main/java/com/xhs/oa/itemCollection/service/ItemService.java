package com.xhs.oa.itemCollection.service;

import com.alibaba.fastjson.JSONObject;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.github.pagehelper.PageHelper;
import com.xhs.cache.RedisClient;
import com.xhs.finance.exception.BusinessException;
import com.xhs.finance.sso.model.UserInfo;
import com.xhs.finance.utils.DateUtil;
import com.xhs.oa.common.service.ApolloCommonConfigService;
import com.xhs.oa.expense.vo.DatePairVo;
import com.xhs.oa.form.service.RbacxClientService;
import com.xhs.oa.helper.AssertHelper;
import com.xhs.oa.itemCollection.constant.ItemConstant;
import com.xhs.oa.itemCollection.dto.ItemOutsiderDTO;
import com.xhs.oa.itemCollection.dto.ItemReceivedMsgDTO;
import com.xhs.oa.itemCollection.dto.ItemSpecDTO;
import com.xhs.oa.itemCollection.enums.ItemEmployeeTypeEnum;
import com.xhs.oa.itemCollection.enums.OperateType;
import com.xhs.oa.itemCollection.enums.ReceiveCycleType;
import com.xhs.oa.itemCollection.mapperProcessor.TItemsMapperProcessor;
import com.xhs.oa.itemCollection.model.*;
import com.xhs.oa.itemCollection.mq.producer.ItemProducer;
import com.xhs.oa.itemCollection.vo.UserReceiveVO;
import com.xhs.oa.rpc.param.SmsSendParam;
import com.xhs.oa.rpc.service.SmsService;
import com.xhs.oa.workflow.model.ActIdUser;
import com.xhs.oa.workflow.service.UserService;
import com.xhs.oa.workflow.vo.UserVo;
import com.xhs.rbac.client.dto.v2.AccountDto;
import com.xhs.rbac.client.service.v2.IRbacClientCoreService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ItemService
{
	@Autowired
	private TItemsMapperProcessor itemsMapperProcessor;

	@Autowired
	private UserService userService;

	@Autowired
	private RedisClient redisClient;

	@Autowired
	private ApolloCommonConfigService apolloCommonConfigService;

	@Autowired
	private IRbacClientCoreService rbacClientCoreService;

	@Autowired
	private RbacxClientService rbacxClientService;

	@Autowired
	private ItemSpecificPersonService itemSpecificPersonService;

	@Autowired
	private SmsService smsService;

	@Autowired
	private ItemProducer itemProducer;

	/**
	 * 手机号国家码前缀
	 */
	@ApolloJsonValue("${xhsoa_phone_nation_code_prefix:+86}")
	private String phoneNationCodePrefix;

	/**
	 * 周期内最大可领取数量
	 */
	@ApolloJsonValue("${item_receive_max_amount:{}}")
	private Map<String, Map<String, Integer>> receiveMaxAmountMap;

	/**
	 * 二维码过期时间
	 */
	@ApolloJsonValue("${item_qr_code_expire_time:600}")
	private Long qrCodeExpireSeconds;


	private static final String QR_CODE_PREFIX = "item_";

	private static final String QR_CODE_RECEIVED_PREFIX = "item_received_";

	private static final String QR_CODE_USER_PREFIX = "item_user_";

	/**
	 * 短信验证码失效时间
	 */
	private static final Long PHONE_CODE_EXPIRE_MINUTES = 5L;
	/**
	 * 手机登录session有效时间
	 */
	private static final int PHONE_LOGIN_TOKEN_EXPIRE_SECONDS = 7 * 24 * 60 * 60;

	/**
	 * 手机登录session前缀
	 */
	private static final String PHONE_LOGIN_TOKEN_PREFIX = "item_phone_token_";
	/**
	 * 手机验证码模板ID
	 */
	private static final int PHONE_CODE_SMS_TEMPLATE_ID = 466;

	/**
	 * 每两周领一次，缓存本次起始时间
	 */
	private static final String CURRENT_TWO_WEEK = "oa_item_current_two_week_key";

	/**
	 * 手机验证码
	 */
	private static final String PHONE_CODE_KEY = "oa_item_phone_code_key_";


	/**
	 * 获取缓存
	 */
	public Object queryRedisCache(String code)
	{
		return redisClient.get(QR_CODE_PREFIX + code);
	}

	/**
	 * 查找第一个物品
	 */
	public TItems queryFirstItem()
	{
		return itemsMapperProcessor.selectFirstItem();
	}

	/**
	 * 查找第一个物品的第一个规格
	 */
	public TItemsSpec queryFirstItemSpec()
	{
		return itemsMapperProcessor.selectFirstItemSpec();
	}

	public Boolean checkGiveOutAuth(String userId)
	{
		List<AccountDto> accountDtos = rbacClientCoreService.listAllUserByRole(rbacxClientService.getRbacApiCallMetaInfo("xhsoa", "xhsoa"), ItemConstant.ITEM_AUTH_USERS);
		List<String> emails = accountDtos.stream().map(AccountDto::getEmail).collect(Collectors.toList());
		String email = userService.findEmailById(userId);
		return emails.contains(email);
	}

	/**
	 * 判断用户是否可以领取{itemId}物资
	 * @param itemId 物资ID
	 * @param userId 用户ID
	 * @return true-可见（可领取），false-不可见（不能领取）
	 */
	public boolean receivable(Long itemId, String userId) {
		List<TItemsSpecificPerson> list = itemSpecificPersonService.queryUserByItemId(itemId.intValue());
		List<String> userIds = list.stream().map(TItemsSpecificPerson::getUserId).distinct().collect(Collectors.toList());
		TItems tItem = queryItemById(itemId);
		ActIdUser user = userService.selectById(userId);
		if (Objects.isNull(tItem)) {
			log.info("获取物资失败,itemId：{}", itemId);
			throw new BusinessException("获取物资失败");
		}
		//没有指定物资，判断员工类型是否可见
		if (CollectionUtils.isEmpty(list)) {
			String itemEmployeeType = tItem.getItemEmployeeType();
			String[] split = itemEmployeeType.split(",");
			List<String> itemEmployeeTypes = Arrays.asList(split);
			return itemEmployeeTypes.contains(ItemEmployeeTypeEnum.all.getCode()) ||
					itemEmployeeTypes.contains(String.valueOf(user.getEmployType()));
		}
		//指定人员物资，直接判断
		return userIds.contains(String.valueOf(user.getId()));
	}

	//转换map
	private Map<Long, List<String>> handlerMap(Map<String, List<String>> itemShowFilter){
		Map<Long, List<String>> map = new HashMap<>();
		if(itemShowFilter == null || itemShowFilter.isEmpty()){
			return map;
		}
		for (Map.Entry<String, List<String>> entry : itemShowFilter.entrySet())
		{
			List<Long> list = Arrays.stream(entry.getKey().split(",")).map(v -> Long.valueOf(v)).collect(Collectors.toList());
			for (Long lo : list)
			{
				if(map.containsKey(lo)){
					map.get(lo).addAll(entry.getValue());
				}else{
					map.put(lo,entry.getValue());
				}
			}
		}
		return map;
	}

	/**
	 * 根据地区id查询物品的默认规格
	 */
	public TItemsSpec queryDefaultItemSpc(Long itemId)
	{
		List<TItemsSpec> tItemsSpecs = itemsMapperProcessor.selectSpecByItemId(itemId);
		return tItemsSpecs.get(0);
	}

	/**
	 * 根据部门id查询物品
	 */
	public List<TItems> queryItemsByDeptId(Long departmentId)
	{
		return itemsMapperProcessor.queryItemsByDeptId(departmentId);
	}

	/**
	 * 批量查询物品
	 */
	public List<TItems> queryItemsByIds(List<Long> itemIds)
	{
		if (CollectionUtils.isEmpty(itemIds))
		{
			return new ArrayList<>();
		}
		return itemsMapperProcessor.queryItemsByIds(itemIds);
	}

	public List<ItemSpecDTO> queryItemSpecNameByItemIds(List<Long> itemIds)
	{
		if (CollectionUtils.isEmpty(itemIds))
		{
			return new ArrayList<>();
		}
		return itemsMapperProcessor.queryItemSpecNameByItemIds(itemIds);
	}

	public List<ItemSpecDTO> queryItemSpecNameBySpecIds(List<Long> specIds)
	{
		if (CollectionUtils.isEmpty(specIds))
		{
			return new ArrayList<>();
		}
		return itemsMapperProcessor.queryItemSpecNameBySpecIds(specIds);
	}

	/**
	 * 根据部门id和物品名称查询物品
	 */
	public List<TItems> queryItemsByDeptIdAndItemName(Long departmentId, String itemName)
	{
		return itemsMapperProcessor.queryItemsByDeptIdAndItemName(departmentId, itemName);
	}

	/**
	 * 查询物品规格的单位
	 */
	public String queryUnit(Long itemId)
	{
		List<TItemsSpec> tItemsSpecs = this.querySpecs(itemId);
		return tItemsSpecs.get(0).getUnit();
	}

	/**
	 * 查询用户领用数量
	 */
	public Integer queryUserReceivedCount(String userId, Long itemId, ReceiveCycleType receiveCycleType)
	{
		Date startTime;
		Date endTime;
		Date now = new Date();
		switch (receiveCycleType)
		{
		case DAILY:
			startTime = DateUtil.startOfDay(now);
			endTime = DateUtil.endOfDay(now);
			break;
		case WEEKLY:
			startTime = com.xhs.oa.common.util.DateUtil.startOfWeek(now);
			endTime = com.xhs.oa.common.util.DateUtil.endOfWeek(now);
			break;
		case WEEKLY_FROM_SUNDAY:
			startTime = com.xhs.oa.common.util.DateUtil.startOfWeekFromSunday(now);
			endTime = com.xhs.oa.common.util.DateUtil.endOfWeekFromSunday(now);
			break;
		case BIWEEKLY:
			// 周日为一周的第一天
			DatePairVo biweekly = getBiweekly(now);
			startTime = biweekly.getStart();
			endTime = biweekly.getEnd();
			break;
		case MONTHLY:
			startTime = com.xhs.oa.common.util.DateUtil.startOfMonth(now);
			endTime = com.xhs.oa.common.util.DateUtil.endOfMonth(now);
			break;
		case QUARTER:
			startTime = com.xhs.oa.common.util.DateUtil.startOfQuarter(now);
			endTime = com.xhs.oa.common.util.DateUtil.endOfQuarter(now);
			break;
		case PERPETUAL:
			startTime = null;
			endTime = null;
			break;
		default:
			throw new BusinessException("未知领取周期");
		}
		List<Long> itemSpecIds = this.querySpecIdsByItemId(itemId);
		Map<String, Object> paramsMap = new HashMap<>();
		paramsMap.put("userId", userId);
		paramsMap.put("startTime", startTime);
		paramsMap.put("endTime", endTime);
		paramsMap.put("specIds", itemSpecIds);
		paramsMap.put("operateType", OperateType.RECEIVE.getType());
		List<TItemsStockLog> tItemsStockLogs = itemsMapperProcessor.selectByUserIdAndDate(paramsMap);
		int count = tItemsStockLogs.stream().map(TItemsStockLog::getAmount).mapToInt(i->i).sum();
		return count;
	}

	private DatePairVo getBiweekly(Date date)
	{
		DatePairVo datePairVo;
		Object object = redisClient.get(CURRENT_TWO_WEEK);
		if (null != object) {
			String jsonString = (String) object;
			datePairVo = JSONObject.parseObject(jsonString, DatePairVo.class);
		} else {
			Date startTime = com.xhs.oa.common.util.DateUtil.startOfWeekFromSunday(date);
			Date endTime = DateUtil.endOfDay(com.xhs.oa.common.util.DateUtil.lastDayOfNextWeek(date));
			datePairVo = new DatePairVo();
			datePairVo.setStart(startTime);
			datePairVo.setEnd(endTime);
			// 过期时间：当前时间距离endTime
			long gap = (endTime.getTime() - date.getTime()) / 1000;
			redisClient.set(CURRENT_TWO_WEEK, JSONObject.toJSONString(datePairVo), gap);
		}
		return datePairVo;
	}

	/**
	 * 查询领用的二维码code
	 */
	public String queryUserQRCode(String userId, Long itemId)
	{
		// 增加是否可领取校验
		AssertHelper.check(receivable(itemId, userId), "该物品您不能领取哦～");

		Random random = new Random();
		String rand = String.valueOf(random.nextInt(900) + 100);
		java.sql.Date date = new java.sql.Date(new Date().getTime());
		String code = String.join("-", userId, itemId.toString(), date.toString(), rand);

		ActIdUser actIdUser = userService.selectById(userId);
		UserReceiveVO userReceiveVO = new UserReceiveVO();
		userReceiveVO.setUserId(userId);
		userReceiveVO.setDate(date);
		userReceiveVO.setItemId(itemId);
		userReceiveVO.setRedName(actIdUser.getRedNameOrFirst());

		// 将领取信息（领取人、领用物品id、领取日期）存入redis，半小时过期
		String key = QR_CODE_PREFIX + code;
		redisClient.set(key, userReceiveVO, qrCodeExpireSeconds);

		// 失效旧二维码
		Object obj = redisClient.get(QR_CODE_USER_PREFIX + itemId + "_" + userId);
		if (obj != null) {
			String oldCode = (String) obj;
			redisClient.remove(QR_CODE_PREFIX + oldCode);
		}
		redisClient.set(QR_CODE_USER_PREFIX + itemId + "_" + userId, code, qrCodeExpireSeconds);

		return code;
	}

	public List<Long> querySpecIdsByItemId(Long itemId)
	{
		// 查询物品所有规格
		List<TItemsSpec> itemSpecs = this.querySpecs(itemId);
		return itemSpecs.stream().map(TItemsSpec::getId).collect(Collectors.toList());
	}

	public List<Long> querySpecIdsByItemIds(List<Long> itemIds)
	{
		if (CollectionUtils.isEmpty(itemIds))
		{
			return new ArrayList<>();
		}
		// 批量查询物品所有规格
		List<TItemsSpec> itemSpecs = this.querySpecsByItemIds(itemIds);
		return itemSpecs.stream().map(TItemsSpec::getId).collect(Collectors.toList());
	}

	/**
	 * 查询规格
	 */
	public List<TItemsSpec> querySpecs(Long itemId)
	{
		return itemsMapperProcessor.selectSpecByItemId(itemId);
	}

	/**
	 * 批量查询规格
	 */
	public List<TItemsSpec> querySpecsByItemIds(List<Long> itemIds)
	{
		if (CollectionUtils.isEmpty(itemIds))
		{
			return new ArrayList<>();
		}
		return itemsMapperProcessor.selectSpecByItemIds(itemIds);
	}

	/**
	 * 分页查询领取记录
	 */
	public List<TItemsStockLog> queryPagedReceiveRecord(List<Long> itemIds, Integer pageNum, Integer pageSize, String userId)
	{
		return queryItemsStockLogs(itemIds, pageNum, pageSize, userId, "userId");
	}

	/**
	 * 分页查询发放记录
	 */
	public List<TItemsStockLog> queryPagedGiveOutRecord(List<Long> itemIds, Integer pageNum, Integer pageSize, String operatorId)
	{
		return queryItemsStockLogs(itemIds, pageNum, pageSize, operatorId, "operatorId");
	}

	private List<TItemsStockLog> queryItemsStockLogs(List<Long> itemIds, Integer pageNum, Integer pageSize, String userId,
			String userIdType)
	{
		if (CollectionUtils.isEmpty(itemIds))
		{
			return new ArrayList<>();
		}
		List<Long> itemSpecIds = this.querySpecIdsByItemIds(itemIds);
		Map<String, Object> paramsMap = new HashMap<>();
		paramsMap.put(userIdType, userId);
		paramsMap.put("itemSpecIds", itemSpecIds);
		paramsMap.put("operateType", OperateType.RECEIVE.getType());

		PageHelper.startPage(pageNum, pageSize);

		return itemsMapperProcessor.queryPagedReceiveRecord(paramsMap);
	}

	/**
	 * 根据地区id查询地区
	 */
	public TItemsArea queryAreaById(Long areaId)
	{
		return itemsMapperProcessor.queryAreaById(areaId);
	}

	/**
	 * 根据地区名查询地区
	 */
	public List<TItemsArea> queryAreaByName(String area)
	{
		return itemsMapperProcessor.queryAreaByName(area);
	}

	/**
	 * 根据地区id查询地区
	 */
	public TItems queryItemById(Long itemId)
	{
		return itemsMapperProcessor.queryItemsById(itemId);
	}

	/**
	 * 查询地区id
	 */
	public List<Long> queryAreaIdsByItemIds(List<Long> itemIds)
	{
		if (CollectionUtils.isEmpty(itemIds))
		{
			return new ArrayList<>();
		}
		List<TItemsArea> tItemsAreas = this.queryAreasByItemIds(itemIds);
		return tItemsAreas.stream().map(TItemsArea::getId).collect(Collectors.toList());
	}

	/**
	 * 根据物品查询地区
	 */
	public List<TItemsArea> queryAreasByItemIds(List<Long> itemIds)
	{
		if (CollectionUtils.isEmpty(itemIds))
		{
			return new ArrayList<>();
		}
		return itemsMapperProcessor.queryAreasByItemIds(itemIds);
	}

	/**
	 * 根据地区和规格查库存
	 */
	public Integer queryStock(Long areaId, Long specId)
	{
		return itemsMapperProcessor.queryStock(areaId, specId);
	}

	/**
	 * 生成领取记录、扣库存、清缓存
	 */
	@Transactional(propagation = Propagation.REQUIRED, transactionManager = "oaTransactionManager")
	public void giveOut(UserReceiveVO userReceiveVO, Integer stock, String code, UserInfo userInfo)
	{
		giveOut(userReceiveVO, stock, code, userInfo, null);
	}


	@Transactional(propagation = Propagation.REQUIRED, transactionManager = "oaTransactionManager")
	public void giveOut(UserReceiveVO userReceiveVO, Integer stock, String code, String phone) {
		giveOut(userReceiveVO, stock, code, null, phone);
	}

	/**
	 * 生成领取记录、扣库存、清缓存
	 * @param userReceiveVO
	 * @param stock
	 * @param code
	 * @param userInfo
	 * @param phone
	 */
	private void giveOut(UserReceiveVO userReceiveVO, Integer stock, String code, UserInfo userInfo, String phone) {
		String operatorName = null;
		String operatorNo = null;
		if (userInfo == null) {
			operatorName = userReceiveVO.getOperator();
		} else {
			operatorName = userInfo.getName();
			operatorNo = userInfo.getUserId();
		}

		Long recordId = this.insertReceiveRecord(userReceiveVO, userInfo, phone);

		Map<String, Object> paramsMap = new HashMap<>();
		paramsMap.put("areaId", userReceiveVO.getAreaId());
		paramsMap.put("specId", userReceiveVO.getItemSpecId());
		paramsMap.put("amount", userReceiveVO.getAmount());
		paramsMap.put("stock", stock);
		paramsMap.put("operatorPhone", phone);
		paramsMap.put("operatorNo", operatorNo);
		paramsMap.put("operator", operatorName);
		if (itemsMapperProcessor.queryStock(userReceiveVO.getAreaId(), userReceiveVO.getItemSpecId()) < 999999999) {
			int affectedCount = itemsMapperProcessor.decreaseStock(paramsMap);
			if (affectedCount != 1) {
				log.error("库存更新失败：{}", JSONObject.toJSONString(userReceiveVO));
				throw new BusinessException("库存更新失败");
			}
		}

		// 发送MQ
		ItemReceivedMsgDTO msg = new ItemReceivedMsgDTO();
		msg.setItemId(userReceiveVO.getItemId());
		msg.setUserId(userReceiveVO.getUserId());
		msg.setOperatorId(operatorNo);
		msg.setOperatorPhone(phone);
		msg.setOperatorName(operatorName);
		msg.setReceiveTime(new Date());
		msg.setAreaName(userReceiveVO.getAreaName());
		msg.setAmount(userReceiveVO.getAmount());
		msg.setUniqueId(recordId);
		itemProducer.send(msg);

		// 已领取打标
		redisClient.set(QR_CODE_RECEIVED_PREFIX + code, userReceiveVO, qrCodeExpireSeconds);
		redisClient.remove(QR_CODE_PREFIX + code);

	}

	public Long insertReceiveRecord(UserReceiveVO userReceiveVO, UserInfo userInfo, String phone)
	{
		TItemsStockLog record = new TItemsStockLog();
		record.setOperateType(OperateType.RECEIVE.getType());
		record.setUserId(userReceiveVO.getUserId());
		record.setItemAreaId(userReceiveVO.getAreaId());
		record.setItemSpecId(userReceiveVO.getItemSpecId());
		record.setAmount(userReceiveVO.getAmount());
		record.setReason("");
		if (userInfo != null) {
			record.setOperator(userInfo.getName());
			record.setOperatorNo(userInfo.getUserId());
		} else {
			record.setOperatorPhone(phone);
			record.setOperator(userReceiveVO.getOperator());
		}
		itemsMapperProcessor.insertStockLog(record);
		return record.getId();
	}

	/**
	 * 获取发放周期
	 */
	public ReceiveCycleType queryReceiveCycle(Long itemId)
	{
		Map<String, String> cycleMap = apolloCommonConfigService.getMapDataByKey(ItemConstant.RECEIVE_CYCLE_KEY);
		String cycle = cycleMap.get(itemId.toString());
		ReceiveCycleType receiveCycleType = ReceiveCycleType.getEnumByType(cycle);
		if (null == receiveCycleType)
		{
			return ReceiveCycleType.DAILY;
		}
		return receiveCycleType;
	}

	/**
	 * 获取物品的默认领取数量
	 */
	public Integer queryReceiveDefaultAmount(Long itemId)
	{
		Map<String, String> defaultAmountMap = apolloCommonConfigService.getMapDataByKey(ItemConstant.RECEIVE_DEFAULT_AMOUNT_KEY);
		String defaultAmountStr = defaultAmountMap.get(itemId.toString());
		if (StringUtils.isBlank(defaultAmountStr))
		{
			return 1;
		}
		return Integer.valueOf(defaultAmountStr);
	}

	/**
	 * 发送手机验证码
	 * @param phone
	 * @return
	 */
	public void getMobileCode(String phone) {
		String key = PHONE_CODE_KEY + phone;
		Object obj = redisClient.get(key);
		if (obj != null) {
			String value = (String) obj;
			String time = value.split("_")[1];
			if (System.currentTimeMillis() - Long.parseLong(time) < 60 * 1000) {
				throw new BusinessException("短信已发送，请勿重复提交");
			}
		}
		// 生成验证码
		String code = genCode(6);

		// 发送短信
		SmsSendParam param = new SmsSendParam();
		phone = phone.startsWith("+") ? phone : (phoneNationCodePrefix + phone);
		param.setPhoneNumber(phone);
		param.setTemplateId(PHONE_CODE_SMS_TEMPLATE_ID);
		List<String> params = new ArrayList<>();
		params.add(code);
		params.add(String.valueOf(PHONE_CODE_EXPIRE_MINUTES));
		param.setParams(params);
		smsService.sendTemplateSms(param);

		redisClient.set(key, code + "_" + System.currentTimeMillis(), PHONE_CODE_EXPIRE_MINUTES * 60);
	}

	/**
	 * 验证手机验证码，返回token
	 * @param phone
	 * @param code
	 * @return
	 */
	public String checkMobileCode(String phone, String code) {
		String key = PHONE_CODE_KEY + phone;
		Object obj = redisClient.get(key);
		if (obj != null) {
			String value = (String) obj;
			String c = value.split("_")[0];
			if (!Objects.equals(code, c)) {
				throw new BusinessException("验证码错误");
			}
		} else {
			throw new BusinessException("验证码已失效");
		}

		// 生成token
		String token = UUID.randomUUID().toString().replace("-", "");
		ItemOutsiderDTO itemOutsiderDTO = new ItemOutsiderDTO();
		itemOutsiderDTO.setPhone(phone);
		redisClient.set(PHONE_LOGIN_TOKEN_PREFIX + token, itemOutsiderDTO, PHONE_LOGIN_TOKEN_EXPIRE_SECONDS);
		redisClient.remove(key);
		return token;
	}

	/**
	 * 生产数字验证码
	 * @param length
	 * @return
	 */
	private String genCode(int length){
		StringBuilder sb = new StringBuilder();
		for (int i = 0; i < length; i++) {
			sb.append(RandomUtils.nextInt(0,9));
		}
		return sb.toString();
	}

	/**
	 * 最大可领取数量
	 * @param itemId
	 * @param receiveCycleType
	 * @return
	 */
	public Integer getMaxReceivedCount(Long itemId, String receiveCycleType) {
		Map<String , Integer> receiveCyclemap = receiveMaxAmountMap.get(String.valueOf(itemId));
		if (receiveCyclemap != null) {
			return receiveCyclemap.get(receiveCycleType);
		}
		return null;
	}

	/**
	 * 校验token
	 * @param token
	 */
	public ItemOutsiderDTO verifyToken(String token) {
		Object obj = redisClient.get(PHONE_LOGIN_TOKEN_PREFIX + token);
		if (obj == null) {
			return null;
		}
		return (ItemOutsiderDTO) obj;
	}

	/**
	 * 近期有过领取行为
	 * @param code
	 * @return
	 */
	public Boolean hasReceivedRecent(String code) {
		if (StringUtils.isBlank(code)) {
			return false;
		}
		return redisClient.get(QR_CODE_RECEIVED_PREFIX + code) != null;
	}
}
