package com.xhs.oa.itemCollection.vo;

import com.xhs.oa.itemCollection.model.TItems;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ItemVO
{
	private Long Id;

	private String item;

	/**
	 * "是否开启弹窗提示"
	 */
	private Boolean popUpMessageStatus;

	/**
	 * "弹窗标题"
	 */
	private String headline;

	/**
	 * 弹窗内容
	 */
	private String popUpContent;

	public static ItemVO toItemVO(TItems tItem){
		ItemVO itemVo = new ItemVO();
		itemVo.setId(tItem.getId());
		itemVo.setItem(tItem.getItem());
		itemVo.setPopUpMessageStatus(tItem.getPopUpMessageStatus());
		itemVo.setHeadline(tItem.getHeadline());
		itemVo.setPopUpContent(tItem.getPopUpContent());
		return itemVo;
	}
}
