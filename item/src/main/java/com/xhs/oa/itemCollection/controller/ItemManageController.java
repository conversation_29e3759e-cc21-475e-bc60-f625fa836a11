package com.xhs.oa.itemCollection.controller;

import com.github.pagehelper.PageInfo;
import com.xhs.finance.exception.BusinessException;
import com.xhs.finance.framework.auth.Authority;
import com.xhs.finance.framework.page.PageResult;
import com.xhs.finance.framework.resubmit.Resubmit;
import com.xhs.finance.sso.UserInfoBag;
import com.xhs.finance.sso.model.UserInfo;
import com.xhs.finance.utils.DateUtil;
import com.xhs.oa.common.constant.CommonConstant;
import com.xhs.oa.common.service.ExportService;
import com.xhs.oa.office.threadPool.ThreadPoolManager;
import com.xhs.oa.common.util.ExcelUtil;
import com.xhs.oa.common.util.ValidUtil;
import com.xhs.oa.itemCollection.constant.ItemConstant;
import com.xhs.oa.itemCollection.dto.AreaStockDTO;
import com.xhs.oa.itemCollection.dto.ItemSpecDTO;
import com.xhs.oa.itemCollection.dto.SpecStockDTO;
import com.xhs.oa.itemCollection.enums.OperateType;
import com.xhs.oa.itemCollection.model.TItems;
import com.xhs.oa.itemCollection.model.TItemsArea;
import com.xhs.oa.itemCollection.model.TItemsSpec;
import com.xhs.oa.itemCollection.model.TItemsStockLog;
import com.xhs.oa.itemCollection.param.AreaStockParam;
import com.xhs.oa.itemCollection.param.DownloadParam;
import com.xhs.oa.itemCollection.param.SpecStockParam;
import com.xhs.oa.itemCollection.param.UpdateStockParam;
import com.xhs.oa.itemCollection.service.ItemManageService;
import com.xhs.oa.itemCollection.service.ItemService;
import com.xhs.oa.itemCollection.service.ItemSpecificPersonService;
import com.xhs.oa.itemCollection.vo.DownloadVO;
import com.xhs.oa.itemCollection.vo.StockVO;
import com.xhs.oa.report.enums.ReportStatusEnum;
import com.xhs.oa.report.enums.SourceTypeEnum;
import com.xhs.oa.report.service.ReportManagerService;
import com.xhs.oa.workflow.model.ActIdUser;
import com.xhs.oa.workflow.service.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;


@RestController
@Slf4j
@Api(value = "/xhs-oa/itemsManage", description = "物品管理相关接口")
@RequestMapping("/xhs-oa/itemsManage")
public class ItemManageController
{

	/**
	 * 1.下载领取明细
	 * 2.库存分页列表
	 * 3.新增、删除地区
	 * 4.调整库存
	 */

	@Autowired
	private ItemService itemService;

	@Autowired
	private ItemManageService itemManageService;

	@Autowired
	private ExportService exportService;

	@Autowired
	private UserService userService;

	@Autowired
	private ReportManagerService reportManagerService;

	@Autowired
	private ItemSpecificPersonService itemSpecificPersonService;

	@ApiOperation(notes = "/queryPagedStock", value = "库存分页列表")
	@GetMapping(value = "/queryPagedStock")
	@Authority(code = "oa_item_stock_manage")
	public PageResult<StockVO> queryPagedMyReceiveRecord(@RequestParam Integer pageNum,
															@RequestParam Integer pageSize,
															@RequestParam(required = false) Long departmentId)
	{
		if (null == departmentId)
		{
			departmentId = ItemConstant.DEFAULT_DEPARTMENT_ID;
		}
		List<TItemsArea> tItemsAreas = itemManageService.queryPagedArea(departmentId, pageNum, pageSize);
		PageInfo<TItemsArea> pageList = new PageInfo<>(tItemsAreas);
		List<AreaStockDTO> areaStockDTOs = itemManageService
				.queryAreaStock(tItemsAreas.stream().map(TItemsArea::getId).collect(Collectors.toList()));
		List<StockVO> stockVOs = this.transformToStockVO(areaStockDTOs);
		PageResult<StockVO> pageResult = new PageResult<>();
		pageResult.setPageNum(pageList.getPageNum());
		pageResult.setPageSize(pageList.getPageSize());
		pageResult.setList(stockVOs);
		pageResult.setTotalPage(pageList.getPages());
		pageResult.setTotal(Integer.valueOf(Long.valueOf(pageList.getTotal()).toString()));
		return pageResult;
	}

	private List<StockVO> transformToStockVO(List<AreaStockDTO> areaStockDTOs)
	{
		List<StockVO> stockVOs = new ArrayList<>();
		for (AreaStockDTO areaStockDTO : areaStockDTOs)
		{
			List<SpecStockDTO> specStockDTOs = areaStockDTO.getSpecStockDTOs();
			int totalStock = 0;
			int leftAmount = 0;
			for (SpecStockDTO specStockDTO : specStockDTOs)
			{
				totalStock += specStockDTO.getTotalStock();
				leftAmount += specStockDTO.getLeftStock();
			}

			StockVO stockVO = new StockVO();
			stockVO.setAreaId(areaStockDTO.getAreaId());
			stockVO.setAreaName(areaStockDTO.getAreaName());
			stockVO.setItemName(areaStockDTO.getItemName());
			stockVO.setTotalStock(totalStock);
			stockVO.setLeftAmount(leftAmount);
			stockVO.setReceivedAmount(totalStock-leftAmount);
			stockVOs.add(stockVO);
		}
		return stockVOs;
	}

	@ApiOperation(notes = "/addAreaStock", value = "新增地区、物品及库存")
	@PostMapping(value = "/addAreaStock")
	@Authority(code = "oa_item_stock_manage")
	@Resubmit(spaceTimeSecond = CommonConstant.SUBMIT_SPACE_TIME)
	public void addAreaStock(@RequestBody AreaStockParam param)
	{
		if (null == param.getDepartmentId())
		{
			param.setDepartmentId(ItemConstant.DEFAULT_DEPARTMENT_ID);
		}
		String errMsg = ValidUtil.validObject(param);
		if (StringUtils.isNotBlank(errMsg))
		{
			throw new BusinessException("参数校验不通过"+errMsg);
		}
		if (BooleanUtils.isTrue(param.getPopUpMessageStatus())){
			if (StringUtils.isBlank(param.getPopUpContent())){
				throw new BusinessException("弹窗内容不能为空");
			}
			if (StringUtils.isBlank(param.getHeadline())){
				throw new BusinessException("弹窗标题不能为空");
			}
		}
		List<SpecStockParam> specStockParams = param.getSpecStockParams();
		if (specStockParams.size() != 1)
		{
			throw new BusinessException("暂不支持物品多规格");
		}
		param.setItemName(param.getItemName().trim());
		param.setAreaName(param.getAreaName().trim());
		boolean exist = itemManageService.queryItemAreaExist(param.getDepartmentId(), param.getItemName(), param.getAreaName());
		if (exist)
		{
			throw new BusinessException("物品地区已存在");
		}
		itemManageService.addAreaStock(param, UserInfoBag.get());
	}

	@ApiOperation(notes = "/deleteArea", value = "删除地区")
	@PostMapping(value = "/deleteArea")
	@Authority(code = "oa_item_stock_manage")
	@Resubmit(spaceTimeSecond = CommonConstant.SUBMIT_SPACE_TIME)
	public void deleteArea(@RequestBody AreaStockParam param)
	{
		if (null == param.getAreaId())
		{
			throw new BusinessException("地区id为空");
		}
		itemManageService.deleteArea(param.getAreaId());
	}

	@ApiOperation(notes = "/updateStock", value = "调整库存")
	@PostMapping(value = "/updateStock")
	@Authority(code = "oa_item_stock_manage")
	@Resubmit(spaceTimeSecond = CommonConstant.SUBMIT_SPACE_TIME)
	public void updateStock(@RequestBody UpdateStockParam param)
	{
		if (param.getAreaId() == null)
		{
			throw new BusinessException("地区id为空");
		}
		if (param.getAmount() == 0)
		{
			return;
		}
		if (param.getAmount() < 0 && StringUtils.isBlank(param.getReason()))
		{
			throw new BusinessException("请填写删减库存的原因");
		}
		TItemsArea area = itemService.queryAreaById(param.getAreaId());
		if (null == area)
		{
			throw new BusinessException("地区不存在");
		}
		if (null == param.getSpecId())
		{
			TItemsSpec tItemsSpec = itemService.queryDefaultItemSpc(area.getItemId());
			param.setSpecId(tItemsSpec.getId());
		}
		itemManageService.updateAreaStock(param, UserInfoBag.get());
	}

	@ApiOperation(notes = "/downloadDetails", value = "下载领取明细")
	@PostMapping(value = "/downloadDetails")
	@Authority(code = "oa_item_stock_manage")
	@Resubmit(spaceTimeSecond = CommonConstant.SUBMIT_SPACE_TIME)
	public String downloadDetails(@RequestBody DownloadParam param)
	{
		this.fillDownloadParam(param);

		String fileName = "物品领取明细_"+ DateUtil.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss") + ".xlsx";

		UserInfo userInfo = UserInfoBag.get();

		Long reportId = reportManagerService.addReportTask(fileName, SourceTypeEnum.ITEM_STOCK_MANAGER.getCode(),userInfo.getUserId(),userInfo.getName());

		ThreadPoolManager.executeThreadTask(() -> {
			try{
				String url = downloadUrl(param, fileName);
				reportManagerService.updateReportStatus(reportId, ReportStatusEnum.FINISHED.getCode(),url);
			}catch (Exception e){
				reportManagerService.updateReportStatus(reportId, ReportStatusEnum.FAILED.getCode(),"");
				log.error("下载领取明细失败",e);
			}
		});
		return "下载成功";
	}

	private String downloadUrl(DownloadParam param, String fileName)
	{
		// 地区id-地区名
		List<TItemsArea> tItemsAreas = itemService.queryAreasByItemIds(param.getItemIds());
		Map<Long, String> areaMap = tItemsAreas.stream().collect(Collectors.toMap(TItemsArea::getId, TItemsArea::getArea));
		// 规格
		List<ItemSpecDTO> itemSpecDTOs = itemService.queryItemSpecNameByItemIds(param.getItemIds());
		Map<Long, ItemSpecDTO> itemSpecDTOMap = itemSpecDTOs.stream().collect(Collectors.toMap(ItemSpecDTO::getSpecId, v->v));

		List<TItemsStockLog> tItemsStockLogs = itemManageService.queryLogs(param);
		List<DownloadVO> downloadVOs = this.transformToDownloadVO(tItemsStockLogs, areaMap, itemSpecDTOMap);
		return exportService.export(downloadVOs, fileName, DownloadVO.class);
	}

	private List<DownloadVO> transformToDownloadVO(List<TItemsStockLog> tItemsStockLogs, Map<Long, String> areaMap, Map<Long, ItemSpecDTO> itemSpecDTOMap)
	{
		Set<String> userIds = new HashSet<>();
		for (TItemsStockLog tItemsStockLog : tItemsStockLogs)
		{
			if (StringUtils.isNotBlank(tItemsStockLog.getUserId()))
			{
				userIds.add(tItemsStockLog.getUserId());
			}
			if (StringUtils.isNotBlank(tItemsStockLog.getOperatorNo()))
			{
				userIds.add(tItemsStockLog.getOperatorNo());
			}
		}
		List<ActIdUser> users = userService.queryUsersByIds(Lists.newArrayList(userIds));
		Map<String, String> userNameMap = users.stream().collect(Collectors.toMap(ActIdUser::getId, ActIdUser::genRedName));

		List<DownloadVO> downloadVOs = new ArrayList<>();
		for (TItemsStockLog tItemsStockLog : tItemsStockLogs)
		{
			ItemSpecDTO itemSpecDTO = itemSpecDTOMap.get(tItemsStockLog.getItemSpecId());

			DownloadVO downloadVO = new DownloadVO();
			downloadVO.setReason(tItemsStockLog.getReason());
			downloadVO.setAmount(tItemsStockLog.getAmount());
			downloadVO.setTime(tItemsStockLog.getOperateTime());
			downloadVO.setReceiveArea(areaMap.get(tItemsStockLog.getItemAreaId()));
			downloadVO.setItemName(itemSpecDTO==null ? "" : itemSpecDTO.getItemName());
			downloadVO.setUnit(itemSpecDTO==null ? "" : itemSpecDTO.getUnit());
			downloadVO.setType(OperateType.queryNameByType(tItemsStockLog.getOperateType()));
			String name = "";
			String sender = "-";
			if (OperateType.RECEIVE.getType().equals(tItemsStockLog.getOperateType()))
			{
				name = userNameMap.get(tItemsStockLog.getUserId());
				sender = userNameMap.get(tItemsStockLog.getOperatorNo());
				if (sender == null) {
					sender = tItemsStockLog.getOperator();
				}
			} else
			{
				name = userNameMap.get(tItemsStockLog.getOperatorNo());
			}
			downloadVO.setSender(sender);
			downloadVO.setUserName(name);
			if (StringUtils.isNotBlank(tItemsStockLog.getOperatorPhone())) {
				downloadVO.setOperatorPhone(tItemsStockLog.getOperatorPhone());
			}
			downloadVOs.add(downloadVO);
		}
		return downloadVOs;
	}

	private void fillDownloadParam(DownloadParam param)
	{
		if (null == param.getDepartmentId())
		{
			param.setDepartmentId(ItemConstant.DEFAULT_DEPARTMENT_ID);
		}
		if (CollectionUtils.isEmpty(param.getItemIds()))
		{
			List<TItems> tItems = itemService.queryItemsByDeptId(param.getDepartmentId());
			param.setItemIds(tItems.stream().map(TItems::getId).collect(Collectors.toList()));
		}
		if (CollectionUtils.isEmpty(param.getAreaIds()))
		{
			List<Long> areaIds = itemService.queryAreaIdsByItemIds(param.getItemIds());
			param.setAreaIds(areaIds);
		}
		if (CollectionUtils.isEmpty(param.getSpecIds()))
		{
			List<Long> specIds = itemService.querySpecIdsByItemIds(param.getItemIds());
			param.setSpecIds(specIds);
		}
	}

	@ApiOperation(notes = "/downloadDetailCount", value = "下载领取明细总数量")
	@PostMapping(value = "/downloadDetailCount")
	@Authority(code = "oa_item_stock_manage")
	public Integer downloadDetailCount(@RequestBody DownloadParam param)
	{
		this.fillDownloadParam(param);
		return itemManageService.queryPagedLogCounts(param);
	}

	@ApiOperation(notes = "/uploadEmailExcel", value = "上传文件通过第一行email识别用户id")
	@PostMapping(value = "/uploadEmailExcel")
	public List<String> uploadEmailExcel(@RequestParam(value = "file") MultipartFile file) {
		return itemSpecificPersonService.getFileUserToItem(file);
	}

	@ApiOperation(notes = "/handlerItemExcel",  value= "")
	@RequestMapping(value = "/handlerItemExcel", method = RequestMethod.POST)
	public void handlerHistoryTravelForm(@RequestParam(value = "file") MultipartFile file,int sheetNum,int emailColumn,int itemId) {
		try {
			Workbook wb = ExcelUtil.transforWorkbook(file.getInputStream());
			itemManageService.apolloParam(wb,sheetNum,emailColumn,itemId);
		} catch (IOException e) {
			throw new RuntimeException("仅支持上传excel文件");
		}
	}


}
