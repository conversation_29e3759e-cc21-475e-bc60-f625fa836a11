package com.xhs.oa.itemCollection.enums;

import lombok.Getter;

@Getter
public enum OperateType
{
	REDUCE_STOCK(0,"减库存"),
	ADD_STOCK(1,"加库存"),
	RECEIVE(2,"领取"),
	;

	private Integer type;

	private String name;

	OperateType(Integer type, String name)
	{
		this.type = type;
		this.name = name;
	}

	public static String queryNameByType(Integer type)
	{
		for (OperateType operateType : OperateType.values())
		{
			if (operateType.getType().equals(type))
			{
				return operateType.getName();
			}
		}
		return "";
	}
}
