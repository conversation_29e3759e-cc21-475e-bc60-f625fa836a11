package com.xhs.oa.itemCollection.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 领用物品人员表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuppressWarnings("serial")
public class TItemsSpecificPerson implements Serializable {
	
	/**
	 * 自增物品id
	 */
	private Long id;
	
	/**
	 * 物品id
	 */
	private Long itemId;

	/**
	 * 领取人员工id
	 */
	private String userId;

	/**
	 * 是否有效：0无效，1有效
	 */
	private Integer isValid;
	
	/**
	 * 操作人id
	 */
	private String operatorNo;
	
	/**
	 * 操作人名称
	 */
	private String operator;
	
	/**
	 * 操作时间
	 */
	private Date operateTime;

	public TItemsSpecificPerson(Long itemId, String userId, String operatorNo, String operator) {
		this.itemId = itemId;
		this.userId = userId;
		this.operatorNo = operatorNo;
		this.operator = operator;
	}
}

