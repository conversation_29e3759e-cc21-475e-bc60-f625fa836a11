package com.xhs.oa.itemCollection.mapper;

import com.xhs.oa.itemCollection.dto.ItemSpecDTO;
import com.xhs.oa.itemCollection.model.TItems;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 
 * <br>
 * <b>功能：</b>TItemsMapper 类<br>
 * <b>日期：</b> 2020-02-04 11:05:01 <br>
 */
public interface TItemsMapper {

    /**
     * 主键查询数据
     */
    TItems selectByPrimaryKey(Long id);

    TItems selectFirst();

    /**
     * 新增数据
     */
    int insert(TItems record);


    /**
     * 主键更新数据
     */
    int updateByPrimaryKey(TItems record);

    /**
     * 根据部门id和物品名称查询物品
     */
    List<TItems> queryItemsByDeptIdAndItemName(@Param("departmentId") Long departmentId, @Param("itemName") String itemName);

    List<TItems> selectByDepartmentId(Long departmentId);

    List<TItems> queryItemsByIds(List<Long> itemIds);

    List<ItemSpecDTO> queryItemSpecNameByItemIds(List<Long> itemIds);

    int deleteItem(@Param("id") Long id);
}
