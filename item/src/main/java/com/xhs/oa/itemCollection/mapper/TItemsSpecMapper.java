package com.xhs.oa.itemCollection.mapper;

import com.xhs.oa.itemCollection.dto.ItemSpecDTO;
import com.xhs.oa.itemCollection.model.TItemsSpec;

import java.util.List;

/**
 * 
 * <br>
 * <b>功能：</b>TItemsMapper 类<br>
 * <b>日期：</b> 2020-02-04 11:05:01 <br>
 */
public interface TItemsSpecMapper
{

    /**
     * 主键查询数据
     */
    TItemsSpec selectByPrimaryKey(Long id);

    TItemsSpec selectFirst();

    /**
     * 新增数据
     */
    int insert(TItemsSpec record);


    /**
     * 主键更新数据
     */
    int updateByPrimaryKey(TItemsSpec record);

    /**
     * 通过物品id查询
     */
    List<TItemsSpec> selectByItemId(Long itemId);

    List<TItemsSpec> selectSpecByItemIds(List<Long> itemIds);

    List<ItemSpecDTO> queryItemSpecNameBySpecIds(List<Long> specIds);

}
