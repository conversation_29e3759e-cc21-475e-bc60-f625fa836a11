package com.xhs.oa.itemCollection.service;

import com.xhs.oa.itemCollection.dto.ItemOutsiderDTO;

public class ItemOutsider {


    private static final ThreadLocal<ItemOutsiderDTO> ITEM_OUTSIDER = new ThreadLocal<>();

    public static ItemOutsiderDTO get(){
        return ITEM_OUTSIDER.get();
    }

    public static void set(ItemOutsiderDTO itemsOutsider){
        ITEM_OUTSIDER.set(itemsOutsider);
    }

    public static void clean(){
        ITEM_OUTSIDER.remove();
    }
}
