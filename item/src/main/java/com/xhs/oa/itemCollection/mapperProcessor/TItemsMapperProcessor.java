package com.xhs.oa.itemCollection.mapperProcessor;

import com.xhs.oa.itemCollection.dto.AreaStockDTO;
import com.xhs.oa.itemCollection.dto.ItemSpecDTO;
import com.xhs.oa.itemCollection.mapper.*;
import com.xhs.oa.itemCollection.model.*;
import com.xhs.oa.itemCollection.param.DownloadParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;


@Component
public class TItemsMapperProcessor
{
	@Autowired
	private TItemsMapper tItemsMapper;

	@Autowired
	private TItemsSpecMapper tItemsSpecMapper;

	@Autowired
	private TItemsAreaMapper tItemsAreaMapper;

	@Autowired
	private TItemsStockMapper tItemsStockMapper;

	@Autowired
	private TItemsStockLogMapper tItemsStockLogMapper;

	/**
	 * 新增物品
	 */
	public void insertItems(TItems items)
	{
		tItemsMapper.insert(items);
	}

	/**
	 * 新增物品规格
	 */
	public void insertItemsSpec(TItemsSpec itemsSpec)
	{
		tItemsSpecMapper.insert(itemsSpec);
	}

	/**
	 * 查找第一个物品
	 */
	public TItems selectFirstItem()
	{
		return tItemsMapper.selectFirst();
	}

	/**
	 * 查找第一个物品的第一个规格
	 */
	public TItemsSpec selectFirstItemSpec()
	{
		return tItemsSpecMapper.selectFirst();
	}

	public List<TItemsStockLog> selectByUserIdAndDate(Map<String, Object> paramsMap)
	{
		return tItemsStockLogMapper.selectByUserIdAndDate(paramsMap);
	}

	public List<TItemsStockLog> queryPagedReceiveRecord(Map<String, Object> paramsMap)
	{
		return tItemsStockLogMapper.queryPagedReceiveRecord(paramsMap);
	}

	/**
	 * 查询物品规格
	 */
	public List<TItemsSpec> selectSpecByItemId(Long itemId)
	{
		return tItemsSpecMapper.selectByItemId(itemId);
	}

	/**
	 * 批量查询物品规格
	 */
	public List<TItemsSpec> selectSpecByItemIds(List<Long> itemIds)
	{
		return tItemsSpecMapper.selectSpecByItemIds(itemIds);
	}

	/**
	 * 查询业务部门下的地区
	 */
	public List<TItemsArea> queryAreasByDepartmentId(Long departmentId)
	{
		return tItemsAreaMapper.queryAreasByDepartmentId(departmentId);
	}

	/**
	 * 根据物品批量查询地区
	 */
	public List<TItemsArea> queryAreasByItemIds(List<Long> itemIds)
	{
		return tItemsAreaMapper.queryAreasByItemIds(itemIds);
	}


	/**
	 * 根据地区和规格查库存
	 */
	public Integer queryStock(Long areaId, Long specId)
	{
		TItemsStock tItemsStock = tItemsStockMapper.selectByAreaAndSpec(areaId, specId);
		return tItemsStock==null ? 0 : tItemsStock.getStock();
	}

	/**
	 * 根据地区和规格查库存
	 */
	public TItemsStock queryStockRecord(Long areaId, Long specId)
	{
		return tItemsStockMapper.selectByAreaAndSpec(areaId, specId);
	}

	/**
	 * 扣减库存
	 */
	public Integer decreaseStock(Map<String, Object> paramsMap)
	{
		return tItemsStockMapper.decreaseStock(paramsMap);
	}

	/**
	 * 分页查库存
	 */
	public List<AreaStockDTO> queryAreaStock(List<Long> areaIds)
	{
		return tItemsStockMapper.queryAreaStock(areaIds);
	}

	/**
	 * 新增地区
	 */
	public void addArea(TItemsArea record)
	{
		tItemsAreaMapper.insert(record);
	}

	/**
	 * 新增地区库存
	 */
	public void addAreaStocks(List<TItemsStock> records)
	{
		tItemsStockMapper.batchInsert(records);
	}

	/**
	 * 删除地区
	 */
	public void deleteArea(Long areaId)
	{
		tItemsAreaMapper.deleteArea(areaId);
	}

	/**
	 * 删除物品
	 */
	public void deleteItem(Long itemId)
	{
		tItemsMapper.deleteItem(itemId);
	}

	/**
	 * 插入调整库存记录
	 */
	public void insertStockLog(TItemsStockLog record)
	{
		tItemsStockLogMapper.insert(record);
	}

	/**
	 * 更新库存
	 */
	public int updateStock(Map<String, Object> paramsMap)
	{
		return tItemsStockMapper.updateStockByPrimaryKey(paramsMap);
	}

	/**
	 * 查询物品操作记录
	 */
	public List<TItemsStockLog> queryLogs(DownloadParam param)
	{
		return tItemsStockLogMapper.queryLogs(param);
	}

	/**
	 * 查询物品操作记录总数
	 */
	public Integer queryPagedLogCounts(DownloadParam param)
	{
		return tItemsStockLogMapper.queryPagedLogCounts(param);
	}

	/**
	 * 根据物品名称和地区名称查询
	 */
	public Integer queryItemAreaExist(Long departmentId, String itemName, String areaName)
	{
		return tItemsAreaMapper.queryItemAreaExist(departmentId, itemName, areaName);
	}

	/**
	 * 根据部门id和物品名称查询物品
	 */
	public List<TItems> queryItemsByDeptIdAndItemName(Long departmentId, String itemName)
	{
		return tItemsMapper.queryItemsByDeptIdAndItemName(departmentId, itemName);
	}

	/**
	 * 根据部门id查询物品
	 */
	public List<TItems> queryItemsByDeptId(Long departmentId)
	{
		return tItemsMapper.selectByDepartmentId(departmentId);
	}

	/**
	 * 批量查询物品
	 */
	public List<TItems> queryItemsByIds(List<Long> itemIds)
	{
		return tItemsMapper.queryItemsByIds(itemIds);
	}

	/**
	 * 根据id查询物品
	 */
	public TItems queryItemsById(Long itemId)
	{
		return tItemsMapper.selectByPrimaryKey(itemId);
	}

	/**
	 * 根据地区id查询地区
	 */
	public TItemsArea queryAreaById(Long areaId)
	{
		return tItemsAreaMapper.selectByPrimaryKey(areaId);
	}

	/**
	 * 根据地区名查询地区
	 */
	public List<TItemsArea> queryAreaByName(String area)
	{
		return tItemsAreaMapper.queryAreaByName(area);
	}

	public List<ItemSpecDTO> queryItemSpecNameByItemIds(List<Long> itemIds)
	{
		return tItemsMapper.queryItemSpecNameByItemIds(itemIds);
	}

	public List<ItemSpecDTO> queryItemSpecNameBySpecIds(List<Long> specIds)
	{
		return tItemsSpecMapper.queryItemSpecNameBySpecIds(specIds);
	}
}
