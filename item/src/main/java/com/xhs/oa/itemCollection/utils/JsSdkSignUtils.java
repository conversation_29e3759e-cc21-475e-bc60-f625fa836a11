package com.xhs.oa.itemCollection.utils;

import org.apache.commons.lang3.StringUtils;

import java.net.URLEncoder;
import java.security.NoSuchAlgorithmException;
import java.util.*;

public class JsSdkSignUtils {


    /**
     * 获取js-sdk签名
     *
     * @param url 调用方传递的参数
     * @return
     */
    public static String getSign(String url, String ticket, Long timestamp, String nonceStr) throws NoSuchAlgorithmException {
        Map paramMap = new HashMap();
        paramMap.put("url", url);
        paramMap.put("noncestr", nonceStr);
        paramMap.put("jsapi_ticket", ticket);
        paramMap.put("timestamp", timestamp);
        String requestParam = formatUrlMap(paramMap, false, false);
        return SHA1Util.sha1(requestParam);
    }

    /**
     * 获取16位随机字符串
     */
    public static String generateRandomString() {
        String upperCaseLetters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        StringBuilder sb = new StringBuilder(16);
        Random random = new Random();
        for (int i = 0; i < 16; i++) {
            sb.append(upperCaseLetters.charAt(random.nextInt(upperCaseLetters.length())));
        }
        return sb.toString();
    }

    /**
     * 参数按照字段名排序
     */
    public static String formatUrlMap(Map<String, Object> paraMap, boolean urlEncode, boolean keyToLower) {
        String buff = "";
        Map<String, Object> tmpMap = paraMap;
        try {
            List<Map.Entry<String, Object>> infoIds = new ArrayList<Map.Entry<String, Object>>(tmpMap.entrySet());
            // 对所有传入参数按照字段名的 ASCII 码从小到大排序（字典序）
            Collections.sort(infoIds, new Comparator<Map.Entry<String, Object>>() {
                @Override
                public int compare(Map.Entry<String, Object> o1, Map.Entry<String, Object> o2) {
                    return (o1.getKey()).toString().compareTo(o2.getKey());
                }
            });
            // 构造URL 键值对的格式
            StringBuilder buf = new StringBuilder();
            for (Map.Entry<String, Object> item : infoIds) {
                if (StringUtils.isNotBlank(item.getKey())) {
                    String key = item.getKey();
                    Object val = item.getValue();
                    if (urlEncode) {
                        val = URLEncoder.encode(val.toString(), "utf-8");
                    }
                    if (keyToLower) {
                        buf.append(key.toLowerCase() + "=" + val);
                    } else {
                        buf.append(key + "=" + val);
                    }
                    buf.append("&");
                }
            }

            buff = buf.toString();
            if (buff.isEmpty() == false) {
                buff = buff.substring(0, buff.length() - 1);
            }
        } catch (Exception e) {
            return null;
        }
        return buff;
    }
}
