package com.xhs.oa.itemCollection.rpc.server;


import com.xhs.oa.itemCollection.mapperProcessor.TItemsMapperProcessor;
import com.xhs.oa.itemCollection.model.TItemsArea;
import com.xhs.oa.itemCollection.model.TItemsSpecificPerson;
import com.xhs.oa.itemCollection.model.TItemsStock;
import com.xhs.oa.itemCollection.rpc.common.ResponseUtil;
import com.xhs.oa.itemCollection.service.ItemService;
import com.xhs.oa.itemCollection.service.ItemSpecificPersonService;
import com.xhs.oa.office.exception.BusinessException;
import com.xiaohongshu.fls.rpc.oa.office.item.ItemServiceRpc;
import com.xiaohongshu.fls.rpc.oa.office.item.common.Response;
import com.xiaohongshu.fls.rpc.oa.office.item.req.RPCAddItemReceiverReq;
import com.xiaohongshu.fls.rpc.oa.office.item.req.RPCGetItemStockReq;
import com.xiaohongshu.fls.rpc.oa.office.item.resp.ItemStockInfo;
import com.xiaohongshu.fls.rpc.oa.office.item.response.RpcAddItemReceiverResp;
import com.xiaohongshu.fls.rpc.oa.office.item.response.RpcGetItemStockResp;
import com.xiaohongshu.infra.rpc.base.Context;
import com.xiaohongshu.infra.rpc.springboot.support.annotions.EnableNettyThriftServer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
@EnableNettyThriftServer(genClass = ItemServiceRpc.class, port = 8000, accessLog = true)
public class ItemServiceRpcImpl implements ItemServiceRpc.Iface{

    @Autowired
    private ItemSpecificPersonService itemSpecificPersonService;

    @Autowired
    private ItemService itemService;

    @Autowired
    private TItemsMapperProcessor itemsMapperProcessor;

    @Override
    public RpcAddItemReceiverResp addItemReceiver(Context context, RPCAddItemReceiverReq req) throws TException {
        RpcAddItemReceiverResp res = new RpcAddItemReceiverResp();
        Response response = ResponseUtil.defaultResp();
        try {
            List<String> userIds = req.getUserIdList();
            Long itemId = req.getItemId();
            if (CollectionUtils.isNotEmpty(userIds)) {
                List<TItemsSpecificPerson> specificList = new ArrayList<>();
                userIds.stream().forEach(v-> specificList.add(new TItemsSpecificPerson(itemId, v , "0", "system")));
                itemSpecificPersonService.batchInsert(specificList);
            }
        } catch (Exception e) {
            log.error("物资领取名单员工添加失败", e);
            response.setSuccess(false);
            response.setMsg(e.getMessage());
        }
        res.setResponse(response);
        return res;
    }

    @Override
    public RpcGetItemStockResp getItemStock(Context context, RPCGetItemStockReq rpcGetItemStockReq) throws TException {
        RpcGetItemStockResp res = new RpcGetItemStockResp();
        Response response = ResponseUtil.defaultResp();
        try {

            String area = rpcGetItemStockReq.getArea();
            Long itemId = rpcGetItemStockReq.getItemId();
            List<Long> specIds = itemService.querySpecIdsByItemId(itemId);
            if (CollectionUtils.isEmpty(specIds)) {
                throw new BusinessException("物品不存在");
            }
            Long itemSpecId = specIds.get(0);
            List<TItemsArea> itemsAreas = itemService.queryAreaByName(area).stream()
                    .filter(v -> Objects.equals(itemId, v.getItemId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(itemsAreas)) {
                throw new BusinessException("地区物品不存在");
            }
            Long areaId = itemsAreas.get(0).getId();
            TItemsStock tItemsStock = itemsMapperProcessor.queryStockRecord(areaId, itemSpecId);
            ItemStockInfo itemStockInfo = new ItemStockInfo();
            itemStockInfo.setStock(tItemsStock.getStock());
            itemStockInfo.setItemId(itemId);
            itemStockInfo.setTotalStock(tItemsStock.getTotalStock());
            itemStockInfo.setArea(area);
            res.setStock(itemStockInfo);
        } catch (Exception e) {
            log.error("物资领取库存查询失败", e);
            response.setSuccess(false);
            response.setMsg(e.getMessage());
        }
        res.setResponse(response);
        return res;
    }
}
