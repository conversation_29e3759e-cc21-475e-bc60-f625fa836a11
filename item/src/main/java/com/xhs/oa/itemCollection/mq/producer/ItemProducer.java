package com.xhs.oa.itemCollection.mq.producer;


import com.xhs.oa.itemCollection.dto.ItemReceivedMsgDTO;
import com.xiaohongshu.erp.common.utils.JsonUtil;
import com.xiaohongshu.events.client.producer.EventsProducer;
import com.xiaohongshu.events.client.producer.SendResult;
import com.xiaohongshu.events.client.producer.SendStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ItemProducer {

    EventsProducer eventsProducer;

    private static final String topic = "oa_office_item_received";

    ItemProducer() {
        eventsProducer = new EventsProducer(topic);
        eventsProducer.start();
    }

    public Boolean send(ItemReceivedMsgDTO msg) {
        String data = JsonUtil.toJson(msg);
        try
        {
            log.info("物资领取消息发送{}", data);
            SendResult result = eventsProducer.send(topic, data);
            if (SendStatus.SUCCESS.equals(result.getSendStatus()))
            {

            } else
            {
                log.error("物资领取消息发送rocketmq 消息失败。msgContent:{}, topic:{},error:{}", data, topic, result);
                return false;
            }
        } catch (Exception e)
        {
            log.error("物资领取消息发送失败{}", data, e);
            return false;
        }
        return true;
    }
}
