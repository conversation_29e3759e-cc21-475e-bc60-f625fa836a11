package com.xhs.oa.itemCollection.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * <br>
 * <b>功能：</b>TItemsStockLog 实体类<br>
 * <b>日期：</b> 2020-02-04 11:05:03 <br>
 */
@Data
@SuppressWarnings("serial")
public class TItemsStockLog implements Serializable {
	
	/**
	 * 自增id
	 */
	private Long id;
	
	private Long itemAreaId;

	private Long itemSpecId;

	/**
	 * 领用人id
	 */
	private String userId;
	
	/**
	 * 库存操作类型：0减库存，1加库存，2领取
	 */
	private Integer operateType;
	
	/**
	 * 数量
	 */
	private Integer amount;
	
	/**
	 * 库存调整原因
	 */
	private String reason;
	
	/**
	 * 操作人id
	 */
	private String operatorNo;
	
	/**
	 * 操作人名称
	 */
	private String operator;
	
	/**
	 * 操作时间
	 */
	private Date operateTime;

	/**
	 * 操作人手机号
	 */
	private String operatorPhone;


}

