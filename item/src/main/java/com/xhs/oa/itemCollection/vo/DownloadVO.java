package com.xhs.oa.itemCollection.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.xhs.oa.common.annotation.ExportDesc;
import lombok.Data;

import java.util.Date;

@Data
public class DownloadVO
{
	@ExportDesc(fieldName = "人员")
	private String userName;

	@ExportDesc(fieldName = "类型")
	private String type;

	@ExportDesc(fieldName = "物品名称")
	private String itemName;

	@ExportDesc(fieldName = "数量")
	private Integer amount;

	@ExportDesc(fieldName = "单位")
	private String unit;

	@ExportDesc(fieldName = "时间")
	@JSONField(format = "yyyy-MM-dd HH:mm" ) // change
	private Date time;

	@ExportDesc(fieldName = "领取地区")
	private String receiveArea;

	@ExportDesc(fieldName = "发放人")
	private String sender;

	@ExportDesc(fieldName = "库存调整原因")
	private String reason;

	@ExportDesc(fieldName = "发放人手机号")
	private String operatorPhone;
}
