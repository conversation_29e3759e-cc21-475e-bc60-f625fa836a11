package com.xhs.oa.itemCollection.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

@Data
public class ItemReceivedMsgDTO {

    /**
     * 唯一ID
     */
    private Long uniqueId;

    /**
     * 物品名ID
     */
    private Long itemId;

    /**
     * 领取人
     */
    private String userId;

    /**
     * 发放人ID
     */
    private String operatorId;

    /**
     * 发放人手机号
     */
    private String operatorPhone;

    /**
     * 发放人姓名
     */
    private String operatorName;

    /**
     * 领取时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date receiveTime;

    /**
     * 领取地区
     */
    private String areaName;

    /**
     * 领取数量
     */
    private Integer amount;
}
