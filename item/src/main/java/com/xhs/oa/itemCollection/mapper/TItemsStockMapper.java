package com.xhs.oa.itemCollection.mapper;

import com.xhs.oa.itemCollection.dto.AreaStockDTO;
import com.xhs.oa.itemCollection.model.TItemsStock;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 
 * <br>
 * <b>功能：</b>TItemsStockMapper 类<br>
 * <b>日期：</b> 2020-02-04 11:05:02 <br>
 */
public interface TItemsStockMapper {

    /**
     * 主键查询数据
     */
    TItemsStock selectByPrimaryKey(Long id);

    TItemsStock selectByAreaAndSpec(@Param("areaId") Long areaId, @Param("specId") Long specId);

    /**
     * 新增数据
     */
    int insert(TItemsStock record);

    int batchInsert(List<TItemsStock> records);


    /**
     * 主键更新数据
     */
    int updateStockByPrimaryKey(@Param("paramsMap") Map<String, Object> paramsMap);

    /**
     * 扣减库存
     */
    int decreaseStock(@Param("paramsMap") Map<String, Object> paramsMap);

    /**
     * 根据地区查询库存
     */
    List<AreaStockDTO> queryAreaStock(@Param("areaIds") List<Long> areaIds);
}
