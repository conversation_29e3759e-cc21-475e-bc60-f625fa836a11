package com.xhs.oa.itemCollection.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class SpecStockParam
{
	@ApiModelProperty("物品规格")
	private String spec;

	@ApiModelProperty("单位")
	@NotNull(message = "单位不能为空")
	private String unit;

	@ApiModelProperty("新增地区某规格物品的库存数量")
	@NotNull(message = "库存不能为空")
	private Integer stock;
}
