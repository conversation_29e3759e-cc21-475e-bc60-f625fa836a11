package com.xhs.oa.itemCollection.service.impl;

import com.xhs.finance.exception.BusinessException;
import com.xhs.oa.common.util.ExcelUtil;
import com.xhs.oa.helper.AssertHelper;
import com.xhs.oa.itemCollection.mapper.TItemsSpecificPersonMapper;
import com.xhs.oa.itemCollection.model.TItemsSpecificPerson;
import com.xhs.oa.itemCollection.service.ItemSpecificPersonService;
import com.xhs.oa.workflow.service.UserService;
import com.xhs.oa.workflow.vo.UserVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/12/19  2:17 下午
 */
@Service
@Slf4j
public class ItemSpecificPersonServiceImpl implements ItemSpecificPersonService {

    @Autowired
    private TItemsSpecificPersonMapper tItemsSpecificPersonMapper;
    @Autowired
    private UserService userService;

    @Override
    public List<TItemsSpecificPerson> queryUserByItemId(Integer itemId) {
        return tItemsSpecificPersonMapper.queryUserByItemId(itemId);
    }

    @Override
    public int batchInsert(List<TItemsSpecificPerson> list) {
        return tItemsSpecificPersonMapper.batchInsert(list);
    }

    @Override
    public List<String> getFileUserToItem(MultipartFile file) {
        if (null == file) {
            throw new RuntimeException("请上传文件");
        }
        String name = file.getOriginalFilename();
        AssertHelper.notNull(name, "文件名称为空");
        if (!name.endsWith(".xlsx") && !name.endsWith(".xls")) {
            throw new RuntimeException("仅支持上传excel文件");
        }
        try {
            Workbook wb = ExcelUtil.transforWorkbook(file.getInputStream());
            AssertHelper.notNull(wb,"文档第一行为空");
            Sheet sheetAt = wb.getSheetAt(0);
            int lastRowNum = sheetAt.getLastRowNum();
            if(lastRowNum < 1){
                throw new BusinessException("无人员数据");
            }
            Set<String> userEmails = new HashSet<>();
            for(int i = 1 ;i<=lastRowNum;i++){
                Row row = sheetAt.getRow(i);
                //员工email第一列；
                String userEmail = ExcelUtil.getStringValue(row.getCell(0));
                userEmails.add(userEmail);
            }
            List<UserVo> userVos = userService.findSimpleUserEmailUserByEmails(new ArrayList<>(userEmails));
            return userVos.stream().map(UserVo::getUserID).collect(Collectors.toList());
        } catch (IOException e) {
            throw new RuntimeException("仅支持上传excel文件");
        }catch (Exception e){
            log.error("物资领取识别文件人员信息失败：{}",e.getMessage(),e);
            throw new BusinessException("请先检查excel格式和用户邮箱是否正确,如无误，请联系oa薯解决");
        }
    }
}
