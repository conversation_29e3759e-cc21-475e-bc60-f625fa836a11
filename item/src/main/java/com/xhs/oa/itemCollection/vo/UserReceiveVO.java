package com.xhs.oa.itemCollection.vo;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 用户单次领取信息
 */
@Data
public class UserReceiveVO
{
	@ApiModelProperty("领取code")
	private String code;

	@ApiModelProperty("本次领取物品id")
	private Long itemId;

	@ApiModelProperty("本次领取物品规格id")
	private Long itemSpecId;

	@ApiModelProperty("本次领取物品所在地区id")
	private Long areaId;

	@ApiModelProperty("本次领取物品所在地区")
	private String areaName;

	@ApiModelProperty("领取用户id")
	private String userId;

	@ApiModelProperty("薯名")
	private String redName;

	@ApiModelProperty("本次领取数量")
	private Integer amount;

	@ApiModelProperty("领取周期")
	private String receiveCycle;

	@ApiModelProperty("物品单位")
	private String unit;

	@ApiModelProperty("物品名称")
	private String itemName;

	@ApiModelProperty("今日领取数量")
	private Integer todayAmount;

	@ApiModelProperty("领取日期")
	@JSONField(format ="yyyy-MM-dd") // change
	private Date date;

	@ApiModelProperty("领取时间")
	@JSONField(format = "HH:mm") // change
	private Date receiveTime;

	@ApiModelProperty("发放人")
	private String operator;
}
