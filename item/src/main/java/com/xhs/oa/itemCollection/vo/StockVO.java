package com.xhs.oa.itemCollection.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 库存
 */
@Data
public class StockVO
{

	@ApiModelProperty("地区id")
	private Long areaId;

	@ApiModelProperty("地区名称")
	private String areaName;

	@ApiModelProperty("物品名称")
	private String itemName;

	@ApiModelProperty("累计库存总数")
	private Integer totalStock;

	@ApiModelProperty("已领取总数")
	private Integer receivedAmount;

	@ApiModelProperty("剩余库存总数")
	private Integer leftAmount;
}
