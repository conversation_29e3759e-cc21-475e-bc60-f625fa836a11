package com.xhs.oa.itemCollection.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 领取周期
 */
@Getter
@AllArgsConstructor
public enum ReceiveCycleType
{
	DAILY("daily", "每日", "今日"),

	/**
	 * 周一为第一天
	 */
	WEEKLY("weekly", "每周", "本周"),
	/**
	 * 周日为第一天
	 */
	WEEKLY_FROM_SUNDAY("weekly2", "每周", "本周"),

	BIWEEKLY("biweekly", "每两周", "两周内"),

	MONTHLY("monthly", "每月", "本月"),

	QUARTER("quarter","每季度","本季度"),

	PERPETUAL("perpetual","永久",""),
	;

	private String type;

	private String name;

	/**
	 * 前端展示文字
	 */
	private String msg;

	public static ReceiveCycleType getEnumByType(String type)
	{
		for (ReceiveCycleType receiveCycleType : ReceiveCycleType.values())
		{
			if (receiveCycleType.getType().equals(type))
			{
				return receiveCycleType;
			}
		}
		return null;
	}

	public static ReceiveCycleType getEnumByMsg(String msg) {
		for (ReceiveCycleType receiveCycleType : ReceiveCycleType.values())
		{
			if (receiveCycleType.getMsg().equals(msg))
			{
				return receiveCycleType;
			}
		}
		return null;
	}
}
