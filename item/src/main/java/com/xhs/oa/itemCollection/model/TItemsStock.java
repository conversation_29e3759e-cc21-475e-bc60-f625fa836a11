package com.xhs.oa.itemCollection.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * <br>
 * <b>功能：</b>TItemsStock 实体类<br>
 * <b>日期：</b> 2020-02-04 11:05:02 <br>
 */
@Data
@SuppressWarnings("serial")
public class TItemsStock implements Serializable {
	
	/**
	 * 自增id
	 */
	private Long id;
	
	/**
	 * 物品地区id
	 */
	private Long itemAreaId;
	
	/**
	 * 物品规格id
	 */
	private Long itemSpecId;
	
	/**
	 * 库存总数
	 */
	private Integer totalStock;
	
	/**
	 * 剩余库存
	 */
	private Integer stock;
	
	/**
	 * 操作人id
	 */
	private String operatorNo;
	
	/**
	 * 操作人名称
	 */
	private String operator;
	
	/**
	 * 操作时间
	 */
	private Date operateTime;
	

}

