package com.xhs.oa.filter;

import com.google.common.collect.Lists;
import com.xhs.oa.itemCollection.dto.ItemOutsiderDTO;
import com.xhs.oa.itemCollection.service.ItemOutsider;
import com.xhs.oa.itemCollection.service.ItemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.apache.log4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

@WebFilter(urlPatterns = "/*", filterName = "itemsOutsideFilter")
@Slf4j
@Component
public class ItemsOutsideFilter implements Filter {

    @Autowired
    private ItemService itemService;

    private final List<String> ignoreUrls = Lists.newArrayList("/xhs-oa/items/out/getMobileCode", "/xhs-oa/items/out/checkMobileCode");

    private final List<String> itemUrls = Lists.newArrayList("/xhs-oa/items/out/*");

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest)request;
        HttpServletResponse httpResponse = (HttpServletResponse)response;
        if(isIgnore(httpRequest)){
            chain.doFilter(httpRequest, httpResponse);
            return;
        }
        String token = httpRequest.getParameter("token");
        if (StringUtils.isBlank(token)){
            token = httpRequest.getHeader("Authorization");
        }
        if (StringUtils.isBlank(token)){
            httpResponse.setStatus(HttpStatus.SC_UNAUTHORIZED);
            httpResponse.getOutputStream().write("请登录".getBytes("UTF-8"));
            return;
        }

        ItemOutsiderDTO itemOutsiderDTO = null;
        try {
            itemOutsiderDTO = itemService.verifyToken(token);
        } catch (Exception e) {
            log.error("get itemOutsider error from redis, token:" + token ,e);
        }
        if (itemOutsiderDTO == null) {
            httpResponse.setStatus(HttpStatus.SC_UNAUTHORIZED);
            httpResponse.getOutputStream().write("请登录".getBytes("UTF-8"));
            return;
        }

        ItemOutsider.set(itemOutsiderDTO);

        try{
            MDC.put("token", token);
            chain.doFilter(httpRequest, httpResponse);
        }finally{
            ItemOutsider.clean();
        }
    }

    private boolean isIgnore(HttpServletRequest httpRequest){
        if (!isItemUrl(httpRequest)) {
            return true;
        }

        for(String url: ignoreUrls){
            AntPathMatcher matcher = new AntPathMatcher();
            boolean isMatch = matcher.match(url, httpRequest.getServletPath());
            if(isMatch){
                return true;
            }
        }
        return false;
    }

    private boolean isItemUrl(HttpServletRequest httpRequest){
        for(String url: itemUrls){
            AntPathMatcher matcher = new AntPathMatcher();
            boolean isMatch = matcher.match(url, httpRequest.getServletPath());
            if(isMatch){
                return true;
            }
        }
        return false;
    }
}
