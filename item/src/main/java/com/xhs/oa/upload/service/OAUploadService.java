package com.xhs.oa.upload.service;

import com.xhs.finance.upload.model.UploadFile;
import com.xhs.finance.upload.service.UploadService;
import com.xhs.oa.upload.enums.OAUploadEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.InputStream;

/**
 * OA封装的uploadService
 */
@Service
public class OAUploadService {

    @Autowired
    private UploadService uploadService;

    public OAUploadService() {
    }

    /**
     * description: 上传至私有空间
     * @author: yzhang14
     * @date: 2019/4/12
     */
    public UploadFile upload(InputStream in, String fileName) {
        return uploadService.upload(in, fileName, OAUploadEnum.UPLOAD_PRIVATE.getChannel(), OAUploadEnum.UPLOAD_PRIVATE.getBucket());
    }

}

