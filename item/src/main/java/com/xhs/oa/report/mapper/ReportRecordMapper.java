package com.xhs.oa.report.mapper;

import com.xhs.oa.report.model.ReportRecord;
import com.xhs.oa.report.param.ReportQueryParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 
 * <br>
 * <b>功能：</b>ReportRecordMapper 类<br>
 * <b>日期：</b> 2019-01-09 14:30:21 <br>
 */
public interface ReportRecordMapper {

    /**
     * 主键查询数据
     */
    ReportRecord selectByPrimaryKey(Long id);

    /**
     * 新增数据
     */
    int insert(ReportRecord record);

    /**
     * 主键动态更新数据
     */
    int updateByPrimaryKeySelective(ReportRecord record);

    /**
     * 主键更新数据
     */
    int updateByPrimaryKey(ReportRecord record);

    /**
     * 分页查询报表记录
     * @param reportQueryParam
     * @return
     */
    List<ReportRecord> reportListQueryByPage(@Param("reportQueryParam") ReportQueryParam reportQueryParam,@Param("userId") String userId);

    /**
     * 查询总的数量
     * @param reportQueryParam
     * @param userId
     * @return
     */
    int reportListCountQuery(@Param("reportQueryParam") ReportQueryParam reportQueryParam,@Param("userId") String userId);

    /**
     * 更新报表任务的状态和下载地址
     * @param id
     * @param reportStatus
     * @param downloadUrl
     * @return
     */
    int updateReportStatusById(@Param("id") Long id ,@Param("reportStatus") String reportStatus,@Param("downloadUrl") String downloadUrl);
}
