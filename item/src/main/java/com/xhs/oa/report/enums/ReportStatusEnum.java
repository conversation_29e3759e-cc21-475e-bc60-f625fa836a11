package com.xhs.oa.report.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
public enum ReportStatusEnum {

    ONGOING("ongoing","运行中"),
    FINISHED("finished","已完成"),
    FAILED("failed","失败");

    private String code;

    private String desc;

    ReportStatusEnum(String code ,String desc){
        this.code = code;
        this.desc = desc;
    }

    public static ReportStatusEnum getStatusEnumByCode(String code){
        if(StringUtils.isBlank(code)){
            return null;
        }
        ReportStatusEnum[] enums = ReportStatusEnum.values();
        for (ReportStatusEnum anEnum : enums) {
            if(anEnum.getCode().equals(code)){
                return anEnum;
            }
        }
        return null;
    }
}
