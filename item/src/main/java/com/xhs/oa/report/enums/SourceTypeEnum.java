package com.xhs.oa.report.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
public enum SourceTypeEnum {

    PERFORMANCE_MANAGER("performanceManager","绩效管理"),
    PROMOTION_MANAGER("promotion_manager","晋升管理"),
    REGULAR_MANAGER("regular_manager", "转正管理"),
    ITEM_STOCK_MANAGER("item_stock_manager", "领用管理"),
    SUPPLIER_MANAGER("supplier_manager", "供应商管理"),
    CONTRACT_ARCHIVE("contract_archive", "合同归档管理"),
    RANK_MANAGER("rank_manager", "职级管理"),
    FUNCTION("FUNCTION", "职能"),
    SEQUENCE("SEQUENCE", "序列"),
    POSITION_CLASS("POSITION_CLASS", "职位类"),
    POSITION("POSITION", "职位"),
    ;

    private String code;

    private String desc;

    SourceTypeEnum(String code ,String desc){
        this.code = code;
        this.desc = desc;
    }

    public static SourceTypeEnum getEnumByCode(String code){
        if(StringUtils.isBlank(code)){
            return null;
        }
        SourceTypeEnum[] enums = SourceTypeEnum.values();
        for (SourceTypeEnum anEnum : enums) {
            if(anEnum.getCode().equals(code)){
                return anEnum;
            }
        }
        return null;
    }
}
