package com.xhs.oa.report.service;

import com.xhs.oa.report.enums.ReportStatusEnum;
import com.xhs.oa.report.mapper.ReportRecordMapper;
import com.xhs.oa.report.model.ReportRecord;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ReportManagerService {

    @Autowired
    private ReportRecordMapper reportRecordMapper;


    /**
     * 新增报表任务
     */
    public Long addReportTask(String reportName,String sourceCode,String userId,String userName){
        ReportRecord reportRecord = new ReportRecord();
        reportRecord.setCreator(userName);
        reportRecord.setCreatorNo(userId);
        if(StringUtils.isNotBlank(reportName) && reportName.length() > 50){
            reportName = reportName.substring(0,50);
        }
        reportRecord.setReportName(reportName);
        reportRecord.setReportStatus(ReportStatusEnum.ONGOING.getCode());
        reportRecord.setSourceName(sourceCode);
        reportRecord.setDownloadUrl("");
        reportRecordMapper.insert(reportRecord);
        return reportRecord.getId();
    }

    /**
     *  更新报表的状态
     * @param id
     */
    public void updateReportStatus(Long id,String reportStatus,String downloadUrl){
        reportRecordMapper.updateReportStatusById(id,reportStatus,downloadUrl);
    }


}
