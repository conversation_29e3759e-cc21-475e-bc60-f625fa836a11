package com.xhs.oa.report.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * <br>
 * <b>功能：</b>ReportRecord 实体类<br>
 * <b>日期：</b> 2019-01-09 14:30:21 <br>
 */
@Data
@SuppressWarnings("serial")
public class ReportRecord implements Serializable {
	
	/**
	 * 自增id
	 */
	private Long id;
	
	/**
	 * 报表名称
	 */
	private String reportName;
	
	/**
	 * 来源菜单
	 */
	private String sourceName;
	
	/**
	 * 报表状态
	 */
	private String reportStatus;
	
	/**
	 * 创建人id
	 */
	private String creatorNo;
	
	/**
	 * 创建人名称
	 */
	private String creator;
	
	/**
	 * 创建时间
	 */
	private Date createTime;
	
	/**
	 * 更新时间
	 */
	private Date updateTime;
	
	/**
	 * 下载地址
	 */
	private String downloadUrl;
	

}

