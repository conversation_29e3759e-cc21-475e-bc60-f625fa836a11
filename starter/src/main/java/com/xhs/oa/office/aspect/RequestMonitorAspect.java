package com.xhs.oa.office.aspect;

import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 请求监控切面
 * 记录所有客户端请求，并打印相关日志信息
 *  - 请求url、请求方法、请求耗时、请求参数、请求头
 *  - 返回值、是否异常、异常信息
 *  - 添加cat链路追踪
 */
@Aspect
@Component
public class RequestMonitorAspect {

    private final Logger logger = LoggerFactory.getLogger(getClass());
    private final ThreadLocal<RequestMonitor> monitorLocal = new ThreadLocal<>();

    @Pointcut("within(@org.springframework.web.bind.annotation.RestController *)")
    public void pointCut() {}

    /**
     * 前置处理器
     */
    @Before("pointCut()")
    public void monitorBefore(JoinPoint joinPoint) {
        RequestMonitor.RequestMonitorBuilder builder = RequestMonitor.builder().startTime(System.currentTimeMillis());

        HttpServletRequest request = RequestContextHolder.get();
        if (request != null) {
            builder.url(request.getRequestURI());
            builder.method(request.getMethod());
            //header第一种格式展示
            Enumeration<String> enumeration = request.getHeaderNames();
            Map<String, String> headerMap = new HashMap<>();
            while (enumeration.hasMoreElements()) {
                String headerName = enumeration.nextElement();
                headerMap.put(headerName, request.getHeader(headerName));
            }
            String headerJsonStr = JSON.toJSONString(headerMap);
            builder.requestHeader(headerJsonStr);
            builder.ip(request.getRemoteAddr());
        }
        Object[] objects = joinPoint.getArgs();
        String params = "{}";
        if (objects != null && objects.length > 0 && objects[0] != null) {
            params = JSON.toJSONString(objects[0]);
        }
        builder.params(params);
        monitorLocal.set(builder.build());
    }

    /**
     * 环绕通知
     */
    @Around("pointCut()")
    public Object doAround(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        Object result = proceedingJoinPoint.proceed();
        RequestMonitor requestMonitor = monitorLocal.get();
        if (requestMonitor != null) {
            requestMonitor.setCostTime(System.currentTimeMillis() - requestMonitor.getStartTime());
            requestMonitor.setLogLevel("INFO");
            requestMonitor.setResponseBody(JSON.toJSONString(result));
        }
        printLog();
        return result;
    }

    @AfterThrowing(throwing = "e", pointcut = "pointCut()")
    public void afterThrowing(Exception e) {
        RequestMonitor requestMonitor = monitorLocal.get();
        if (requestMonitor != null) {
            requestMonitor.setCostTime(System.currentTimeMillis() - requestMonitor.getStartTime());
            StackTraceElement[] stackTrace = e.getStackTrace();
            List<String> expMsgs = Arrays.stream(stackTrace).map(StackTraceElement::toString).collect(Collectors.toList());
            String exp = StringUtils.join(expMsgs, "\n\t");
            requestMonitor.setExceptionMsg(e + "\n\t" + exp);
            requestMonitor.setLogLevel("ERROR");
        }
        printLog();
    }

    private void printLog() {
        try {
            RequestMonitor requestMonitor = monitorLocal.get();
            logger.info("{}", JSON.toJSONString(requestMonitor));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        } finally {
            monitorLocal.remove();
        }
    }
}
