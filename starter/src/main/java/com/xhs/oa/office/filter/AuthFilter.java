package com.xhs.oa.office.filter;

import com.xhs.cache.RedisClient;
import com.xhs.finance.sso.UserInfoBag;
import com.xhs.finance.sso.model.UserInfo;
import com.xhs.oa.common.constant.CommonConstant;
import com.xhs.oa.common.util.EncryptUtil;
import com.xhs.oa.login.service.LoginService;
import com.xhs.oa.workflow.mapper.ActIdUserMapper;
import com.xhs.oa.workflow.model.ActIdUser;
import com.xiaohongshu.fls.sso.accessor.context.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.apache.log4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.testng.util.Strings;
import org.yaml.snakeyaml.TypeDescription;
import org.yaml.snakeyaml.Yaml;
import org.yaml.snakeyaml.constructor.Constructor;

import javax.annotation.PostConstruct;
import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;

@WebFilter(urlPatterns = "/*", filterName = "authFilter")
@Slf4j
@Component
public class AuthFilter implements Filter {

    public static final String MORK_KEY = "X-Mork-Id";

    private WebPattern webPattern;

    @Autowired
    private LoginService loginService;


    @Autowired
    private ActIdUserMapper actIdUserMapper;

    @Autowired
    private RedisClient redisClient;

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
    }

    @PostConstruct
    public void initAuthUrls(){
        try (InputStream input = getClass().getClassLoader().getResourceAsStream("web-pattern.yaml")) {
            Constructor constructor = new Constructor(WebPattern.class);
            TypeDescription webPatternsDescription = new TypeDescription(WebPattern.class);
            constructor.addTypeDescription(webPatternsDescription);
            Yaml yaml = new Yaml(constructor);
            webPattern = (WebPattern) yaml.load(input);
        } catch (Exception e) {
            log.error("parse web-pattern.yaml error", e);
        }
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest)request;
        HttpServletResponse httpResponse = (HttpServletResponse)response;
        if(isIgnore(httpRequest)){
            chain.doFilter(httpRequest, httpResponse);
            return;
        }
        UserInfo userInfo = null;
        // 获取sso中的邮箱
        String userEmail = UserContext.getUserEmail();
        log.info("sso set user email:{}", userEmail);
        if (!Strings.isNullOrEmpty(userEmail)) {
            userInfo = loginService.simulationLogin(userEmail);
        }
        String token = httpRequest.getParameter("token");
        if (!Strings.isNullOrEmpty(token) && userInfo == null) {
            userInfo = this.getUserInfoForEmailToken(token);
        }
        if(userInfo == null){
           token = httpRequest.getHeader("Authorization");
        }
        String mock = httpRequest.getHeader(MORK_KEY);
        if (StringUtils.isBlank(mock) && StringUtils.isBlank(token) && userInfo == null) {
            httpResponse.setStatus(HttpStatus.SC_UNAUTHORIZED);
            httpResponse.getOutputStream().write("请登录".getBytes("UTF-8"));
            return;
        }
        if(userInfo == null){
            if(!StringUtils.isBlank(token)){
                try{
                    userInfo = loginService.getAndRefreshLoginUserInfo(token);
                    if(userInfo == null){
                        userInfo = loginService.getRedNameLoginUserInfo(token);
                    }
                }catch(Exception e){
                    log.error("get userinfo from redis error,token="+token,e);
                    httpResponse.setStatus(HttpStatus.SC_UNAUTHORIZED);
                    httpResponse.getOutputStream().write("请登录".getBytes("UTF-8"));
                    return;
                }
            } else if (!StringUtils.isBlank(mock) && !"prod".equals(System.getProperty("user.env"))) {
                //关闭线上环境mork
                token = mock;
                userInfo = UserInfo.mork(mock);
            }
        }

        if(null == userInfo){
            httpResponse.setStatus(HttpStatus.SC_UNAUTHORIZED);
            httpResponse.getOutputStream().write("请登录".getBytes("UTF-8"));
            return;
        }

        if (loginService.getBlackUserIdList().contains(userInfo.getUserId())) {
            httpResponse.setStatus(HttpStatus.SC_FORBIDDEN);
            httpResponse.getOutputStream().write("无操作权限".getBytes("UTF-8"));
            return;
        } else {
            String userTokenKey = "xhsoa-token-userid-" + userInfo.getUserId();
            String userTokenValue = (String) redisClient.get(userTokenKey);
            if (StringUtils.isEmpty(userTokenValue) && StringUtils.isNotEmpty(token)) {
                log.info("login user from name:{},userId:{},token:{}", userInfo.getName(), userInfo.getUserId(), token);
                redisClient.set(userTokenKey, token, 60);
            }
        }

        UserInfoBag.cleanAndSet(userInfo);
        try{
            MDC.put("token", userInfo.getEmail());
            chain.doFilter(httpRequest, httpResponse);
        }finally{
            MDC.remove("token");
            UserInfoBag.clean();
        }
    }

    @Override
    public void destroy() {

    }

    private boolean isIgnore(HttpServletRequest httpRequest){
        if(CollectionUtils.isEmpty(webPattern.getIgnoreUrls())){
            return false;
        }
        for(String url: webPattern.getIgnoreUrls()){
            AntPathMatcher matcher = new AntPathMatcher();
            boolean isMatch = matcher.match(url, httpRequest.getServletPath());
            if(isMatch){
                return true;
            }
        }
        return false;
    }


    /**
     * 如果是从param中获取到的token 则认为是邮件审批 先从redis中获取 如果获取不到 对token进行解密（兼容以前的逻辑）
     * @param token
     * @return
     */
    private UserInfo getUserInfoForEmailToken(String token){
        if(StringUtils.isBlank(token)){
            return null;
        }
        try{
            UserInfo userInfo = loginService.getEmailLoginUserInfo(token);
            if(userInfo != null){
                return userInfo;
            }
            String userIDFormNum = EncryptUtil.decrypt(token, CommonConstant.TASK_EMAIL_AUDIT_SECRET_KEY);
            String[] strArr = userIDFormNum.split("_");
            if(strArr.length != 2){
                return null;
            }
            ActIdUser actIdUser = actIdUserMapper.selectByPrimaryKey(strArr[0]);
            if(actIdUser.getAccountStatus() == 0){
                return null;
            }
            userInfo = new UserInfo();
            userInfo.setUserId(actIdUser.getId());
            userInfo.setName(actIdUser.getRedNameOrFirst());
            userInfo.setEmail(actIdUser.getEmail());
            userInfo.setOthers(CommonConstant.ENCRYPT_EMAIL_TOKEN);
            userInfo.setToken(token);
            return userInfo;
        }catch (Exception e){
            log.error("token 解析失败:"+token);
            return null;
        }
    }
}
