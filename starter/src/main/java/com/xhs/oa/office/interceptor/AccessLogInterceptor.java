package com.xhs.oa.office.interceptor;

import com.google.common.base.Charsets;
import com.google.common.collect.Sets;
import com.xiaohongshu.erp.common.framework.mvc.handler.ErpGlobalResponseHandler;
import com.xiaohongshu.erp.common.framework.mvc.handler.GlobalRequestHandler;
import com.xiaohongshu.erp.common.sso.UserInfoBag;
import com.xiaohongshu.erp.common.sso.model.UserInfo;
import com.xiaohongshu.erp.common.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Enumeration;
import java.util.List;
import java.util.Set;

@Slf4j
public class AccessLogInterceptor extends HandlerInterceptorAdapter
{

	private static final Logger accessLog = LoggerFactory.getLogger("com.xiaohongshu.erp.accesslog");

	private static final String REQUEST_ATTRIBUTE_START_TIME = "access_log_start_time";

	public static final String HTTP_REQUEST_ATTRIBUTE_EXCEPTION = "response_exception";

	private static final Set<String> SENSITIVE_FIELDS = Sets.newHashSet();

	public AccessLogInterceptor()
	{

	}

	public AccessLogInterceptor(List<String> sensitiveFields)
	{
		if (CollectionUtils.isNotEmpty(sensitiveFields))
		{
			SENSITIVE_FIELDS.addAll(sensitiveFields);
		}
	}


	@Override
	public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception
	{
		try{
			request.setAttribute(REQUEST_ATTRIBUTE_START_TIME, System.currentTimeMillis());
			HttpServletRequest httpServletRequest = getHttpServletRequest();
			TraceIdUtils.initTraceForHttp(httpServletRequest);
			return true;
		} catch (Exception ex) {
			log.error("AccessLogInterceptor preHandle occur error", ex);
			return true;
		}
	}

	@Override
	public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView)
			throws Exception
	{

	}

	@Override
	public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
			throws Exception
	{
		try
		{
			Object body = request.getAttribute(ErpGlobalResponseHandler.HTTP_REQUEST_ATTRIBUTE_RESPONSE_BODY);
			accessLog(body, response.getStatus());

			Object exception = getHttpServletRequest()
					.getAttribute(HTTP_REQUEST_ATTRIBUTE_EXCEPTION);

			TraceIdUtils.finishTraceForHttp((Throwable)exception);
		} catch (Exception e)
		{
			log.error("AccessLogInterceptor postHandle occur error", ex);
		}
	}

	private static <T> void accessLog(Object returnValue, int httpStatus)
	{
		accessLog.info(buildAccessLogContent(returnValue, httpStatus));
	}

	private static String buildAccessLogContent(Object returnValue, int httpStatus)
	{
		HttpServletRequest request = getHttpServletRequest();

		StringBuilder logContentBuilder = new StringBuilder();
		try
		{
			// user ip
			logContentBuilder.append(getIpAddr(request)).append(" - - ");

			// request method
			String method = request.getMethod();
			logContentBuilder.append("\"").append(method).append(" ");

			// request url

			logContentBuilder.append("http://");
			logContentBuilder.append(request.getHeader("Host"));

			logContentBuilder.append(request.getRequestURI());

			// request params
			if ("GET".equals(method))
			{
				logContentBuilder.append(request.getQueryString() == null ? "" : "?" + request.getQueryString()).append(" ");
			} else
			{
				boolean isRequestBodyJson = StringUtils.containsIgnoreCase(request.getContentType(), "application/json");
				if (isRequestBodyJson)
				{
					Object requestBody = request.getAttribute(GlobalRequestHandler.HTTP_REQUEST_ATTRIBUTE_REQUEST_BODY);
					logContentBuilder.append(" ")
							.append(null != requestBody ? JsonUtil.toJson(requestBody, SENSITIVE_FIELDS) : "{}").append(" ");
				} else
				{
					logContentBuilder.append("?");
					Enumeration enumeration = request.getParameterNames();

					while (enumeration.hasMoreElements())
					{
						String paramName = (String) enumeration.nextElement();
						if (SENSITIVE_FIELDS.contains(paramName))
						{
							continue;
						}
						String value = request.getParameter(paramName);
						try
						{
							value = URLEncoder.encode(value, Charsets.UTF_8.toString());
						} catch (UnsupportedEncodingException e)
						{
							log.error(e.getMessage(), e);
						}
						logContentBuilder.append(paramName).append("=").append(value).append("&");
					}
					logContentBuilder.deleteCharAt(logContentBuilder.length() - 1).append(" ");
				}

			}

			// protocol
			logContentBuilder.append(request.getProtocol()).append("\" ");

			// state code
			logContentBuilder.append(httpStatus).append(" - ");

			// referer
			String referer = request.getHeader("Referer");
			logContentBuilder.append("\"").append(StringUtils.isBlank(referer) ? "-" : referer).append("\" ");

			// user agent
			String userAgent = request.getHeader("User-Agent");
			logContentBuilder.append("\"").append(StringUtils.isBlank(userAgent) ? "-" : userAgent).append("\" ");

			if (returnValue != null)
			{
				logContentBuilder.append(formatReturnValue(returnValue)).append(" ");
			} else
			{
				logContentBuilder.append("returnValue:-").append(" ");
			}
			// 响应时间
			Long beginTime = (Long) request.getAttribute(REQUEST_ATTRIBUTE_START_TIME);
			if (beginTime != null)
			{
				logContentBuilder.append(String.valueOf(System.currentTimeMillis() - beginTime)).append(" ");
			} else
			{
				logContentBuilder.append("elapsed:-").append(" ");
			}

			UserInfo userInfo = UserInfoBag.get();
			if(userInfo != null) {
				logContentBuilder.append("loginUserId:").append(userInfo.getUserId()).append(" ")
						.append("loginUserName:").append(userInfo.getName());
			} else {
				logContentBuilder.append("loginUser:-");
			}

		} catch (Exception e)
		{
			log.error("get access log string Exception ", e);
		}
		return logContentBuilder.toString();
	}

	private static String formatReturnValue(Object returnValue)
	{
		if (0 == SENSITIVE_FIELDS.size())
		{
			return JsonUtil.toJson(returnValue);
		} else
		{
			return JsonUtil.toJson(returnValue, SENSITIVE_FIELDS);
		}
	}

	private static String getIpAddr(HttpServletRequest request)
	{
		String ip = request.getHeader("x-forwarded-for");
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip))
		{
			ip = request.getHeader("Proxy-Client-IP");
		}
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip))
		{
			ip = request.getHeader("WL-Proxy-Client-IP");
		}
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip))
		{
			ip = request.getRemoteAddr();
		}
		return ip;
	}

	private static HttpServletRequest getHttpServletRequest()
	{
		return ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
	}
}
