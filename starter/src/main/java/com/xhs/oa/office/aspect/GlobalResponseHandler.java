package com.xhs.oa.office.aspect;

import com.dianping.cat.Cat;
import com.xhs.oa.office.common.GlobalResponse;
import com.xhs.oa.office.exception.BusinessException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.ConversionNotSupportedException;
import org.springframework.beans.TypeMismatchException;
import org.springframework.core.MethodParameter;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.http.converter.HttpMessageNotWritableException;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.web.HttpMediaTypeNotAcceptableException;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingPathVariableException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.ServletRequestBindingException;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.context.request.async.AsyncRequestTimeoutException;
import org.springframework.web.multipart.support.MissingServletRequestPartException;
import org.springframework.web.servlet.NoHandlerFoundException;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.xhs.oa.office.common.GlobalResponseEnum.*;
import static com.xhs.oa.office.common.GlobalResponseEnum.SERVER_ERROR;

@SuppressWarnings("NullableProblems")
@Order
@ControllerAdvice(annotations = {RestController.class, Controller.class})
public class GlobalResponseHandler implements ResponseBodyAdvice<Object> {

    public static final String HTTP_REQUEST_ATTRIBUTE_HAS_EXCEPTION = "response_has_exception";

    public static final String HTTP_REQUEST_ATTRIBUTE_RESPONSE_BODY = "response_body";

    public static final String HTTP_REQUEST_ATTRIBUTE_EXT_MAP = "ext_map";

    private static final Logger log = LoggerFactory.getLogger(GlobalResponseHandler.class);

    private static final Map<String, String> IGNORE_TYPES = new HashMap<>();

    static {
        IGNORE_TYPES.put(GlobalResponse.class.getName(), "1");
        IGNORE_TYPES.put(ResponseEntity.class.getName(), "1");
    }

    public GlobalResponseHandler() {
    }

    @Override
    public boolean supports(MethodParameter returnType,
                            Class<? extends HttpMessageConverter<?>> converterType) {
        return !IGNORE_TYPES.containsKey(returnType.getParameterType().getName());
    }

    @SuppressWarnings("unchecked")
    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType,
                                  MediaType selectedContentType,
                                  Class<? extends HttpMessageConverter<?>> selectedConverterType,
                                  ServerHttpRequest request,
                                  ServerHttpResponse response) {

        String returnTypeName = returnType.getParameterType().getName();
        GlobalResponse<Object> globalResponse = "void".equals(returnTypeName) ?
                GlobalResponse.success(null) :
                GlobalResponse.success(body);

        HttpServletRequest httpServletRequest = ((ServletRequestAttributes) Objects
                .requireNonNull(RequestContextHolder.getRequestAttributes()))
                .getRequest();
        httpServletRequest.setAttribute(HTTP_REQUEST_ATTRIBUTE_RESPONSE_BODY, globalResponse);

        Map<String, String> extMap = (Map<String, String>) httpServletRequest.getAttribute(HTTP_REQUEST_ATTRIBUTE_EXT_MAP);
        globalResponse.setExtMap(extMap);
        return globalResponse;
    }

    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler({ com.xiaohongshu.erp.common.exception.BusinessException.class })
    public <T> GlobalResponse<T> handleException(HttpServletRequest request, com.xiaohongshu.erp.common.exception.BusinessException e)
    {
        request.setAttribute(HTTP_REQUEST_ATTRIBUTE_HAS_EXCEPTION, true);
        log.error(e.getMessage(), e);
        GlobalResponse<T> globalResponse = businessFail(e);
        request.setAttribute(HTTP_REQUEST_ATTRIBUTE_RESPONSE_BODY, globalResponse);
        return globalResponse;
    }

    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler({ com.xhs.finance.exception.BusinessException.class })
    public <T> GlobalResponse<T> handleException(HttpServletRequest request, com.xhs.finance.exception.BusinessException e)
    {
        request.setAttribute(HTTP_REQUEST_ATTRIBUTE_HAS_EXCEPTION, true);
        log.error(e.getMessage(), e);
        GlobalResponse<T> globalResponse = businessFail(e);
        request.setAttribute(HTTP_REQUEST_ATTRIBUTE_RESPONSE_BODY, globalResponse);
        return globalResponse;
    }


    public static <T> GlobalResponse<T> businessFail(com.xhs.finance.exception.BusinessException e) {
        GlobalResponse<T> resp = new GlobalResponse();
        resp.setStatusCode(com.xiaohongshu.erp.common.framework.mvc.handler.GolbalResponseCodeEnum.EXCEPTION.getCode());
        resp.setErrorCode(e.getErrorCode());
        resp.setTraceLogId(Cat.getCurrentMessageId());
        resp.setAlertMsg(StringUtils.isEmpty(e.getAlertMsg()) ? com.xiaohongshu.erp.common.framework.mvc.handler.GolbalResponseCodeEnum.EXCEPTION.getDesc() : e.getAlertMsg());
        resp.setErrorMsg(e.getMessage());
        resp.setSuccess(false);
        return resp;
    }

    public static <T> GlobalResponse<T> businessFail(com.xiaohongshu.erp.common.exception.BusinessException e) {
        GlobalResponse<T> resp = new GlobalResponse();
        resp.setStatusCode(com.xiaohongshu.erp.common.framework.mvc.handler.GolbalResponseCodeEnum.EXCEPTION.getCode());
        resp.setErrorCode(e.getErrorCode());
        resp.setTraceLogId(Cat.getCurrentMessageId());
        resp.setAlertMsg(StringUtils.isEmpty(e.getAlertMsg()) ? com.xiaohongshu.erp.common.framework.mvc.handler.GolbalResponseCodeEnum.EXCEPTION.getDesc() : e.getAlertMsg());
        resp.setErrorMsg(e.getMessage());
        resp.setSuccess(false);
        return resp;
    }

    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler({ BusinessException.class })
    public <T> GlobalResponse<T> handleException(HttpServletRequest request, BusinessException e)
    {
        request.setAttribute(HTTP_REQUEST_ATTRIBUTE_HAS_EXCEPTION, true);
        log.error(e.getMessage(), e);
        GlobalResponse<T> globalResponse = GlobalResponse.businessFail(e);
        request.setAttribute(HTTP_REQUEST_ATTRIBUTE_RESPONSE_BODY, globalResponse);
        return globalResponse;
    }

    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler({Throwable.class})
    public <T> GlobalResponse<T> handleThrowable(HttpServletRequest request, Throwable e) {
        request.setAttribute(HTTP_REQUEST_ATTRIBUTE_HAS_EXCEPTION, true);
        log.error(e.getMessage(), e);
        GlobalResponse<T> globalResponse = GlobalResponse.exception(e, null);
        request.setAttribute(HTTP_REQUEST_ATTRIBUTE_RESPONSE_BODY, globalResponse);
        return globalResponse;
    }

    /**
     * 参数校验
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseBody
    public <T> GlobalResponse<T> methodArgumentNotValidException(MethodArgumentNotValidException e) {
        GlobalResponse<T> resp = new GlobalResponse<>();
        resp.setStatusCode(VALID_ERROR.getCode());
        resp.setErrorCode(VALID_ERROR.getCode());
        resp.setAlertMsg(wrapperBindingResult(e.getBindingResult()));
        resp.setErrorMsg(wrapperBindingResult(e.getBindingResult()));
        resp.setSuccess(false);
        return resp;
    }

    /**
     * 参数绑定异常
     *
     * @param e 异常
     * @return 异常结果
     */
    @ExceptionHandler(value = BindException.class)
    @ResponseBody
    public <T> GlobalResponse<T> handleBindException(BindException e) {
        GlobalResponse<T> resp = new GlobalResponse<>();
        resp.setStatusCode(VALID_ERROR.getCode());
        resp.setErrorCode(VALID_ERROR.getCode());
        resp.setTraceLogId(Cat.getCurrentMessageId());
        resp.setAlertMsg(wrapperBindingResult(e.getBindingResult()));
        resp.setSuccess(false);
        return resp;
    }

    private String wrapperBindingResult(BindingResult bindingResult) {
        StringBuilder msg = new StringBuilder();
        if (bindingResult == null) {
            return "bindingResult is null";
        }
        for (ObjectError error : bindingResult.getAllErrors()) {
            msg.append(", ");
            if (error instanceof FieldError) {
                msg.append(((FieldError) error).getField()).append(": ");
            }
            msg.append(error.getDefaultMessage() == null ? "" : error.getDefaultMessage());

        }
        return msg.substring(2);
    }


    /**
     * 未定义异常
     *
     * @param e 异常
     * @return 异常结果
     */
    @ExceptionHandler(value = Exception.class)
    @ResponseBody
    public <T> GlobalResponse<T> handleException(Exception e) {
        log.error("GlobalExceptionHandler handleException:{}", e.getMessage(), e);
        GlobalResponse<T> resp = new GlobalResponse<>();
        resp.setStatusCode(SERVICE_ERROR.getCode());
        resp.setErrorCode(SERVICE_ERROR.getCode());
        resp.setTraceLogId(Cat.getCurrentMessageId());
        resp.setAlertMsg(SERVICE_ERROR.getMessage());
        if (e.getMessage() == null) {
            resp.setErrorMsg(e.toString());
        } else {
            resp.setErrorMsg(e.getMessage());
        }
        resp.setSuccess(false);
        return resp;
    }

    /**
     * 服务器异常
     *
     * @param e 异常
     * @return 异常结果
     */
    @ExceptionHandler({
            NoHandlerFoundException.class,
            HttpRequestMethodNotSupportedException.class,
            HttpMediaTypeNotSupportedException.class,
            MissingPathVariableException.class,
            MissingServletRequestParameterException.class,
            TypeMismatchException.class,
            HttpMessageNotReadableException.class,
            HttpMessageNotWritableException.class,
            HttpMediaTypeNotAcceptableException.class,
            ServletRequestBindingException.class,
            ConversionNotSupportedException.class,
            MissingServletRequestPartException.class,
            AsyncRequestTimeoutException.class
    })
    @ResponseBody
    public <T> GlobalResponse<T> handleServletException(Exception e) {
        log.error("GlobalExceptionHandler handleServletException:" + e.getMessage());
        GlobalResponse<T> resp = new GlobalResponse<>();
        resp.setStatusCode(SERVER_ERROR.getCode());
        resp.setErrorCode(SERVER_ERROR.getCode());
        resp.setTraceLogId(Cat.getCurrentMessageId());
        resp.setAlertMsg(SERVER_ERROR.getMessage());
        resp.setErrorMsg(e.getMessage());
        resp.setSuccess(false);
        return resp;
    }
}