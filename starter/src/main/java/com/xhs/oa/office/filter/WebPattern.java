package com.xhs.oa.office.filter;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;


@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class WebPattern {
    /**
     * 不拦截url
     */
    private List<String> ignoreUrls;

    /**
     * 需要单独做访问权限校验的url
     */
    private List<String> authControlUrls;

}
