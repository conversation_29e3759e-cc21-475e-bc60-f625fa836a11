package com.xhs.oa.office.aspect;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * 请求监控实体
 */
@Data
@Builder
public class RequestMonitor implements Serializable {

    private static final long serialVersionUID = -1087369821615494817L;

    /**
     * 请求url
     */
    private String url;

    private String requestHeader;

    /**
     * 客户端请求出口ip
     */
    private String ip;

    /**
     * 请求id
     */
    private String requestId;

    /**
     * 请求参数
     */
    private String params;

    /**
     * 返回值数据
     */
    private String responseBody;

    /**
     * 日志级别 INFO, ERROR
     */
    private String logLevel;

    /**
     * 异常消息
     */
    private String exceptionMsg;

    /**
     * 请求方式：GET, POST
     */
    private String method;

    /**
     * 接口耗时，以ms为单位
     */
    private long costTime;

    private long startTime;
}
