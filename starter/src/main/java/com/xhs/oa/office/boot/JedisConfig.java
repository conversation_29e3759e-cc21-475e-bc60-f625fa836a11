package com.xhs.oa.office.boot;

import com.xiaohongshu.infra.utils.redis.JedisProxy;
import com.xiaohongshu.infra.utils.redis.SingleJedisPoolProvider;
import org.redisson.Redisson;
import org.redisson.config.Config;
import org.redisson.config.SingleServerConfig;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

/**
 * Jedis
 *
 * <AUTHOR>
 * @date 2020/3/8
 **/
@Configuration
public class JedisConfig
{

	@Value("${jedis.host}")
	private String host;

	@Value("${jedis.port}")
	private int port;

	@Value("${jedis.database}")
	private int database;

	@Value("${jedis.password}")
	private String password;

	@Value("${jedis.timeout}")
	private int timeout;

	@Bean(name = "jedisClient")
	public Jedis createJedis() throws Exception
	{
		JedisPool jedisPool = new JedisPool(new JedisPoolConfig(), host, port, timeout, password, database);
		JedisProxy jedisProxy = new JedisProxy(new SingleJedisPoolProvider(jedisPool, host, port));
		jedisProxy.setName(String.format("%s:%d", host, port));
		return jedisProxy.getJedis();
	}

	//Redisson 相关配置
	@Value("${redis.address}")
	private String address;

	@Value("${redis.password}")
	private String redissonPassword;

	@Bean("config4Redisson")
	public Config config(){
		Config config = new Config();
		SingleServerConfig singleServerConfig = config.useSingleServer();
		singleServerConfig.setAddress(address);
		singleServerConfig.setPassword(redissonPassword);
		return config;
	}

	@Bean
	public Redisson redisson(@Qualifier("config4Redisson") Config config){
		Redisson redisson = (Redisson) Redisson.create(config);
		return redisson;
	}

}
