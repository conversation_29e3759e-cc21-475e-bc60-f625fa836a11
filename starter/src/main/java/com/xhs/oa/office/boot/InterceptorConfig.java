package com.xhs.oa.office.boot;

import com.xhs.cache.RedisClient;
import com.xhs.oa.form.service.RbacxClientService;
import com.xhs.oa.office.interceptor.AccessLogInterceptor;
import com.xhs.oa.office.interceptor.AuthorityInterceptor;
import com.xhs.oa.office.interceptor.ResubmitInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;

@Configuration
public class InterceptorConfig extends WebMvcConfigurerAdapter {


    @Autowired
    private RedisClient redisClient;

    @Autowired
    private RbacxClientService rbacxClientService;


    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        //这里可以添加多个拦截器
        registry.addInterceptor(new ResubmitInterceptor(redisClient)).addPathPatterns("/**");
        registry.addInterceptor(new AuthorityInterceptor(redisClient,rbacxClientService));
        registry.addInterceptor(new AccessLogInterceptor());
        super.addInterceptors(registry);
    }
}

