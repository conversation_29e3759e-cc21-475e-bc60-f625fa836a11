

package com.xhs.oa.office.interceptor;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.xhs.cache.RedisClient;
import com.xhs.finance.exception.BusinessException;
import com.xhs.finance.framework.resubmit.Resubmit;
import com.xhs.finance.sso.UserInfoBag;
import com.xhs.finance.sso.model.UserInfo;
import com.xhs.finance.utils.StringUtils;
import com.xiaohongshu.erp.common.framework.resubmit.BodyReaderHttpServletRequestWrapper;
import com.xiaohongshu.erp.common.framework.resubmit.RequestHandleUtil;
import com.xiaohongshu.erp.common.framework.resubmit.v2.ResubmitV2;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.concurrent.TimeUnit;

/**
 * copy com.xhs.finance.framework.resubmit.ResubmitInterceptor 解决springboot2.0拦截静态资源类型转换问题
 */
public class ResubmitInterceptor extends HandlerInterceptorAdapter {
    private final String resubmit_ex = "resub_";
    private RedisClient redisClient;


    public ResubmitInterceptor(RedisClient redisClient) {
        this.redisClient = redisClient;
    }

    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if(handler instanceof HandlerMethod) {
            HandlerMethod method = (HandlerMethod) handler;
            Resubmit anno = method.getMethodAnnotation(Resubmit.class);
            ResubmitV2 anno2 = method.getMethodAnnotation(ResubmitV2.class);
            if(null != anno){
                int expairTime = anno.spaceTimeSecond();
                if (expairTime <= 0) {
                    return true;
                } else {
                    TimeUnit timeUnit = anno.timeUnit();
                    if (timeUnit == TimeUnit.SECONDS && (expairTime <= 0 || expairTime > 100)) {
                        expairTime = 3;
                    }

                    UserInfo user = UserInfoBag.get();
                    if (null == user) {
                        throw new RuntimeException("防重复提交只能在用户登录情况下生效");
                    } else {
                        String key = this.getKey(user.getToken(), request.getRequestURI().replaceAll("/", "_"));
                        boolean flag = this.redisClient.trySet(key, 1, (long) expairTime, timeUnit);
                        if (!flag) {
                            throw new BusinessException("请稍后再试.");
                        } else {
                            return true;
                        }
                    }
                }
            }else if(null != anno2){
                String pkId = anno2.pkId();
                int expairTime = anno2.spaceTimeSecond();
                TimeUnit timeUnit = anno2.timeUnit();
                if (expairTime <= 0)
                {
                    return true;
                }
                if (timeUnit == TimeUnit.SECONDS)
                {
                    if (expairTime <= 0 || expairTime > 100)
                    {
                        //防止错误设置
                        expairTime = 3;
                    }
                }
                String pkIdValue = getReqParam(request);
                if(StrUtil.isEmpty(pkIdValue)){
                    return true;
                }
                pkIdValue =  SecureUtil.md5(pkIdValue);
                pkIdValue = pkIdValue.substring(0,Math.min(pkIdValue.length(),20));
                String requestURI = request.getRequestURI();
                //控制redis key的长度
                requestURI = requestURI.substring(requestURI.length() - 20);
                StringBuffer key = new StringBuffer();
                key.append(resubmit_ex).append(requestURI).append(":").append(pkId == null ? "id" : pkId).append(":")
                        .append(pkIdValue == null ? "value" : pkIdValue).append(":");

                boolean flag = redisClient.trySet(key.toString(), 1, expairTime, timeUnit);
                if (!flag)
                {
                    throw new com.xiaohongshu.erp.common.exception.BusinessException("请稍后再试.");
                }
                return true;
            }else {
                return true;
            }
        }else {
            return true;
        }
    }

    private String getKey(String token, String path) {
        if (!StringUtils.isEmpty(path) && !StringUtils.isEmpty(token)) {
            return "resub_" + token + path;
        } else {
            throw new RuntimeException("防重复提交只能在用户登录情况下生效");
        }
    }

    public static String getReqParam(HttpServletRequest req)
    {
        String method = req.getMethod();
        if (RequestHandleUtil.METHOD_GET.equals(method))
        {
            return doGet(req);
        } else if (RequestHandleUtil.METHOD_POST.equals(method))
        {
            return doPost(req);
        }
        return null;
    }

    private static String doGet(HttpServletRequest req)
    {
        return req.getQueryString();
    }

    private static String doPost(HttpServletRequest request)
    {
        return ((BodyReaderHttpServletRequestWrapper) request).getBodyStr();
    }


}
