package com.xhs.oa.office.interceptor;

import com.xhs.cache.RedisClient;
import com.xhs.finance.framework.Env;
import com.xhs.finance.framework.auth.Authority;
import com.xhs.finance.sso.UserInfoBag;
import com.xhs.finance.sso.model.UserInfo;
import com.xhs.oa.form.service.RbacxClientService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Objects;

/**
 * 权限拦截器
 * <AUTHOR>
 *
 */
@Slf4j
public class AuthorityInterceptor extends HandlerInterceptorAdapter {
	private RedisClient redisClient;

	private RbacxClientService rbacxClientService;

	/**
	 * 登陆用户信息缓存
	 */
	public static final String SSO_LOGIN_USERINFO_AUTH_KEY = "sso_login_userinfo_auth_key_";


	public AuthorityInterceptor(RedisClient redisClient, RbacxClientService rbacxClientService){
		this.rbacxClientService = rbacxClientService;
		this.redisClient = redisClient;
	}

	public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
		if(handler instanceof HandlerMethod) {
			HandlerMethod method = (HandlerMethod) handler;
			Authority annotation = method.getMethodAnnotation(Authority.class);
			if (Objects.isNull(annotation)) {
				return true;
			} else {
				UserInfo user = UserInfoBag.get();
				if (null == user) {
					response.setStatus(403);
					response.getOutputStream().write("无用户信息，无法进行权限认证，请检查登录认证的例外配置".getBytes("UTF-8"));
					return false;
				} else if ("mork".equals(user.getName()) && !Env.prod.name().equalsIgnoreCase(System.getProperty("user.env"))) {
					return true;
				} else {
					try {
						String loginAuthKey = SSO_LOGIN_USERINFO_AUTH_KEY + user.getEmail();
						List<String> permissions = (List<String>) redisClient.get(loginAuthKey);
						if (!CollectionUtils.isEmpty(permissions) && permissions.contains(annotation.code())) {
							return true;
						} else {
							List<String> newPermissions = rbacxClientService.getPermissionCodesByEmailAndAppNames(user.getEmail());
							redisClient.set(loginAuthKey, newPermissions, 20);
							if (!CollectionUtils.isEmpty(newPermissions) && newPermissions.contains(annotation.code())) {
								return true;
							}
						}
						response.setStatus(403);
						String msg = "没有权限:" + annotation.code();
						response.getOutputStream().write(msg.getBytes("UTF-8"));
						response.addHeader("no-permission-code", annotation.code());
						response.addHeader("no-permission-type", "permission");
						// 前端可获取
						response.setHeader("Access-Control-Expose-Headers", "no-permission-code,no-permission-type");
						return false;
					} catch (Exception e) {
						log.error("AuthorityOaInterceptor error:{}", e);
						//do nothing
						return true;
					}
				}
			}
		}else{
			return true;
		}
	}
}
