package com.xhs.oa.office;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.xhs.finance.framework.mvc.handle.GlobalResponseHandler;
import com.xhs.rbac.client.config.RbacClientAutoConfiguration;
import com.xiaohongshu.infra.rpc.springboot.support.listener.ThriftApplicationListener;
import com.xiaohongshu.infra.utils.redis.RedisConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.FilterType;

import static com.xhs.finance.framework.AbstractApplication.start;

@EnableApolloConfig(value = {"application", "application_didi", "ds_mysql", "ds_mysql_sec"})
@SpringBootApplication(
		// 去除MongoDB自动读取以及若干自动注册操作操作
		exclude = { MybatisAutoConfiguration.class, RbacClientAutoConfiguration.class,SecurityAutoConfiguration.class, MongoAutoConfiguration.class, MongoDataAutoConfiguration.class})
@ComponentScan(basePackages = {"com.xiaohongshu.infra", "com.xhs", "com.xiaohongshu.fls.rpc", "com.xiaohongshu.media.services.sdk", "com.xiaohongshu.fls.sso.accessor"},
		excludeFilters = @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE,
				classes = {GlobalResponseHandler.class, RedisConfig.class, RbacClientAutoConfiguration.class}))
@EnableAspectJAutoProxy(exposeProxy = true)
@Slf4j
public class Application {

	public static void main(String[] args) {
		if (ArrayUtils.isEmpty(args)) {
			args = new String[]{"--spring.profiles.active=local", "--eds.hosts=***********:80;***********:80;**********:80"};
		}
		start(args);
		new SpringApplicationBuilder().listeners(new ThriftApplicationListener()).sources(Application.class).run(args);
	}



}