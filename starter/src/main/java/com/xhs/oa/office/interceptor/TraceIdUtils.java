package com.xhs.oa.office.interceptor;

import com.dianping.cat.Cat;
import com.xiaohongshu.infra.apm.tracing.Span;
import com.xiaohongshu.infra.apm.tracing.SpanContext;
import com.xiaohongshu.infra.apm.tracing.Tracer;
import com.xiaohongshu.infra.apm.tracing.zipkin.ZipkinTracer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;

import javax.servlet.http.HttpServletRequest;
import java.net.InetAddress;
import java.net.UnknownHostException;

@Slf4j
public class TraceIdUtils
{

	private static final String TRACE_ID_KEY = "traceId";

	private static final String SPAN_ID_KEY = "spanId";

	private static final String CAT_MESSAGE_ID_KEY = "catMessageId";

	private static final ZipkinTracer tracer = ZipkinTracer.getInstance();


	public static void initTraceForHttp(HttpServletRequest httpServletRequest)
	{

		SpanContext currentSpanContext = tracer.extract(httpServletRequest);
		if (currentSpanContext == null)
		{
			currentSpanContext = startSpanForHttp(httpServletRequest);
		}

		if (currentSpanContext == null)
		{
			log.warn("http全链路监控打标未成功开始");
			return;
		}

		MDC.put(TRACE_ID_KEY, currentSpanContext.getTraceId());
		MDC.put(SPAN_ID_KEY, currentSpanContext.getSpanId());
		MDC.put(CAT_MESSAGE_ID_KEY,Cat.getCurrentMessageId());
	}

	public static void finishTraceForHttp(Throwable throwable)
	{
		Span activeSpan = tracer.getActiveSpan();
		if (activeSpan != null)
		{
			activeSpan.finish(throwable);
		}
		MDC.remove(TRACE_ID_KEY);
		MDC.remove(SPAN_ID_KEY);
		MDC.remove(CAT_MESSAGE_ID_KEY);
	}

	private static SpanContext startSpanForHttp(HttpServletRequest httpServletRequest)
	{
		Tracer.SpanBuilder builder = tracer.buildSpan(httpServletRequest.getRequestURI()).kind(Span.Kind.SERVER)
				.setRemoteServiceName("HTTP");
		Span parent = tracer.getActiveSpan();
		if (parent != null)
		{
			builder.asChildOf(parent.getSpanContext());
		}
		InetAddress remoteAddress = null;
		if (StringUtils.isNotBlank(httpServletRequest.getRemoteAddr()))
		{
			try
			{
				remoteAddress = InetAddress.getByName(httpServletRequest.getRemoteAddr());
			} catch (UnknownHostException e)
			{
				log.error(e.getMessage(), e);
			}
		}
		if (remoteAddress != null)
		{
			builder.setRemoteAddress(remoteAddress, httpServletRequest.getRemotePort());
		}
		Span consumerSpan = builder.build();

		consumerSpan.start();
		return consumerSpan.getSpanContext();
	}

}
