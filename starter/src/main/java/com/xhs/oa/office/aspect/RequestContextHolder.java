package com.xhs.oa.office.aspect;

import javax.servlet.http.HttpServletRequest;

/**
 * Http Servlet Request持有器
 */
public class RequestContextHolder {

    private static final ThreadLocal<HttpServletRequest> REQUEST_LOCAL = new ThreadLocal<>();

    public static void set(HttpServletRequest request) {
        REQUEST_LOCAL.set(request);
    }

    public static HttpServletRequest get() {
        return REQUEST_LOCAL.get();
    }

    public static void remove() {
        REQUEST_LOCAL.remove();
    }
}
