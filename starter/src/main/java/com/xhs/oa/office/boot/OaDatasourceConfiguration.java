package com.xhs.oa.office.boot;

import com.alibaba.druid.pool.DruidDataSource;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.xhs.finance.utils.PropUtils;
import com.xiaohongshu.infra.utils.mybatis.MybatisConfig;
import com.xiaohongshu.infra.utils.mybatis.MybatisSQLInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.support.TransactionTemplate;

import javax.sql.DataSource;
import java.io.IOException;

@MapperScan(basePackages={
        "com.xhs.oa.report.mapper",
        "com.xhs.oa.workflow.mapper",
        "com.xhs.oa.itemCollection.mapper",
        "com.xhs.reimburse.xhsoa.mapper",
        "com.xhs.oa.travel_apply.mapper"
        }, sqlSessionFactoryRef = "oaSqlSessionFactory")
@Configuration
@Slf4j
public class OaDatasourceConfiguration extends MybatisConfig {

    @Autowired
    private Environment env;
    @Autowired
    private MybatisPlusInterceptor mybatisPlusInterceptor;
    @Autowired
    private MybatisSQLInterceptor catMybatisSQLInterceptor;

    @Bean(name="oaDruidDataSource", destroyMethod = "close")
    public DruidDataSource createOaDataSource() throws IOException {
        log.info("====== xhsoa DataSource init start ! =======");
        DruidDataSource dataSource = new DruidDataSource();
        dataSource.setDriverClassName(env.getProperty("jdbc.driver-class-name"));
        dataSource.setUrl(env.getProperty("jdbc.url"));
        dataSource.setUsername(env.getProperty("jdbc.username"));
        dataSource.setPassword(env.getProperty("jdbc.password"));
        dataSource.configFromPropety(PropUtils.load(OaDatasourceConfiguration.class, "druid.properties"));
        log.info("====== xhsoa DataSource init success ! =======");
        return dataSource;
    }

    @Bean(name="oaSqlSessionFactory")
    public SqlSessionFactory oaSqlSessionFactory() throws Exception{
        DruidDataSource dataSource = createOaDataSource();

        // 显示设置扫描mapper
        MybatisSqlSessionFactoryBean factoryBean = new MybatisSqlSessionFactoryBean();
        factoryBean.setDataSource(dataSource);
        factoryBean.setMapperLocations(
                new PathMatchingResourcePatternResolver()
                        .getResources("classpath*:sqlmap/**/*.xml")
        );

        factoryBean.setPlugins(mybatisPlusInterceptor, catMybatisSQLInterceptor);
        return factoryBean.getObject();
    }

    @Bean(name = "oaSqlSessionTemplate")
    public SqlSessionTemplate oaSqlSessionTemplate() throws Exception {
        SqlSessionFactory sqlSessionFactory = oaSqlSessionFactory();
        return new SqlSessionTemplate(sqlSessionFactory);
    }

    @Bean(name = "oaTransactionManager")
    public DataSourceTransactionManager oaTransactionManager(@Qualifier("oaDruidDataSource") DataSource dataSource) throws Exception {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "oaTransactionTemplate")
    public TransactionTemplate oaTransactionTemplate(@Qualifier("oaTransactionManager") DataSourceTransactionManager transactionManager) throws Exception {
        TransactionTemplate transactionTemplate = new TransactionTemplate();
        transactionTemplate.setTransactionManager(transactionManager);
        transactionTemplate.setIsolationLevel(TransactionDefinition.ISOLATION_READ_COMMITTED);
        transactionTemplate.setTimeout(30);
        return transactionTemplate;
    }
}
