package com.xhs.oa.office.boot;

import com.google.common.base.Predicates;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.schema.ModelRef;
import springfox.documentation.service.Parameter;
import springfox.documentation.builders.ParameterBuilder;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.util.ArrayList;
import java.util.List;

@Configuration
@EnableSwagger2
@Profile({"sit","local"})
public class Swagger2Config {
    @Bean
    public Docket coreApiConfig(){
        List<Parameter> globalParameters = new ArrayList<>();
        globalParameters.add(new ParameterBuilder()
                .name("X-Mork-Id")
                .description("令牌")
                .modelRef(new ModelRef("string"))
                .parameterType("header")
                .required(false)
                .build());

        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(adminApiInfo()).select()
                .apis(Predicates.or(
                        RequestHandlerSelectors.basePackage("com.xhs.oa.itemCollection.controller"),
                        RequestHandlerSelectors.basePackage("com.xhs.reimburse.controller"),
                        RequestHandlerSelectors.basePackage("com.xhs.oa.didi.controller")
                ))
                .build()
                .pathMapping("")
                .globalOperationParameters(globalParameters);
    }

    private ApiInfo adminApiInfo(){
        return new ApiInfoBuilder()
                .title("行政系统--api文档")
                .description("行政系统接口描述")
                .version("1.0")
                .build();
    }
}