package com.xhs.oa.office.boot;

import com.alibaba.druid.util.StringUtils;
import com.xhs.cache.RedisClient;
import com.xhs.cache.impl.RedissonImpl;
import com.xhs.finance.framework.mvc.bind.JsonBodyResolver;
import com.xiaohongshu.erp.common.workflow.CommonWorkflowService;
import org.apache.tomcat.util.http.LegacyCookieProcessor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.context.annotation.PropertySources;
import org.springframework.context.support.PropertySourcesPlaceholderConfigurer;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;

import java.util.List;


@Configuration
@ConfigurationProperties
@PropertySources({
		@PropertySource("classpath:props/${user.env}/server.properties"),
		@PropertySource("classpath:props/${user.env}/events.properties"),
		@PropertySource("classpath:props/${user.env}/jdbc-oaoffice.properties"),
		@PropertySource("classpath:props/${user.env}/jdbc.properties"),
		@PropertySource("classpath:props/${user.env}/redis.properties"),
		@PropertySource("classpath:props/${user.env}/wechat.properties"),
		@PropertySource("classpath:props/${user.env}/eds.properties"),
})
public class AppConfig {

	private String env = StringUtils.isEmpty(System.getProperty("user.env")) ? "local" : System.getProperty("user.env");

	@Value("${redis.key.ex}")
	private String redisKeyEx;

	@Value("${redis.json.config}")
	private String redisConfigJson;


	@Bean
	public static PropertySourcesPlaceholderConfigurer propertySourcesPlaceholderConfigurer() {
		return new PropertySourcesPlaceholderConfigurer();
	}

	@Bean
	public static WebMvcConfigurerAdapter springMvcResolver(){

		return new WebMvcConfigurerAdapter(){
			@Override
			public void addArgumentResolvers(List<HandlerMethodArgumentResolver> argumentResolvers) {
				argumentResolvers.add(new JsonBodyResolver());
			}
		};
	}

	@Bean
	public RedisClient getRedisClient(){
		return new RedissonImpl(redisKeyEx, redisConfigJson, env);
	}



	@Bean
	public WebServerFactoryCustomizer<TomcatServletWebServerFactory> cookieProcessorCustomizer() {
		LegacyCookieProcessor legacyCookieProcessor = new LegacyCookieProcessor();
		legacyCookieProcessor.setAllowEqualsInValue(true);
		legacyCookieProcessor.setSameSiteCookies("None");
		return (factory) -> factory.addContextCustomizers((context)->context.setCookieProcessor(legacyCookieProcessor));
	}
}
