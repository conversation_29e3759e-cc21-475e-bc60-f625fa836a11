package com.xhs.oa.office.filter;

import com.xhs.finance.sso.model.UserInfo;
import com.xhs.oa.login.service.LoginService;
import com.xhs.oa.office.utils.ApplicationContextUtils;
import com.xiaohongshu.fls.sso.accessor.context.UserContext;
import com.xiaohongshu.fls.sso.accessor.spi.AuthorizationTokenValidator;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class OfficeAuthorizationTokenValidator implements AuthorizationTokenValidator {
    @Override
    public boolean isValid(String s) {
        LoginService loginService = ApplicationContextUtils.getBean(LoginService.class);
        UserInfo userInfo = loginService.getAndRefreshLoginUserInfo(s);
        if (userInfo == null) {
            userInfo = loginService.getRedNameLoginUserInfo(s);
        }
        boolean valid = userInfo != null;
        if (valid) {
            UserContext.setUserEmail(userInfo.getEmail());
        }
        log.info("[OfficeAuthorizationTokenValidator] token={} isValid={}", s, valid);
        return valid;
    }
}
