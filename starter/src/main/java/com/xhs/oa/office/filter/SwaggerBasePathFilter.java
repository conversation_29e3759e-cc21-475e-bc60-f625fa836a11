package com.xhs.oa.office.filter;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.util.ContentCachingResponseWrapper;

@Order(0)
@WebFilter(filterName = "SwaggerBasePathFilter", urlPatterns = {"/v2/api-docs"})
@Component
public class SwaggerBasePathFilter implements Filter {
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;

        // 仅处理 Swagger 的 /v2/api-docs 请求
        if (httpRequest.getRequestURI().endsWith("/v2/api-docs")) {
            ContentCachingResponseWrapper responseWrapper = new ContentCachingResponseWrapper(httpResponse);
            chain.doFilter(request, responseWrapper);

            // 获取响应内容并替换 basePath
            byte[] content = responseWrapper.getContentAsByteArray();
            String modifiedContent = new String(content)
                    .replace("\"basePath\":\"/\"", "\"basePath\":\"\"");

            // 写回修改后的内容
            responseWrapper.resetBuffer();
            responseWrapper.getOutputStream().write(modifiedContent.getBytes());
            responseWrapper.copyBodyToResponse();
        } else {
            chain.doFilter(request, response);
        }
    }
}
