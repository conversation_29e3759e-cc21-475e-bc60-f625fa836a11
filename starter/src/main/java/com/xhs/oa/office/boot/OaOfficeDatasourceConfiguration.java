package com.xhs.oa.office.boot;

import com.alibaba.druid.pool.DruidDataSource;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.xhs.finance.utils.PropUtils;
import com.xiaohongshu.infra.apm.monitor.Monitor;
import com.xiaohongshu.infra.apm.tracing.Tracer;
import com.xiaohongshu.infra.utils.mybatis.MybatisConfig;
import com.xiaohongshu.infra.utils.mybatis.MybatisSQLInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;
import java.io.IOException;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName OaOfficeDatasourceConfiguration.java
 * @createTime 2025年02月12日 11:34:00
 */
@Slf4j
@MapperScan(basePackages = {
        "com.xhs.reimburse.mapper",
        "com.xhs.oa.travel_apply.office_mapper"
}, sqlSessionFactoryRef = "oaOfficeSqlSessionFactory")
@Configuration
public class OaOfficeDatasourceConfiguration extends MybatisConfig {

    @Autowired
    private Environment env;
    @Autowired(required = false)
    @Qualifier("defaultTracer")
    private Tracer defaultTracer;

    @Autowired(required = false)
    @Qualifier("defaultMonitor")
    private Monitor defaultMonitor;

    @Bean(name = "oaOfficeDruidDataSource", destroyMethod = "close")
    public DruidDataSource createOaOfficeDataSource() throws IOException {
        log.info("====== oaOffice DataSource init start ! =======");
        DruidDataSource dataSource = new DruidDataSource();
        dataSource.setDriverClassName(env.getProperty("oa.office.jdbc.driver-class-name"));
        dataSource.setUrl(env.getProperty("oa.office.jdbc.url"));
        dataSource.setUsername(env.getProperty("oa.office.jdbc.username"));
        dataSource.setPassword(env.getProperty("oa.office.jdbc.password"));
        dataSource.configFromPropety(PropUtils.load(OaOfficeDatasourceConfiguration.class, "druid.properties"));
        log.info("====== oaOffice DataSource init success ! =======");
        return dataSource;
    }

    @Bean(name = "oaOfficeSqlSessionFactory")
    public SqlSessionFactory oaOfficeSqlSessionFactory() throws Exception {
        MybatisSqlSessionFactoryBean sqlSessionFactoryBean = new MybatisSqlSessionFactoryBean();
        sqlSessionFactoryBean.setDataSource(createOaOfficeDataSource());
        sqlSessionFactoryBean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:/mapper/*.xml"));
        sqlSessionFactoryBean.setPlugins(mybatisPlusInterceptor(), catMybatisSQLInterceptor());
        return sqlSessionFactoryBean.getObject();
    }

    @Bean(name = "oaOfficeSqlSessionTemplate")
    public SqlSessionTemplate oaOfficeSqlSessionTemplate() throws Exception {
        SqlSessionFactory sqlSessionFactory = oaOfficeSqlSessionFactory();
        return new SqlSessionTemplate(sqlSessionFactory);
    }

    @Bean(name = "oaOfficeTransactionManager")
    public DataSourceTransactionManager oaTransactionManager(@Qualifier("oaOfficeDruidDataSource") DataSource dataSource) throws Exception {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor mpi = new MybatisPlusInterceptor();
        //添加分页拦截器
        mpi.addInnerInterceptor(new PaginationInnerInterceptor());
        //添加乐观锁拦截器
        mpi.addInnerInterceptor(new OptimisticLockerInnerInterceptor());
        return mpi;
    }

    @Bean
    public MybatisSQLInterceptor catMybatisSQLInterceptor() {
        MybatisSQLInterceptor mybatisSQLInterceptor = new MybatisSQLInterceptor();
        mybatisSQLInterceptor.setTracer(defaultTracer);
        mybatisSQLInterceptor.setMonitor(defaultMonitor);

        return mybatisSQLInterceptor;
    }
}