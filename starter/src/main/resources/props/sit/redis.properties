redis.key.ex=xhsoa
redis.json.config= {\
   "singleServerConfig":{\
      "idleConnectionTimeout":10000,\
      "pingTimeout":1000,\
      "connectTimeout":10000,\
      "timeout":3000,\
      "retryAttempts":3,\
      "retryInterval":1500,\
      "reconnectionTimeout":3000,\
      "failedAttempts":3,\
      "password":"crs-k1h6he9q:qHt3McDu^P63",\
      "subscriptionsPerConnection":5,\
      "clientName":null,\
      "address": "redis://redis-default.sit.xiaohongshu.com:30494",\
      "subscriptionConnectionMinimumIdleSize":1,\
      "subscriptionConnectionPoolSize":50,\
      "connectionMinimumIdleSize":10,\
      "connectionPoolSize":64,\
      "database":0,\
      "dnsMonitoring":false,\
      "dnsMonitoringInterval":5000\
   },\
   "threads":0,\
   "nettyThreads":0,\
   "codec":null,\
   "useLinuxNativeEpoll":false\
}

## jedis
jedis.host=redis-default.sit.xiaohongshu.com
jedis.port=30494
jedis.database=0
jedis.password=crs-k1h6he9q:qHt3McDu^P63
jedis.timeout=3000

redis.address: redis://redis-default.sit.xiaohongshu.com:30494
redis.password: crs-k1h6he9q:qHt3McDu^P63