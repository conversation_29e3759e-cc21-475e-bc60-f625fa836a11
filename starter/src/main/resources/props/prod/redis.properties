redis.key.ex=xhsoa
redis.json.config= {\
   "singleServerConfig":{\
      "idleConnectionTimeout":10000,\
      "pingTimeout":1000,\
      "connectTimeout":10000,\
      "timeout":3000,\
      "retryAttempts":3,\
      "retryInterval":1500,\
      "reconnectionTimeout":3000,\
      "failedAttempts":3,\
      "password":"crs-re5gep20:zLSLZ5Kr",\
      "subscriptionsPerConnection":5,\
      "clientName":null,\
      "address": "redis://***********:6379",\
      "subscriptionConnectionMinimumIdleSize":1,\
      "subscriptionConnectionPoolSize":50,\
      "connectionMinimumIdleSize":10,\
      "connectionPoolSize":64,\
      "database":0,\
      "dnsMonitoring":false,\
      "dnsMonitoringInterval":5000\
   },\
   "threads":0,\
   "nettyThreads":0,\
   "codec":null,\
   "useLinuxNativeEpoll":false\
}

## jedis
jedis.host=***********
jedis.port=6379
jedis.database=0
jedis.password=crs-re5gep20:zLSLZ5Kr
jedis.timeout=3000

redis.address=redis://***********:6379
redis.password=crs-re5gep20:zLSLZ5Kr