spring.application.name=oa-office
server.servlet.context-path=/
logging.config=classpath:logback-spring.xml
management.endpoints.enabled=true


spring.datasource.sql-script-encoding=utf-8
spring.datasource.platform=mysql
spring.datasource.initialization-mode=never
spring.http.encoding.force=true
spring.http.encoding.charset=UTF-8
spring.http.encoding.enabled=true
server.tomcat.uri-encoding=UTF-8
server.tomcat.max-threads=800
spring.profiles.active=local
spring.servlet.multipart.max-file-size=700MB
spring.servlet.multipart.max-request-size=700MB

mybatis.mapper-locations=classpath:sqlmap/**/*.xml

mock_user_pwd=123456



apollo.bootstrap.enabled=true
apollo.bootstrap.namespaces=application
spring.main.lazy-initialization=true

redschedule.appid=oaoffice
redschedule.domain=ep

##allow overriding
spring.main.allow-bean-definition-overriding=true

apm.tracing.name=zipkin

logging.level.com.xhs.reimburse.mapper=DEBUG

