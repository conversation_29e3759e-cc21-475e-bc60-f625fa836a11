allprojects {
    group = 'com.xhs.oa.office'
    version = '0.0.1-SNAPSHOT'
    repositories {
        maven {
            url "http://mvn.devops.xiaohongshu.com/repository/maven-public"
        }
    }
}

buildscript {
    ext {
        springBootVersion = '2.1.6.RELEASE'
        middlewareVersion = '2.4.20-RELEASE'
        root_pom_version = '3.3.0-MONTHLY-SNAPSHOT'
    }
    repositories {
        maven {
            url "http://mvn.devops.xiaohongshu.com/repository/maven-public"
        }
    }
    dependencies {
        classpath("org.springframework.boot:spring-boot-gradle-plugin:${springBootVersion}")
    }
}

subprojects {
    apply plugin: 'java'
    apply plugin: 'eclipse'
    apply plugin: 'idea'
    apply plugin: 'io.spring.dependency-management'

    sourceCompatibility = 1.8
    targetCompatibility = 1.8

    // java编译的时候缺省状态下会因为中文字符而失败
    [compileJava, compileTestJava, javadoc]*.options*.encoding = 'UTF-8'

    repositories {
        maven {
            url "http://mvn.devops.xiaohongshu.com/repository/maven-public"
        }
    }

    dependencies {
        implementation 'org.projectlombok:lombok:1.18.2'
        annotationProcessor 'org.projectlombok:lombok:1.18.2'
        implementation 'com.alibaba:dubbo:2.6.12'
    }

}
