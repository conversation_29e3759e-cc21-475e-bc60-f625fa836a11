# 机审打款实现步骤与测试方案

## 实现步骤拆分

### 阶段一：基础架构搭建 (1pd)
1. **灰度控制框架搭建**
   - 实现用户灰度控制逻辑
   - 添加单据类型限制配置
   - 创建灰度开关配置

2. **数据库表结构设计**
   - 设计 `form_payment_detail` 表结构
   - 设计支付状态枚举和状态机

3. **RPC接口封装**
   - 封装财务付款平台RPC调用
   - 实现联行号查询接口调用
   - 创建支付查询接口封装

### 阶段二：报销单据提交模块 (2pd)
1. **获取付款信息接口**
   - 实现付款主体信息获取
   - 实现境内/境外付款判断
   - 实现线上付款可行性校验

2. **前置校验接口**
   - 实现 `checkUnifyPayment` 接口调用
   - 构造校验请求参数
   - 处理校验响应结果
   - 替换原有 `paymentRpcService.batchCheckPayments` 接口

3. **参数构造通用方法**
   - 实现支付请求参数构造
   - 实现银行信息加密
   - 实现幂等键生成逻辑

### 阶段三：审核节点事件处理 (1pd)
1. **审核节点事件监听**
   - 监听出纳付款节点到达事件
   - 实现灰度路由逻辑

2. **支付发起逻辑**
   - 实现真实支付发起
   - 实现支付重试机制
   - 实现支付失败处理

3. **数据库更新**
   - 更新 `common_form` 表
   - 更新 `form_payment_detail` 表
   - 实现流程驳回逻辑

### 阶段四：支付消息事件处理 (2pd)
1. **消息消费机制**
   - 实现顺序消息消费
   - 实现失败重试机制（2次重试，共3次）
   - 实现报警机制

2. **支付状态处理**
   - 实现支付成功处理
   - 实现支付失败处理
   - 实现支付退票处理
   - 实现审批拒绝处理

3. **状态机实现**
   - 实现支付状态机转换
   - 实现业务单据状态更新

### 阶段五：定时任务实现 (1pd)
1. **完结业务单据定时任务**
   - 扫描支付完成单据
   - 实现15天退票期检查
   - 自动完结符合条件的单据

2. **对账定时任务**
   - 实现业务系统与支付系统对账
   - 处理状态不一致情况
   - 实现对账结果记录

## 验收测试Case设计

### 功能验收测试

#### 1. 报销单据提交测试
**Case 1.1: 正常境内付款校验**
- 前置条件：用户命中灰度，境内付款，金额<1000元
- 测试步骤：提交报销单
- 预期结果：校验通过，单据进入审核流程

**Case 1.2: 境外付款校验**
- 前置条件：用户命中灰度，境外付款
- 测试步骤：提交报销单
- 预期结果：校验失败，提示不支持境外付款

**Case 1.3: 未命中灰度用户**
- 前置条件：用户未命中灰度
- 测试步骤：提交报销单
- 预期结果：走原有校验逻辑

**Case 1.4: 银行信息缺失**
- 前置条件：用户命中灰度，银行信息不完整
- 测试步骤：提交报销单
- 预期结果：校验失败，提示银行信息不完整

#### 2. 审核节点事件测试
**Case 2.1: 正常支付发起**
- 前置条件：单据到达出纳付款节点，用户命中灰度
- 测试步骤：触发审核节点事件
- 预期结果：成功发起支付，更新支付明细表

**Case 2.2: 支付失败重试**
- 前置条件：首次支付失败
- 测试步骤：触发重试机制
- 预期结果：重试1次，仍失败则驳回单据

**Case 2.3: 未命中灰度用户**
- 前置条件：用户未命中灰度
- 测试步骤：触发审核节点事件
- 预期结果：走原有xhsoa处理逻辑

#### 3. 支付消息事件测试
**Case 3.1: 支付成功消息**
- 前置条件：支付成功
- 测试步骤：接收支付成功消息
- 预期结果：更新支付状态为成功，单据状态保持审核中

**Case 3.2: 支付失败消息**
- 前置条件：支付失败
- 测试步骤：接收支付失败消息
- 预期结果：驳回单据到发起人，驳回原因为支付失败

**Case 3.3: 支付退票消息**
- 前置条件：支付退票
- 测试步骤：接收支付退票消息
- 预期结果：驳回单据到发起人，驳回原因为退票

**Case 3.4: 消息消费失败重试**
- 前置条件：消息消费失败
- 测试步骤：触发重试机制
- 预期结果：重试2次后仍失败则报警

#### 4. 定时任务测试
**Case 4.1: 完结业务单据**
- 前置条件：支付成功超过15天，无退票
- 测试步骤：执行定时任务
- 预期结果：自动完结单据

**Case 4.2: 对账任务**
- 前置条件：业务系统与支付系统状态不一致
- 测试步骤：执行对账任务
- 预期结果：同步状态，记录对账结果

## 自测Case设计

### 单元测试
1. **参数构造测试**
   - 测试支付请求参数构造正确性
   - 测试银行信息加密正确性
   - 测试幂等键生成唯一性

2. **状态机测试**
   - 测试支付状态转换正确性
   - 测试业务单据状态更新正确性

3. **灰度控制测试**
   - 测试用户灰度命中逻辑
   - 测试单据类型限制逻辑

### 集成测试
1. **RPC接口测试**
   - 测试财务付款平台接口调用
   - 测试联行号查询接口调用
   - 测试支付查询接口调用

2. **消息消费测试**
   - 测试消息接收和处理
   - 测试重试机制
   - 测试报警机制

3. **定时任务测试**
   - 测试完结任务执行
   - 测试对账任务执行

### 端到端测试
1. **完整流程测试**
   - 从报销单提交到支付完成的完整流程
   - 验证各环节数据一致性

2. **异常流程测试**
   - 支付失败流程
   - 退票流程
   - 网络异常恢复流程

## 测试环境要求

### 环境配置
1. **Beta环境**
   - 配置财务付款平台Beta环境
   - 配置消息队列Beta环境
   - 配置数据库Beta环境

### 测试数据准备
1. **用户数据**
   - 准备命中灰度用户
   - 准备未命中灰度用户
   - 准备不同权限用户

2. **报销单数据**
   - 准备境内付款报销单
   - 准备境外付款报销单
   - 准备不同金额报销单

3. **银行数据**
   - 准备常用银行信息
   - 准备联行号数据
   - 准备测试账户信息
