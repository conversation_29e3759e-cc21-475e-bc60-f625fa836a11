# 机审打款技术方案

## 背景
报销机审是员工面临的“报销操作难、流程长、体验差”整体问题中解决方案的一环
- For员工：通过把明确的审核规则放在提交前校验+给到明确引导的方式让无风险的报销单免去人工审核，结合自动打款的能力，实现“提报-快速到账”的效果，解决的是员工因缺少材料/材料有误易被回退的低效问题与小额报销等待打款周期长的体验问题
- For业务：大幅降低职能侧在审核、出纳打款中的人力投入成本，人工只需核验有异常风险的报销单

PRD：
- [【WIP-P0】报销单对接自动打款](https://docs.xiaohongshu.com/doc/d2b0f389d180da8cccdc840093e123f7)
- [【PRD】报销机审](https://docs.xiaohongshu.com/doc/c9013fb19e9a5411c5a145e62598034e)
pingcode：


## 整体流程

现有流程：

对接财务付款平台后流程：
新报销打款能力在新系统中，逐步解耦打款与老OA系统。


## 详细设计

### 1. 报销单据提交
从财务视角看报销单提交，主要需要做两件事：
1. 构造财务相关信息，例如付款主体、境内外付款等打款相关信息
2. 做打款前校验，单据提交过程中不涉及到打款信息修改，避免最终因为打款失败导致单据驳回，在提交时需要校验打款信息尽量完善、合法。

#### 1.1 获取付款信息
返回信息为：
- 是否走线上付款
- 爱马仕code=付款公司主体编码
- 付款方式，境内、境外
- 如果不能走线上付款，原因是什么？
当识别到不能线上付款时以及境外打款时直接报错？

#### 1.2 前置校验接口/打款接口
参考文档：[资金付款产品1.0](https://docs.xiaohongshu.com/doc/fc96d996c6a9685620c62a2cc2c295f1)
com.xiaohongshu.fls.rpc.finance.cashiercenter.PaymentService.Iface#unifyPaymen / checkUnifyPayment
| 参数 | 释义 | 业务赋值 |
| ---- | ---- | ---- |
| sourceType | 系统来源  报账平台自行定义 这边以小额打款的系统来源为例 | OA-OFFICE |
| sourceNo | 业务单号（幂等键） 对报账平台而言全局唯一 | 单号+ 游标<br/>一个单据可能多次打款，游标递增<br/>相同sourceNo可以重试？ 可以重试 |
| expectPayTime | 期望付款时间 可为空 如果下单时间小于期望付款时间，则到付款时间才进行付款 | 无 |
| sourceBizType | 业务类型  保障平台自行定义 这边以小额打款的业务类型为例 | 待分配 |
| paymentSubject | 主体信息（即上游付款主体的机构编码） | 用户选择，来源公司主体列表，已确认报销使用到的是财务域子集 |
| paymentAmount | 金额   单位为元 | 报销金额 |
| paymentCurrency | 币种 | 默认CNY |
| beneficiaryChannelName | 分支机构名称 | 有银行名称，例如：招商银行是否满足？总行OK |
| beneficiaryAccountNumber | 收款账 | 用户提交报销表单中带有此信息 |
| beneficiaryName | 收款人名称 | 用户提交报销表单中带有此信息 |
| paymentDetails | 打款备注  传给渠道侧的 | 报销单号 |
| remark | 打款备注 不传给渠道，小红书内部使用 | 报销单号 |
| masterSourceNo | 上游原始单号（和业务单号的区别，上游系统内部自身的单号，在和收付中心交互的时候，可以不传） | 报销单号 |
| sourceOrderDetailUrl | 源单地址，可以不传 | 无 |
| offlinePay | 是否人工付款 可以不传，默认是不支持人工付款 | 无 |
| offlinePayReason | 和上面的是否人工付款原因配合使用 | 无 |
| splitPay | 是否拆单 可以不传，默认是不拆单，一般上游业务也没有拆单诉求 | 无 |
| domesticOrOverseas | 境内打款传DOMESTIC  反之境外付款传OVERSEAS | DOMESTIC |
| bankRequest.beneficiaryBankCode | 联行号 | 待确认，如何获取？<br/>[联行号查询接口](https://docs.xiaohongshu.com/doc/169edf9b5bd0aaed26b7068b455b5eff)<br/>取返回值中的superBankCode字段 |
| bankRequest.publicOrPrivate | 对公或者对私 | PRIVATE |
| password | 加密密钥<br/>加密参考：[资金付款产品1.0/代码块](https://docs.xiaohongshu.com/doc/fc96d996c6a9685620c62a2cc2c295f1?anchorLinkId=block-1845655ba8c34958a3ab9889fe98de6f) | 待分配 |
sit是否可以联调全链路？beta上才能验证。
响应：
{
    code:0
    success:true // 判断是否成功
    failReason:
}

替换原爱马仕打款接口校验：
- paymentRpcService.batchCheckPayments 原校验接口
- 此处不直接下掉，使用灰度控制
构造请求参数作为通用方法提供校验与真实打款共用。


### 2. 审核节点变化事件
处理节点到达出纳付款节点，此处需要真实发起付款。
实现注意点：
1. 发起付款时，需要根据报错判断是否要重试，相同幂等key（支付单号）支持重试。幂等key规则为单号+游标，记录在付款明细表中form_payment_detail。
2. 支付失败，如果没有失败原则则进行1次重试，如果仍然失败则将支付失败原因作为流程驳回原因写入流程中。驳回到发起人节点。
3. 结束前更新common_form以及form_payment_detail表。
4. 需要添加灰度控制，命中灰度新逻辑oaoffice处理，没命中灰度老逻辑xhsoa处理。


### 3. 支付消息事件
发起支付后监听财务事件获取支付结果
注意消息消费机制：顺序消费，且失败重试2次共3次，仍然失败则报警。eventbus顺序消费参考：[顺序消息](https://docs.xiaohongshu.com/doc/dd3eee49d4065ff1898801771d85162c)
消息报文
@Data
public class PaymentNotifyDto extends RocketMqPaymentResultDto{
    //来源类型 打款接口中的sourceType
    private String sourceType; 
 
    //来源业务类型 打款接口中的sourceBizType
    private String sourceBizType;
 
    //来源编号 打款接口中的sourceNo
    private String sourceNo;
 
    //小红书编号
    private String xhsNo;
 
    //状态，枚举见PaymentNotifyStatusEnum
    private String status;
 
    //业务状态实际发生时间
    private Long occurrence;
 
    //错误编码
    private int errorCode;
 
    //子错误编码（明细错误码）
    private String subErrorCode;
 
    //错误信息
    private String errorMsg;
 
}

消息发送是有序的么，消费方按照顺序消费即可？ 有序发送。
支付状单态机
![图片](https://xhs-doc.xhscdn.com/1040025031kbhlvak200c78bo9k?imageView2/2/w/1600 "")
支付单审批拒绝：业务单驳回到发起人，驳回原因为审批拒绝原因。
支付单取消付款：不存在这种情况，业务不会主动调用取消付款。
支付单付款失败：业务单驳回到发起人，驳回原因为付款失败原因。
支付单付款成功：业务单据仍然为审核中。如果15天内没有发生退票则完结业务单据。
支付单据付款退票：业务单据驳回到发起人，驳回原因为退票原因。


#### 3.1 定时任务完结业务单据
当支付成功后不会实时完成单据状态，还有可能发生退票。所以需要定时任务扫描支付完成单据，支付完成时间小于当前时间-15天，如果单据仍然为出纳付款，并且支付状态为付款成功，则完成当前单据。


#### 3.2 定时任务对账
业务系统与支付系统对账，实现支付状态的一致性。
查询接口：[com.xiaohongshu.fls.rpc.finance](com.xiaohongshu.fls.rpc.finance).cashiercenter.PaymentService.Iface#queryPaymentOrderBySourceNo
  PaymentQueryBySourceNoRequest request = new PaymentQueryBySourceNoRequest();
            request.setSourceType(SOURCE_TYPE);
            request.setSourceNo(sourceNo);
            request.setQuerDetail(queryDetail);
          
 PaymentQueryBySourceNoResponse   paymentQueryBySourceNoResponse=paymentService.queryPaymentOrderBySourceNo(ContextHelper.getContext(), request);

   状态机终态：
   PAY_REJECT("PAY_REJECT","审批拒绝"),
   PAY_CANCEL("PAY_CANCEL","取消付款"),
   PAY_SUCCESS("PAY_SUCCESS", "付款成功"),
   PAY_FAIL("PAY_FAIL", "付款失败"),
   POLYMORPHIC_FINAL_STATE("POLYMORPHIC_FINAL_STATE", "多态终态"),
   PAY_RETURN("PAY_RETURN", "付款退票");
 
 



### 灰度
本次需求涉及到的链路改动较大，添加灰度控制，实现出现问题可以恢复之前链路，灰度by user灰度即可。
灰度涉及到，打款校验、打款、审核事件回调、定时任务扫描，同时添加单据类型限制。


## 排期
| 开发内容 | 估时 |
| ---- | ---- |
| 校验、打款实现 | 2 |
| 审核节点事件监听 | 1 |
| 支付消息事件处理 | 2 |
| 自测&联调 | 2 |
共计7pd