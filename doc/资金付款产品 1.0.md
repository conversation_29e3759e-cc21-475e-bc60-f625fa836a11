# 资金付款产品1.0 副本

##### 产品迭代记录
| 修改日期 | 修改人 | 产品版本号 | 文档版本号 | 修改内容 |
| ---- | ---- | ---- | ---- | ---- |
| 2025-3-19 | 沈梦婷 | 1.0 | 0.8 | 初始化 |
|  |  |  |  |  |


## 1、产品描述

##### 资金付款产品：
在中国&中国香港市场，建设统一、高效、稳定、安全、全面、确定的资金付款服务平台，具备接入、受理、路由、重试、审批、汇路、回单、退票关联的全流程标准能力，通过高质量整合业务单+付款单+渠道单（含退票），向前支撑公司各经营板块的业务付款需求，向内满足头寸统一&快速排查&核算提效的管理需求。实现业务身份对齐、业务入驻审批、付款账户统一、路由决策净化、付款审批高效的目标，打造一个更加高效、智能、安全且功能完备的统一付款平台。

### 产研运维团队
| 部门 | 岗位 | 人员 |
| ---- | ---- | ---- |
| 财务平台产品组 | 产品经理 | 九霄、沈梦婷 |
| 资金技术组 | 资金研发 | 业笙、吉修 |


## 2、能力组成
| 能力定义 | 能力展现方式 | 能力描述 | 能力依赖 | 能力使用限制 |
| ---- | ---- | ---- | ---- | ---- |
| 统一付款能力 | 管理付款单 | 统一资金付款单，包括业务/资金/流水单号、状态、手工维护 | 主体主数据 |  |
|  | 付款账户路由支持落地线下 | 支持落地线下通过SAAS端付款 |  |  |
|  | 付款受理迭代 | 全局支持：指定付款账户、指定流水备注、指定人工处理 |  |  |
|  | 付款汇路决策迭代 | 迭代HK地区的汇路决策，资金汇路决策从网关层合并到付款产品（境外） | 资金网关、支付网关、文件网关 |  |
|  | One Account | 下架爱马仕账户表，收款&付款路由，从Treasury表取账户 | Treasury账户 |  |
|  | 付款入驻配置工作台 | 形成业务入驻付款的管理工作台 |  |  |
|  | 付款查询迭代 | 支持电子回单 | 付款查询接口 |  |
|  | 付款场景业财身份统一 | 统一全链路业财身份 | 业财身份 |  |
|  | 付款审批迭代 | 串联RedFlow形成符合资金特色的单一审批/批量审批能力 | RedFlow |  |
|  | 统一付款 | 将OA、CRM返点支付、MCN佣金提现、资金调拨多场景统一收敛至付款产品。 | 新版联行号接口、汇率服务产品等 |  |
|  | 付款报表 |  |  |  |


## 3、支持的渠道
暂无

## 4、关联产品
付款场景关联：OA、苍穹-结算单付款管理、爱马仕-社区商业化、MCN平台、汇率服务产品
审批关联：Redflow、天玑-付款审批


## 5、典型场景

#### 场景一、付款审批
需求：基于现有的OA能力不足的情况，高效化付款审批。
支持批量审批、指定人工、批量查询、基于业务单号进行快速检索，展示币种和金额。


#### 场景二、账户、路由与业财身份的统一
需求：付款产品完全使用CashierCenter账户表的瓶颈、渠道决策无效化的问题、业务身份命名混乱的困境亟待解决，需要优化陈年路径，实现账户、路由与业财身份的统一。
从CashierCenter账户表切换至TReasury账户表，付款业务表实现统一，业财身份全局一致化。


#### 场景三、各类出金场景的收敛
需求：OA资金审批、CRM返点支付、MCN佣金提现、资金调拨等出金场景分散，审批入口多样，需统一化出金场景收敛至付款产品。
MCN佣金提现：爱马仕审批下架，MCN佣金接入新付款产品
CRM返佣：CRM侧资金审批下架，CRM返佣接入付款产品、新版联行号接口、汇率服务产品。
OA：比照报账平台模式，资金审批集中至付款产品，线上线下决策在资金付款产品侧。


## 6、付款单状态机


## 7、接口列表
| 接口名称 | 主要业务参数 | 地址链接 |
| ---- | ---- | ---- |
| 境内分支&联行号查询接口 | 开户行名称 bankName ：模糊匹配<br/>联行号 cnapsBankCode：精准查询<br/>省市编码 areaCode <br/>超级网银号 superBankCode | com.xiaohongshu.finance.rpc.rftreasury.CnapsCodeQueryServiceRpc.Iface#queryCnapsCodeInfoByCodeOrBankName |
| 打款前置校验接口 | 详见下方 | [com.xiaohongshu.fls.rpc.finance](com.xiaohongshu.fls.rpc.finance).cashiercenter.PaymentService.Iface#checkUnifyPayment |
| 打款接口 | 详见下方 | [com.xiaohongshu.fls.rpc.finance](com.xiaohongshu.fls.rpc.finance).cashiercenter.PaymentService.Iface#unifyPayment |
| 支付状态查询接口 | 来源系统sourceType<br/>                      来源业务单号sourceNo<br/>                     是否查询子单明细queryDetail | [com.xiaohongshu.fls.rpc.finance](com.xiaohongshu.fls.rpc.finance).cashiercenter.PaymentService.Iface#queryPaymentOrderBySourceNo |
| 取消付款接口 | 来源系统sourceType<br/>                      来源业务单号sourceNo | [com.xiaohongshu.fls.rpc.finance](com.xiaohongshu.fls.rpc.finance).cashiercenter.PaymentService.Iface#cancelPaymentOrderBySourceNo |

### 7.1 核心接口请求示例
 maven依赖：
<dependency>
    <groupId>com.xiaohongshu.fls.thrift</groupId>
    <artifactId>lib-thrift-cashiercenter</artifactId>
    <version>0.12.3</version>  
</dependency>     



####        0.打款前置校验接口：
      本接口目的是校验收款信息要素是否符合规范。是一个前置校验接口。可以最大限度保证付款的成功率。
      
          com.xiaohongshu.fls.rpc.finance[](com.xiaohongshu.fls.rpc.finance).cashiercenter.PaymentService.Iface#checkUnifyPayment
             对应的入参和下面的打款接口一致。

####     1.打款接口：
      com.xiaohongshu.fls.rpc.finance[](com.xiaohongshu.fls.rpc.finance).cashiercenter.PaymentService.Iface#unifyPayment

#####            1.境内打款入参：
{
    "sourceType": "USERQMS",   // 系统来源  报账平台自行定义 这边以小额打款的系统来源为例
    "sourceNo": "userqms_4159105_1018895187806613504",  // 业务单号（幂等键） 对报账平台而言全局唯一
    "expectPayTime" :"2025-01-01 10:00:00" // 期望付款时间 可为空 如果下单时间小于期望付款时间，则到付款时间才进行付款
    "sourceBizType": "KYC",    // 业务类型  保障平台自行定义 这边以小额打款的业务类型为例
    "paymentSubject": "CPY_SH", // 主体信息（即上游付款主体的机构编码）
    "paymentAmount": "0.17",    // 金额   单位为元
    "paymentCurrency": "CNY",   // 币种   
    "beneficiaryChannelName": "中国银行股份有限公司广州员村支行",  // 分支机构名称
    "beneficiaryAccountNumber": "************", // 收款账号
    "beneficiaryName": "大鹏（广州）咨询服务有限公司",  // 收款人名称
    "paymentDetails": "小额对公打款", // 打款备注  传给渠道侧的
     "remark":"小红书内部流转备注" // 打款备注 不传给渠道，小红书内部使用
     "masterSourceNo":"PO123KJK2" // 上游原始单号（和业务单号的区别，上游系统内部自身的单号，在和收付中心交互的时候，可以不传） 
     "sourceOrderDetailUrl":"https://fin.sit.xiaohongshu.com/expenses/paymentApply/detail/CFK202503130254814211" // 源单地址，可以不传
     "offlinePay":false // 是否人工付款 可以不传，默认是不支持人工付款
     "offlinePayReason":"人工付款原因" // 和上面的是否人工付款原因配合使用
    "splitPay":false // 是否拆单 可以不传，默认是不拆单，一般上游业务也没有拆单诉求
    "domesticOrOverseas":"DOMESTIC"   // 境内打款传DOMESTIC  反之境外付款传OVERSEAS
    "digitalDigest": "8dbf4d67cc77928b4fe130de8d840f47", // 加密后的摘要信息
    "bankRequest": {
        "beneficiaryBankCode": "************", // 联行号
        "publicOrPrivate": "PUBLIC" // 对公或者对私  （PUBLIC OR PRIVATE） 公司传PUBLIC 个人传PRIVATE
    }
}

   

#####     2.境外打款入参：
 {
    "sourceType": "CRM", // 系统来源
    "sourceNo": "CPMR20241122000063",  // 业务单号（幂等键）
    "appointedPayTime":"2024-11-25 08:00:00", // 指定付款时间（可为空）（当前时间还未到指定付款时间则会pending到指定付款时间才进行付款）
    "sourceBizType": "CRM_COST_PAYMENT", // 业务类型 
    "paymentSubject": "CPY_YT", // 主体信息（即上游付款主体的机构编码）
    "paymentAmount": "5253.03",  // 金额   单位为元
    "paymentCurrency": "CNY",  // 币种   
    "beneficiaryChannelName": "UNICREDIT SPA",  // 分支机构名称
    "beneficiaryAccountNumber": "***************************",  // 收款账号
    "beneficiaryName": "VALUE CHINA S.R.L.", // 收款人名称
     "paymentDetails": "dkjfkjk1", // 打款备注  传给渠道侧的 境外场景必须传纯英文数字
     "remark":"小红书内部流转备注" // 打款备注 不传给渠道，小红书内部使用
     "masterSourceNo":"PO123KJK2" // 上游原始单号（和业务单号的区别，上游系统内部自身的单号，在和收付中心交互的时候，可以不传） 
     "sourceOrderDetailUrl":"https://fin.sit.xiaohongshu.com/expenses/paymentApply/detail/CFK202503130254814211" // 源单地址，可以不传
     "offlinePay":false // 是否人工付款 可以不传，默认是不支持人工付款
     "offlinePayReason":"人工付款原因" // 和上面的是否人工付款原因配合使用
     "splitPay":false // 是否拆单 可以不传，默认是不拆单，一般上游业务也没有拆单诉求
     "domesticOrOverseas":"OVERSEAS"   // 境内打款传DOMESTIC  反之境外付款传OVERSEAS
     "digitalDigest": "6691f2955563f99e92e74d082f160526", // 加密后的摘要信息
     "bankRequest": {
        "beneficiaryBankCodeType": "SWIFT", // bankCode类型 境外都是传SWIFT 境内默认不传类型
        "beneficiaryBankCode": "UNCRITM1NN0", // SWIFT_CODE
         "chargesIndicator": "BEN" ,  // 服务费承担方  SHR|BEN|OUR|  
        "beneficiaryAddress": "FLAT/RM 08-09 19/F  LIPPO CENTRE TOWER 189 QUEENSWAY ADMIRALTY  HK",  // 境外需要填写收款银行地址。
        "publicOrPrivate": "PUBLIC"  // 对公或者对私  （PUBLIC OR PRIVATE） 公司传PUBLIC 个人传PRIVATE
        
    }
}

请求示例： 
  加密用到的秘钥PASSWORD="iwiqeisfbp"   (分配给你们的秘钥，请勿外传)
 
           UnifyPaymentApplyRequest unifyPaymentApplyRequest = new UnifyPaymentApplyRequest();
          // 你们系统的名称，ones上定义的系统名称，这个必须是一致的。
        unifyPaymentApplyRequest.setSourceType("TREASURY");
          //  幂等键，全局必须唯一，建议是带有业务语义的单号
        unifyPaymentApplyRequest.setSourceNo("KYC202408261923a");
          //  传业务类型KYC （小额打款产品能力）
        unifyPaymentApplyRequest.setSourceBizType("KYC");
          // 写死传XHS  （小额打款产品对应的付款主体）
        unifyPaymentApplyRequest.setPaymentSubject("XHS");
          //  付款金额 单位是元，小数点后保留2位有效数字，比如1元如下
        unifyPaymentApplyRequest.setPaymentAmount("1.00");
          // 付款币种  必传 ，当前场景传CNY （人明币）
        unifyPaymentApplyRequest.setPaymentCurrency("CNY");
          //  收款账号
        unifyPaymentApplyRequest.setBeneficiaryAccountNumber("*********");
          //  收款账号类型
        unifyPaymentApplyRequest.setBeneficiaryAccountType("BANK");
          //  收款人姓名
        unifyPaymentApplyRequest.setBeneficiaryName("小红书科技有限公司");
          //  收款人开户行名称 来自联行号查询的结果
        unifyPaymentApplyRequest.setBeneficiaryChannelName("中国民生银行股份有限公司上海市北支行");
          //  摘要，必传  （更新下，不需要）
         unifyPaymentApplyRequest.setPaymentDetails("招行周末出金验证");
 
        UnifyPaymentApplyBankRequest unifyPaymentApplyBankRequest = new UnifyPaymentApplyBankRequest();
        //. 联行号，来自联行号接口查询的结果    
    unifyPaymentApplyBankRequest.setBeneficiaryBankCode("************");
         // 对公对私    对公打款传PUBLIC,对私传PUBLIC，对私传PRIVATE 
        unifyPaymentApplyBankRequest.setPublicOrPrivate("PUBLIC");
        unifyPaymentApplyRequest.setBankRequest(unifyPaymentApplyBankRequest);
       
         // 生成摘要信息，必传，参考下面buildUnifyPaymentReq和generateAbstract 的示例
        String digest = generateAbstract(buildUnifyPaymentReq(unifyPaymentApplyRequest), PASSWORD);
     
        unifyPaymentApplyRequest.setDigitalDigest(digest);
  
                UnifyPaymentApplyResponse unifyPaymentApplyResponse = paymentService.unifyPayment(ContextHelper.getContext(), unifyPaymentApplyRequest);
    
 
 
 
        响应结果：{
code:0
success:true
failReason:
}
通过success字段判定。
 

  buildUnifyPaymentReq方法定义：
/**
     * 统一付款加签字段拼接
     *
     * @param request
     * @return
     */
    private static String buildUnifyPaymentReq(UnifyPaymentApplyRequest request) {
        StringBuilder sb = new StringBuilder();
        sb.append(StringUtils.trimToEmpty(request.getSourceType()));
        sb.append(StringUtils.trimToEmpty(request.getSourceNo()));
        sb.append(StringUtils.trimToEmpty(request.getSourceBizType()));
        sb.append(StringUtils.trimToEmpty(request.getPaymentSubject()));
        if (CollectionUtils.isNotEmpty(request.getPaymentChannelList())) {
            for (String channel : request.getPaymentChannelList()) {
                sb.append(StringUtils.trimToEmpty(channel));
            }
        }
        sb.append(StringUtils.trimToEmpty(request.getPaymentAccountNumber()));
        sb.append(StringUtils.trimToEmpty(request.getPaymentName()));
        sb.append(StringUtils.trimToEmpty(request.getPaymentAmount()));
        sb.append(StringUtils.trimToEmpty(request.getBeneficiaryChannelName()));
        sb.append(StringUtils.trimToEmpty(request.getBeneficiaryAccountNumber()));
        sb.append(StringUtils.trimToEmpty(request.getBeneficiaryAccountType()));
        sb.append(StringUtils.trimToEmpty(request.getBeneficiaryName()));
        sb.append(StringUtils.trimToEmpty(request.getPaymentDetails()));
        sb.append(StringUtils.trimToEmpty(request.getVersion()));
        if (request.getExtendRequest() != null) {
            sb.append(StringUtils.trimToEmpty(request.getExtendRequest().getBeneficiaryCellphone()));
            sb.append(StringUtils.trimToEmpty(request.getExtendRequest().getBeneficiaryCertNo()));
            sb.append(StringUtils.trimToEmpty(request.getExtendRequest().getBeneficiaryCertType()));
            sb.append(StringUtils.trimToEmpty(request.getExtendRequest().getBeneficiaryNationality()));
            sb.append(StringUtils.trimToEmpty(request.getExtendRequest().getSellerId()));
            sb.append(StringUtils.trimToEmpty(request.getExtendRequest().getSubAccountNo()));
            sb.append(StringUtils.trimToEmpty(request.getExtendRequest().getSubAccountName()));
        }
        if (request.getBankRequest() != null) {
            sb.append(StringUtils.trimToEmpty(request.getBankRequest().getBeneficiaryBankCode()));
            sb.append(StringUtils.trimToEmpty(request.getBankRequest().getBeneficiaryBankCodeType()));
            sb.append(StringUtils.trimToEmpty(request.getBankRequest().getPublicOrPrivate()));
            sb.append(StringUtils.trimToEmpty(request.getBankRequest().getChargesIndicator()));
            sb.append(StringUtils.trimToEmpty(request.getBankRequest().getBeneficiaryContactName()));
            sb.append(StringUtils.trimToEmpty(request.getBankRequest().getBeneficiaryContactPhone()));
            sb.append(StringUtils.trimToEmpty(request.getBankRequest().getBeneficiaryEmailAddress()));
        }
 
        return sb.toString();
    }

   generateAbstract方法定义：
    /**
     * 生成摘要
     *
     * @param data
     * @param pwd
     * @return
     */
    public static String generateAbstract(String data, String pwd) {
        MessageDigest md5 = null;
        try {
            md5 = MessageDigest.getInstance("MD5");
            md5.update(pwd.getBytes());
        } catch (NoSuchAlgorithmException e) {
            log.error("7942333,获取md5消息加密对象异常");
            throw new BusinessException("获取md5消息加密对象异常");
        }
        String hex = Hex.encodeHexString(md5.digest(data.getBytes()));
        log.info("MD5摘要:" + hex);
        return hex;
    }
 
 


##### 3. 消息回告接口：
      topic：fls-cashiercenter-master-payorder-status
      tag：你们请求传入的sourceType
/**
 * mq付款消息体
 *
 * 付款失败消息示例：
 * {"errorCode":9999,"subErrorCode":"PARAM_ERROR","errorMsg":"测试错误","occurrence":1571386697796,"sourceNo":"OAFYBX20191018794340030UX","sourceType":"OA","sourceBizType":"OA","status":"PAY_FAIL","xhsNo":"191018000000031"}
 *
 * 付款成功消息示例：
 * {"errorCode":0,"errorMsg":"","occurrence":1571386697796,"sourceNo":"OAFYBX20191018794340032UE","sourceType":"OA","sourceBizType":"OA","status":"PAY_SUCCESS","xhsNo":"191018000000031"}
 *
 * 付款退票消息示例：
 * {"errorCode":0,"errorMsg":"","occurrence":1571386697796,"sourceNo":"OAFYBX20191018794340032UE","sourceType":"OA","sourceBizType":"OA","status":"PAY_RETURN","xhsNo":"191018000000034"}
 
 
 *
 * 付款取消消息示例：
 * {"errorCode":0,"errorMsg":"","occurrence":1571386697796,"sourceNo":"OAFYBX20191018794340032UE","sourceType":"OA","sourceBizType":"OA","status":"PAY_CANCEL","xhsNo":"191018000000034"}
 
 
 *
 * 付款审批拒绝消息示例：
 * {"errorCode":0,"errorMsg":"","occurrence":1571386697796,"sourceNo":"OAFYBX20191018794340032UE","sourceType":"OA","sourceBizType":"OA","status":"PAY_REJECT","xhsNo":"191018000000034"}
 
 
 *
 * 付款中消息示例：
 * {"errorCode":0,"errorMsg":"","occurrence":1571386697796,"sourceNo":"OAFYBX20191018794340032UE","sourceType":"OA","sourceBizType":"OA","status":"PAYING","xhsNo":"191018000000034"}
 
 
 */
@Data
public class PaymentNotifyDto extends RocketMqPaymentResultDto{
    //来源类型
    private String sourceType;
 
    //来源业务类型
    private String sourceBizType;
 
    //来源编号
    private String sourceNo;
 
    //小红书编号
    private String xhsNo;
 
    //状态，枚举见PaymentNotifyStatusEnum
    private String status;
 
    //业务状态实际发生时间
    private Long occurrence;
 
    //错误编码
    private int errorCode;
 
    //子错误编码（明细错误码）
    private String subErrorCode;
 
    //错误信息
    private String errorMsg;
 
}



####    2 兜底查询付款结果接口：
    com.xiaohongshu.fls.rpc.finance[](com.xiaohongshu.fls.rpc.finance).cashiercenter.PaymentService.Iface#queryPaymentOrderBySourceNo
  入参：入参是sourceType（调用方系统自定义的系统来源）和sourceNo（调用方自己生成的单号）
  PaymentQueryBySourceNoRequest request = new PaymentQueryBySourceNoRequest();
            request.setSourceType(SOURCE_TYPE);
            request.setSourceNo(sourceNo);
            request.setQuerDetail(queryDetail);
          
 PaymentQueryBySourceNoResponse   paymentQueryBySourceNoResponse=paymentService.queryPaymentOrderBySourceNo(ContextHelper.getContext(), request);
 
 
paymentQueryBySourceNoResponse.getStatus()
    判断status来确定是否付款成功。
    WAIT_APPROVE("WAIT_APPROVE", "待审核"),
    APPROVE("APPROVE", "审核中"),
    APPROVE_REJECT("APPROVE_REJECT", "审批拒绝"),
    WAIT_PAY("WAIT_PAY", "等待付款"),
    READY("READY", "准备支付"),
    CANCEL("CANCEL", "终止"),
    PAYING("PAYING", "付款中"),
    PAY_SUCCESS("PAY_SUCCESS", "付款成功"),
    PAY_FAIL("PAY_FAIL", "付款失败"),
    PAY_RETURN("PAY_RETURN", "付款退票"),
    POLYMORPHIC_FINAL_STATE("POLYMORPHIC_FINAL_STATE", "多态终态"),
    MANUAL_PROCESSING("MANUAL_PROCESSING", "人工处理"),
 
 
 
   能够取消付款的状态机：
   WAIT_APPROVE, APPROVE, WAIT_PAY, READY,MANUAL_PROCESSING
 
   状态机终态：
   PAY_REJECT("PAY_REJECT","审批拒绝"),
   PAY_CANCEL("PAY_CANCEL","取消付款"),
   PAY_SUCCESS("PAY_SUCCESS", "付款成功"),
   PAY_FAIL("PAY_FAIL", "付款失败"),
   POLYMORPHIC_FINAL_STATE("POLYMORPHIC_FINAL_STATE", "多态终态"),
   PAY_RETURN("PAY_RETURN", "付款退票");
 
 

    3. 取消付款接口：
  能够取消付款的状态机：
  WAIT_APPROVE("WAIT_APPROVE", "待审核"),
 APPROVE("APPROVE", "审核中"),
 WAIT_PAY("WAIT_PAY", "等待付款"),
  READY("READY", "准备支付"),
 MANUAL_PROCESSING("MANUAL_PROCESSING", "人工处理"),
    com.xiaohongshu.fls.rpc.finance[](com.xiaohongshu.fls.rpc.finance).cashiercenter.PaymentService.Iface#cancelPaymentOrderBySourceNo
     入参：入参是sourceType（调用方系统自定义的系统来源）和sourceNo（调用方自己生成的单号）
     请求示例：
 PaymentCannelBySourceNoRequest paymentCannelBySourceNoRequest = new PaymentCannelBySourceNoRequest();
            paymentCannelBySourceNoRequest.setSourceType(SOURCE_TYPE);
            paymentCannelBySourceNoRequest.setSourceNo(requestNo);
            paymentCannelBySourceNoResponse = paymentService.cancelPaymentOrderBySourceNo(ContextHelper.getContext(), paymentCannelBySourceNoRequest);
 


### 7.2 非核心接口请求示例
   maven依赖：
<dependency>
    <groupId>com.xiaohongshu.fls.thrift</groupId>
    <artifactId>lib-thrift-rftreasury</artifactId>
    <version>1.0.5</version>
</dependency>


#### 7.2.1 联行号查询接口：
com.xiaohongshu.finance.rpc.rftreasury.CnapsCodeQueryServiceRpc.Iface#queryCnapsCodeInfoByCodeOrBankName
请求参数：
// 查询联行号参数，两个参数至少传一个
struct CnapsCodeQueryRequest {
    // 分支联行号，精准匹配
    1: string cnapsCode,
    // 银行分支名（模糊匹配）,如果使用银行名称查询则最多只返回20条数据
    2: string bankName,
    // 来源，填你的应用名
    3: string source
}

响应结果：

// 联行号查询结果
struct CnapsCodeQueryResult {
    // 是否成功
    1: bool success;
    // 状态码
    2: string code;
    // 失败消息
    3: string errorMsg;
    // 联行号信息
    4: list<CnapsCodeInfo> cnapsInfos;
}
 
struct CnapsCodeInfo{
    1: string cnapsCode, // 联行号
    2: string bankName, // 银行名称
    3: string superBankCode, // 超级网银号
    4: string areaCode, // 地区编码
}



## 8、FAQ
| 序号 | 问题 | 解答 |
| ---- | ---- | ---- |
| 1 |  |  |
| 2 |  |  |


## 9、更新预告
暂无