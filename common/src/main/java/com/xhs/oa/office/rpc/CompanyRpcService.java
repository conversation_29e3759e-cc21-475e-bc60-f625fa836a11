package com.xhs.oa.office.rpc;

import com.xhs.oa.office.exception.BusinessException;
import com.xiaohongshu.fls.rpc.oacommon.company.CompanyService;
import com.xiaohongshu.fls.rpc.oacommon.company.request.QueryExpenseInfosRequest;
import com.xiaohongshu.fls.rpc.oacommon.company.response.ExpenseInfo;
import com.xiaohongshu.fls.rpc.oacommon.company.response.QueryExpenseInfosResponse;
import com.xiaohongshu.infra.rpc.base.Context;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Arrays;

@Slf4j
@Service
public class CompanyRpcService {

    @Getter
    @Resource
    private CompanyService.Iface companyService;


    // 获取报销主体信息
    public ExpenseInfo queryExpenseInfos(String userId) {
        try {
            QueryExpenseInfosRequest request = new QueryExpenseInfosRequest();
            request.setUserIds(Arrays.asList(userId));
            QueryExpenseInfosResponse response = companyService.queryExpenseInfos(new Context(), request);
            if (!response.success) {
                log.error("queryExpenseInfos error,userId:{},response:{}", userId, response);
                throw new BusinessException("查询用户合同主体异常，请联系OA薯");
            }

            if (CollectionUtils.isEmpty(response.getExpenseInfos())) {
                log.error("queryExpenseInfos error,userId:{},response:{}", userId, response);
                throw new BusinessException("查询用户合同主体异常，请联系OA薯");
            }
            return response.getExpenseInfos().get(0);
        } catch (Exception e) {
            log.error("queryExpenseInfos error,userId:{}", userId, e);
            throw new BusinessException("查询用户合同主体异常，请联系OA薯");
        }
    }

}
