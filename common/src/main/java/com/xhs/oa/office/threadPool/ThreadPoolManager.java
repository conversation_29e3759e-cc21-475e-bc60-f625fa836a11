package com.xhs.oa.office.threadPool;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.concurrent.*;

@Service
@Slf4j
public class ThreadPoolManager
{

    /**
     * 同步处理通用线程池
     */
    public static final ExecutorService commonExecutorService = new ThreadPoolExecutor(40, 60, 10L, TimeUnit.SECONDS,
            new LinkedBlockingQueue<Runnable>(2000));

    /**
     * 日志线程池
     */
    public static final ExecutorService logExecutorService = new ThreadPoolExecutor(20, 30, 10L, TimeUnit.SECONDS,
            new LinkedBlockingQueue<Runnable>(2000));

    /**
     * 异步处理通用线程池(此线程池专门处理耗时操作的任务）
     */
    public static final ExecutorService asyncExecutorService = new ThreadPoolExecutor(10, 20, 10L, TimeUnit.SECONDS,
            new LinkedBlockingQueue(2000));

    public static final ScheduledExecutorService scheduledThreadPool = Executors.newScheduledThreadPool(3);

    public static void executeThreadTask(Runnable runnable) {
        try {
            ThreadPoolExecutor threadPoolExecutor = (ThreadPoolExecutor) commonExecutorService;
            log.info("通用线程池，当前排队线程数：{}", threadPoolExecutor.getQueue().size());
            log.info("通用线程池，当前活动线程数：{}", threadPoolExecutor.getActiveCount());
            log.info("通用线程池，当前执行完成线程数：{}", threadPoolExecutor.getCompletedTaskCount());
            log.info("通用线程池，当前总线程数：{}", threadPoolExecutor.getTaskCount());

        } catch (Exception e) {
            log.error("通用线程池运行状态打印异常", e);
        }

        try {
            commonExecutorService.execute(runnable);
        } catch (Exception e) {
            log.error("commonExecutorService线程池执行异常" + e.getMessage(), e);
        }
    }

    public static <T> Future<T> submitThreadTask(Callable<T> callable) {
        try {
            ThreadPoolExecutor threadPoolExecutor = (ThreadPoolExecutor) asyncExecutorService;
            log.info("异步通用线程池，当前排队线程数：{}", threadPoolExecutor.getQueue().size());
            log.info("异步通用线程池，当前活动线程数：{}", threadPoolExecutor.getActiveCount());
            log.info("异步通用线程池，当前执行完成线程数：{}", threadPoolExecutor.getCompletedTaskCount());
            log.info("异步通用线程池，当前总线程数：{}", threadPoolExecutor.getTaskCount());

        } catch (Exception e) {
            log.error("异步通用线程池运行状态打印异常", e);
        }
        try {
             return asyncExecutorService.submit(callable);
        } catch (Exception e) {
            log.error("asyncExecutorService线程池执行异常" + e.getMessage(), e);
        }
        return null;
    }

    public static void executeLogThreadTask(Runnable runnable) {
        try {
            ThreadPoolExecutor threadPoolExecutor = (ThreadPoolExecutor) logExecutorService;
            log.info("日志线程池，当前排队线程数：{}", threadPoolExecutor.getQueue().size());
            log.info("日志线程池，当前活动线程数：{}", threadPoolExecutor.getActiveCount());
            log.info("日志线程池，当前执行完成线程数：{}", threadPoolExecutor.getCompletedTaskCount());
            log.info("日志线程池，当前总线程数：{}", threadPoolExecutor.getTaskCount());

        }  catch (Exception e)
        {
            log.error("日志线程池运行状态打印异常", e);
        }

        try
        {
            logExecutorService.execute(runnable);
        } catch (Exception e)
        {
            log.error("日志线程池执行异常" + e.getMessage(), e);
        }
    }

    public static void scheduledThreadPoolTask(Runnable runnable, Integer seconds)
    {
        try
        {
            ThreadPoolExecutor threadPoolExecutor = (ThreadPoolExecutor) scheduledThreadPool;
            log.info("日志线程池，延迟队列，当前排队线程数：{}", threadPoolExecutor.getQueue().size());
            log.info("日志线程池，延迟队列，当前活动线程数：{}", threadPoolExecutor.getActiveCount());
            log.info("日志线程池，延迟队列，当前执行完成线程数：{}", threadPoolExecutor.getCompletedTaskCount());
            log.info("日志线程池，延迟队列，当前总线程数：{}", threadPoolExecutor.getTaskCount());
        }  catch (Exception e)
        {
            log.error("日志线程池运行状态打印异常", e);
        }

        try
        {
            scheduledThreadPool.schedule(runnable, seconds, TimeUnit.SECONDS);
        } catch (Exception e)
        {
            log.error("日志线程池执行异常" + e.getMessage(), e);
        }
    }
}
