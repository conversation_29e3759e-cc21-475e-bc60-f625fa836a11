package com.xhs.oa.office.utils;

import com.xhs.oa.office.exception.BusinessException;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public class AssertHelper{


    public static void notNull(Object data, String message) {
        if (Objects.isNull(data)) {
            throw new BusinessException(message);
        }
    }

    public static void notBlank(String data, String message) {
        if (StringUtils.isBlank(data)) {
            throw new BusinessException(message);
        }
    }

    public static void check(boolean expression, String message) {
        if (!expression) {
            throw new BusinessException(message);
        }
    }

    public static void equals(Object data,Object otherData, String message) {
        if (!Objects.equals(data,otherData)) {
            throw new BusinessException(message);
        }
    }

    public static void isNull(Object data, String message) {
        if (!Objects.isNull(data)) {
            throw new BusinessException(message);
        }
    }

    public static void isBlank(String data, String message) {
        if (StringUtils.isNotBlank(data)) {
            throw new BusinessException(message);
        }
    }

    public static void notEmpty(Collection<?> c, String message) {
        if (c == null || c.isEmpty()) {
            throw new BusinessException(message);
        }
    }

}
