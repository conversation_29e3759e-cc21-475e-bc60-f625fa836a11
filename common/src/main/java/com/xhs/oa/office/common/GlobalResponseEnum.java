package com.xhs.oa.office.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum GlobalResponseEnum {
    /**
     * 成功
     */
    SUCCESS(200, "SUCCESS"),

    /**
     * 校验异常
     */
    VALID_ERROR(6000, "校验异常"),

    /**
     * 系统异常
     */
    SERVICE_ERROR(9998, "系统异常"),

    /**
     * 服务器异常
     */
    SERVER_ERROR(9999, "服务器异常"),

    ;

    /**
     * 返回码
     */
    private final int code;
    /**
     * 返回消息
     */
    private final String message;
}