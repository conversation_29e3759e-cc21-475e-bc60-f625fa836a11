package com.xhs.oa.office.rpc;

import com.google.common.collect.Lists;
import com.xhs.ehr.rpc.request.QueryEmployeeRecordRequest;
import com.xhs.ehr.rpc.response.EmployeeRecordInfo;
import com.xhs.ehr.rpc.response.QueryEmployeeRecordResponse;
import com.xhs.ehr.rpc.service.EhrEmployeeRecordService;
import com.xiaohongshu.infra.rpc.base.Context;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class EmployeeRecordService {

    @Autowired
    private EhrEmployeeRecordService.Iface iface;

    public List<EmployeeRecordInfo> queryCurrentEmployeeRecordByIds(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return new ArrayList<>();
        }
        List<EmployeeRecordInfo> rstList = new ArrayList<>();
        Lists.partition(ids, 100).forEach(employeeIds -> {
            QueryEmployeeRecordRequest request = new QueryEmployeeRecordRequest();
            request.setEmployeeIdList(employeeIds);
            request.setEnterpriseId(10000L);
            QueryEmployeeRecordResponse response = null;
            try {
                response = iface.query_current_employee_record_by_ids(new Context(), request);
                if (response == null || !response.success) {
                    log.error("query_current_employee_record_by_ids查询用户记录失败, 请求:{}", employeeIds);
                    return;
                }
                rstList.addAll(response.getEmployeeRecordList());
            } catch (TException e) {
                log.error("query_current_employee_record_by_ids查询用户记录失败, 请求:{}，异常：{}", employeeIds, e.getMessage());
            }
        });
        return rstList;
    }

    public List<EmployeeRecordInfo> queryCurrentEmployeeRecordByStrIds(List<String> ids) {
        List<Long> userIdList = ids.stream().filter(EmployeeCommonService::str2Long).map(Long::parseLong).collect(Collectors.toList());
        return queryCurrentEmployeeRecordByIds(userIdList);
    }
}
