package com.xhs.oa.office.rpc;

import com.xhs.ehr.rpc.request.QueryEmployeeRequest;
import com.xhs.ehr.rpc.response.BatchQueryEmployeeResponse;
import com.xhs.ehr.rpc.response.EmployeeInfo;
import com.xhs.ehr.rpc.service.EhrEmployeeService;
import com.xhs.oa.office.exception.BusinessException;
import com.xiaohongshu.infra.rpc.base.Context;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class EmployeeCommonService {

    @Autowired
    private EhrEmployeeService.Iface ehrEmployeeRpcClient;

    public static boolean str2Long(String userId) {
        try {
            Long.valueOf(userId);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    public List<EmployeeInfo> batchQueryEmployee(List<String> userIdList, Boolean withInvalid) {
        QueryEmployeeRequest request = new QueryEmployeeRequest();
        try {
            List<Long> userIds = userIdList.stream().filter(EmployeeCommonService::str2Long).map(Long::valueOf).collect(Collectors.toList());
            request.setEnterpriseId(10000L);
            request.setWithInvalid(withInvalid);
            request.setEmployeeIdList(userIds);
            BatchQueryEmployeeResponse response = ehrEmployeeRpcClient.query_employee_by_ids(new Context(), request);
            if (response.success) {
                return response.getEmployeeInfoList();
            } else {
                throw new BusinessException(response.error_message);
            }
        } catch (TException e) {
            log.error("EhrDepartmentRpcService query_employee_by_ids userIdList:{},error:{}", userIdList, e.getMessage(), e);
        }
        return Collections.emptyList();
    }
}
