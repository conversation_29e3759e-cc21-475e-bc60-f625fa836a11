package com.xhs.oa.office.utils;

import org.apache.commons.lang3.StringUtils;

public enum Env {
    LOCAL, DEV, TEST, BETA, PROD, SIT;

    public static Env of(String profile) {
        if ("dev".equalsIgnoreCase(profile) || "development".equalsIgnoreCase(profile)) {
            return DEV;
        }
        if ("test".equalsIgnoreCase(profile) || "tst".equalsIgnoreCase(profile)) {
            return TEST;
        }

        if ("beta".equals(profile)) {
            return BETA;
        }

        if ("prod".equalsIgnoreCase(profile) || "product".equalsIgnoreCase(profile)
                || "production".equalsIgnoreCase(profile)) {
            return PROD;
        }

        if ("sit".equalsIgnoreCase(profile)) {
            return SIT;
        }
        return LOCAL;
    }

    public static Env getCurrentEnv(){
        String env = StringUtils.isEmpty(System.getProperty("user.env")) ? "local" : System.getProperty("user.env");
        return of(env);
    }
}
