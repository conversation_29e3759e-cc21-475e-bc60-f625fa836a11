package com.xhs.oa.office.common;

import com.dianping.cat.Cat;
import com.google.common.base.Throwables;
import com.xhs.oa.office.exception.BusinessException;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

@Data
public class GlobalResponse<T>
{

    @ApiModelProperty(notes = "业务成功时:true,失败时：false")
    protected boolean success = true;

    @ApiModelProperty(notes = "可以用来指定特定的错误原因")
    private Integer errorCode;

    @ApiModelProperty(notes = "数据")
    private T data;

    @ApiModelProperty(notes = "不为空。等于200时表示业务成功，其他表示业务失败")
    private int statusCode = 200;

    @ApiModelProperty(notes = "用来弹窗展示的错误信息，如果不为空，展示给用户")
    private String alertMsg;

    @ApiModelProperty(notes = "系统错误信息，供开发人员问题跟踪使用")
    private String errorMsg;

    @ApiModelProperty(notes = "日志跟踪号")
    protected String traceLogId;

    @ApiModelProperty(notes = "额外信息")
    private Map<String, String> extMap;

    public GlobalResponse()
    {
    }

    public GlobalResponse(T data) {
        this.data = data;
        this.alertMsg = "操作成功";
    }

    public GlobalResponse(T data, String traceLogId)
    {
        this.traceLogId = traceLogId;
        this.data = data;
        this.alertMsg = "操作成功";
    }

    public static <T> GlobalResponse<T> success(T data)
    {
        return new GlobalResponse(data, Cat.getCurrentMessageId());
    }

    public static <T> GlobalResponse<T> fail(String alertMsg) {
        GlobalResponse<T> resp = new GlobalResponse();
        resp.setStatusCode(GlobalResponseEnum.SERVER_ERROR.getCode());
        resp.setTraceLogId(Cat.getCurrentMessageId());
        resp.setAlertMsg(StringUtils.isEmpty(alertMsg) ? GlobalResponseEnum.SERVER_ERROR.getMessage() : alertMsg);
        resp.setErrorCode(GlobalResponseEnum.SERVER_ERROR.getCode());
        resp.setSuccess(false);
        resp.setErrorMsg(GlobalResponseEnum.SERVER_ERROR.getMessage());
        return resp;
    }

    public static <T> GlobalResponse<T> exception(Throwable e, String alertMsg) {
        GlobalResponse<T> resp = new GlobalResponse();
        resp.setStatusCode(GlobalResponseEnum.SERVER_ERROR.getCode());
        resp.setTraceLogId(Cat.getCurrentMessageId());
        resp.setAlertMsg(StringUtils.isEmpty(alertMsg) ? GlobalResponseEnum.SERVER_ERROR.getMessage() : alertMsg);
        resp.setErrorCode(GlobalResponseEnum.SERVER_ERROR.getCode());
        resp.setSuccess(false);
        resp.setErrorMsg(Throwables.getStackTraceAsString(Throwables.getRootCause(e)));
        return resp;
    }


    public static <T> GlobalResponse<T> businessFail(BusinessException e)
    {
        GlobalResponse<T> resp = new GlobalResponse();
        resp.setStatusCode(GlobalResponseEnum.SERVER_ERROR.getCode());
        resp.setErrorCode(e.getErrorCode());
        resp.setTraceLogId(Cat.getCurrentMessageId());
        resp.setAlertMsg(StringUtils.isEmpty(e.getAlertMsg()) ? GlobalResponseEnum.SERVER_ERROR.getMessage() : e.getAlertMsg());
        resp.setErrorMsg(e.getMessage());
        resp.setSuccess(false);
        return resp;
    }

    public static <T> GlobalResponse<T> businessFail(BusinessException e, String alertMsg)
    {
        GlobalResponse<T> resp = new GlobalResponse();
        resp.setStatusCode(GlobalResponseEnum.SERVER_ERROR.getCode());
        resp.setErrorCode(e.getErrorCode());
        resp.setTraceLogId(Cat.getCurrentMessageId());
        resp.setAlertMsg(StringUtils.isEmpty(alertMsg) ? GlobalResponseEnum.SERVER_ERROR.getMessage() : alertMsg);
        resp.setErrorMsg(e.getMessage());
        resp.setSuccess(false);
        return resp;
    }

    public static <T> GlobalResponse<T> httpError(Integer httpCode)
    {
        Integer code = null == httpCode ? GlobalResponseEnum.SERVER_ERROR.getCode() : httpCode;
        GlobalResponse<T> resp = new GlobalResponse();
        resp.setStatusCode(code);
        resp.setTraceLogId(Cat.getCurrentMessageId());
        resp.setAlertMsg(GlobalResponseEnum.SERVER_ERROR.getMessage());
        resp.setSuccess(false);
        resp.setErrorMsg("http请求异常，Http statusCode = " + httpCode);
        return resp;
    }

}
