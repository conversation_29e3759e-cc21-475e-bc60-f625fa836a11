plugins {
    id 'java'
}

group = 'com.xhs.oa.office'
version = '0.0.1-SNAPSHOT'

repositories {
    mavenCentral()
}

dependencies {
    configurations.all {
        resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
    }

    implementation project(':common')

    //spring
    compileOnly("org.mapstruct:mapstruct:1.5.5.Final")
    annotationProcessor("org.mapstruct:mapstruct-processor:1.5.5.Final")
    implementation("jakarta.persistence:jakarta.persistence-api:2.2.3")
    implementation(enforcedPlatform("com.xiaohongshu:infra-root-pom:${root_pom_version}"))
    implementation('com.xiaohongshu:thrift-springboot') { exclude group: 'com.alibaba', module: 'fastjson' }
    implementation('io.swagger:swagger-models:1.5.21')

    // db
    implementation('mysql:mysql-connector-java:8.0.16')
    implementation('org.mybatis:mybatis:3.5.5')
    implementation('org.mybatis:mybatis-spring:2.0.5')
    implementation('com.baomidou:mybatis-plus-boot-starter:3.4.0')
    implementation('com.baomidou:mybatis-plus-extension:3.4.0')
    implementation('com.github.pagehelper:pagehelper-spring-boot-starter:1.2.3')
    // MQ消息
    implementation 'com.xiaohongshu:events-client:1.0.7.5'
    //fastjson2
    implementation("com.alibaba.fastjson2:fastjson2:2.0.47")
    //新ehr
    implementation('com.xiaohongshu.fls.thrift:lib-thrift-ehrservice:1.5.9')
    //redFlow
    implementation('com.xiaohongshu.fls.thrift:lib-thrift-oa-public:master-SNAPSHOT')
    //oacommon
    implementation('com.xiaohongshu.fls.thrift:lib-thrift-oa-common:master-SNAPSHOT')
    //财务-出纳中心
    implementation('com.xiaohongshu.fls.thrift:lib-thrift-cashiercenter:0.4.7')
    //财务-喜马拉雅
    implementation('com.xiaohongshu.fls.thrift:lib-thrift-himalaya:1.0.10')
    //apiHub
    implementation('com.xiaohongshu.fls.thrift:lib-thrift-apihubservice:master-SNAPSHOT')

    // 上线修改
    implementation('com.xiaohongshu.fls.thrift:lib-thrift-oa-office-idl:master-SNAPSHOT') { changing = true }

    implementation('com.xhs.enterprise:erp-common:1.1.4-SNAPSHOT') {
        changing = true
        exclude group: 'com.alibaba', module: 'fastjson'
        exclude group: 'com.ctrip.framework.apollo', module: 'apollo-openapi'
        exclude group: 'com.ctrip.framework.apollo', module: 'apollo-core'
        exclude group: 'com.xuxueli', module: 'xxl-job-core'
        exclude group: 'javax.servlet', module: 'javax.servlet-api'
    }

    // ocr
    implementation('com.xiaohongshu.fls.thrift:lib-thrift-oa-contract:master-SNAPSHOT')
    // cdn
    implementation ('com.xiaohongshu.multicdn.thrift:multicdn-service-rpc-sdk:0.2.5')
    implementation ('com.xiaohongshu.media:media-services-sdk:1.0.3')
    // 上传文件组件sdk
    implementation('com.xiaohongshu:ros-java-sdk:0.0.18')
    implementation('com.thoughtworks.xstream:xstream:1.4.17'){ force = true }

    // kotlin
    implementation('org.jetbrains.kotlin:kotlin-stdlib:1.3.70')
    implementation('org.jetbrains.kotlin:kotlin-stdlib-common:1.3.70')

}

test {
    useJUnitPlatform()
}