//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.xhs.reimburse.rpc.consumer.ocr.bean;


import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class ImageInvoicesRecogcollectBizInvoiceCompStatus extends BasicEntity {
    private Long id;
    private Long invoiceId;
    private String complianceCode;
    private String complianceType;
    private String complianceErrCode;
    private String complianceMessage;
    private String userAccount;
    private String createUid;
    private String createUname;
    private String orgId;
    private String orgName;
    private String updateUid;
    private String createTime;
    private String updateTime;
}
