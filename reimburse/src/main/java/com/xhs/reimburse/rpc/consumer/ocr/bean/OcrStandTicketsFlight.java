package com.xhs.reimburse.rpc.consumer.ocr.bean;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class OcrStandTicketsFlight extends BasicEntity {
    private String fromStation;
    private String toStation;
    private String flightNo;
    private String travelDate;
    private String travelTime;
    private String seatLevel;
    private String carrier;
    private String className;
}
