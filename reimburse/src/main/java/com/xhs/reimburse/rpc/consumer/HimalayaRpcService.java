package com.xhs.reimburse.rpc.consumer;

import com.xhs.finance.exception.BusinessException;
import com.xiaohongshu.fls.rpc.himalaya.HimalayaService;
import com.xiaohongshu.fls.rpc.himalaya.response.GetExchangeRateResponse;
import com.xiaohongshu.infra.rpc.base.Context;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName HimalayaRpcService.java
 * @createTime 2025年03月21日 17:08:00
 */
@Slf4j
@Service
public class HimalayaRpcService {

    @Resource
    private HimalayaService.Iface himalayaService;

    public BigDecimal getExchangeRate(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        try {
            GetExchangeRateResponse response = himalayaService.getExchangeRate(new Context(), name);
            if (response == null || !response.isResult()) {
                throw new BusinessException("获取汇率失败：" + name + (response != null ? response.getMessage() : ""));
            }
            BigDecimal exchangeRate = BigDecimal.valueOf(response.getRate());
            return exchangeRate;
        } catch (Exception e) {
            log.error("getExchangeRate error:" + name, e);
            return null;
        }
    }
}
