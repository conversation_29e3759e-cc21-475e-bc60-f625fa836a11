//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.xhs.reimburse.rpc.consumer.ocr.bean;


import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class ImageInvoicesRecogcollectBizInvoiceNontaxExpend extends BasicEntity {
    private Long id;
    private Long invoiceId;
    private String code;
    private String value;
    private String createTime;
    private String updateTime;
}
