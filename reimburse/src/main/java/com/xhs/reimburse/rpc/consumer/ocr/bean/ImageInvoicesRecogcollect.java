package com.xhs.reimburse.rpc.consumer.ocr.bean;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class ImageInvoicesRecogcollect extends BasicEntity {
    private Long folderId;
    private ImageInvoicesRecogcollectBizInvoiceFolder bizInvoiceFolder;
    private ImageInvoicesRecogcollectMediaFile mediaFile;
    private List<ImageInvoicesRecogcollectMediaInvoice> mediaInvoiceList;
}