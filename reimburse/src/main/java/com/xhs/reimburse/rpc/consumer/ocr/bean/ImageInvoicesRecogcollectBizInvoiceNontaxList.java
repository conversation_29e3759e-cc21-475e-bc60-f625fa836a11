//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.xhs.reimburse.rpc.consumer.ocr.bean;


import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class ImageInvoicesRecogcollectBizInvoiceNontaxList extends BasicEntity {
    private Long id;
    private Long invoiceId;
    private String itemRelatedCode;
    private String itemRelatedName;
    private String code;
    private String name;
    private String std;
    private String num;
    private String unit;
    private String amt;
    private String remark;
    private String createTime;
    private String updateTime;
}
