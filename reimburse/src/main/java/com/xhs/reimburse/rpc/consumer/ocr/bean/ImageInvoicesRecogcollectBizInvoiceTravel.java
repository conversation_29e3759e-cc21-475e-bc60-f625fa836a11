//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.xhs.reimburse.rpc.consumer.ocr.bean;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class ImageInvoicesRecogcollectBizInvoiceTravel extends BasicEntity {
    private Long id;
    private Long invoiceId;
    private String fromStation;
    private String toStation;
    private String flightNo;
    private String travelDate;
    private String travelTime;
    private String seatLevel;
    private String carrier;
    private String createTime;
    private String updateTime;
}
