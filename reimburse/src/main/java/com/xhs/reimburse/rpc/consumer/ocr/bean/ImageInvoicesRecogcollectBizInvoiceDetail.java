//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.xhs.reimburse.rpc.consumer.ocr.bean;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Data
public class ImageInvoicesRecogcollectBizInvoiceDetail extends BasicEntity {
    private Long id;
    private Long invoiceId;
    private String rowNo;
    private String commodityCode;
    private String commodityName;
    private String specificationModel;
    private String unit;
    private BigDecimal quantity;
    private BigDecimal unitPrice;
    private BigDecimal amount;
    private BigDecimal taxRate;
    private BigDecimal tax;
    private String currentDateStart;
    private String currentDateEnd;
    private String licensePlateNum;
    private Integer detailType;
    private String goodsType;
    private Integer specialMark;
    private String createTime;
    private String updateTime;
}
