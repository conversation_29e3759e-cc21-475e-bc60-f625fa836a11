package com.xhs.reimburse.rpc.consumer.ocr;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName OcrStandTicketsFlight.java
 * @createTime 2025年02月19日 17:40:00
 */
@Data
public class OcrStandTicketsFlight {
    // 飞机票仓位等级
    private String className;

    // 出发站
    private String fromStation;

    // 到达站
    private String toStation;

    // 航班号
    private String flightNo;

    // 乘机日期
    private String travelDate;

    // 乘机时间
    private String travelTime;

    // 座位等级
    private String seatLevel;

    // 承运人
    private String carrier;
}