//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.xhs.reimburse.rpc.consumer.ocr.bean;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class AbstractResponse extends BasicEntity {
    private Boolean success;
    private String method;
    private ErrorResponse errorResponse;
    private String requestId;
}
