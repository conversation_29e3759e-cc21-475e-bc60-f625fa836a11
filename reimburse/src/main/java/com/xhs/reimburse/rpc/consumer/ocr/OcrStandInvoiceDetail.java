package com.xhs.reimburse.rpc.consumer.ocr;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName OcrStandInvoiceDetail.java
 * @createTime 2025年02月19日 17:42:00
 */
@Data
public class OcrStandInvoiceDetail {
    // 商品名称
    private String commodityName;

    // 规格型号
    private String specifictionModel;

    // 单位
    private String unit;

    // 数量
    private String quantity;

    // 单价
    private String unitPrice;

    // 金额
    private String totalAmount;

    // 税率
    private String taxRate;

    // 税额
    private String tax;

    // Getter and Setter methods
}
