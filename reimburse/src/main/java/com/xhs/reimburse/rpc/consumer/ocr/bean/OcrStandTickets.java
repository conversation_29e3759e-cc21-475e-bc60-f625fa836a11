package com.xhs.reimburse.rpc.consumer.ocr.bean;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class OcrStandTickets implements Serializable, Cloneable {
    private String invoiceType;
    private String invoiceCode;
    private String invoiceNo;
    private String invoiceDate;
    private BigDecimal totalAmount;
    private BigDecimal amountTax;
    private BigDecimal totalTax;
    private BigDecimal otherTax;
    private BigDecimal taxRate;
    private BigDecimal civilAviationFund;
    private String checkCode;
    private String purchaserTaxNo;
    private String buyerIdentification;
    private String saleTaxNo;
    private String marketTaxNo;
    private String sellerId;
    private String drawer;
    private String leaveCity;
    private String arriveCity;
    private String trainNumber;
    private String idNum;
    private String trainSeat;
    private String leaveTime;
    private String arriveTime;
    private String mileage;
    private Integer orientation;
    private String hasSeal;
    private String carNo;
    private String carCode;
    private String carEngineCode;
    private String machineInvoiceNo;
    private String machineInvoiceCode;
    private OcrStandTicketsInvoicePosition invoicePosition;
    private String asyncCode;
    private String saleName;
    private String purchaserName;
    private String invoiceTemplateType;
    private String invoiceTemplateName;
    private String invoiceCiphertext;
    private String machineCode;
    private String remark;
    private String taxAuthoritiesCode;
    private String taxAuthoritiesName;
    private String carModel;
    private String certificateNo;
    private String marketName;
    private String registrationNo;
    private String serialNum;
    private BigDecimal premium;
    private String printNumber;
    private List<OcrStandTicketsFlight> flightList;
    private String invoiceTime;
    private String entrance;
    private String roadExit;
    private String isHighway;
    private String province;
    private String city;
    private String applyDate;
    private String phone;
    private String travelStartDate;
    private String travelEndDate;
    private String transitMark;
    private String printedMark;
    private String category;
    private String invoiceCodeConfirm;
    private String invoiceNoConfirm;
    private String issuer;
    private String reviewer;
    private List<OcrStandInvoiceDetail> detailList;
    private List<OcrStandCarTravel> carList;
    private String sellerAddrTel;
    private String sellerBankAccount;
    private String buyerAddrTel;
    private String buyerBankAccount;
}
