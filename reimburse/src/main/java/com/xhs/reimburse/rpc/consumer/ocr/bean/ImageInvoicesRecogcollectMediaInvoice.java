//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.xhs.reimburse.rpc.consumer.ocr.bean;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
public class ImageInvoicesRecogcollectMediaInvoice extends BasicEntity {
    private Map<String, Object> ext;
    private Long id;
    private Long billId;
    private Long fileId;
    private String folderNo;
    private String invoiceType;
    private Long noteType;
    private String noteName;
    private String costNo;
    private String costName;
    private String administrativeDivisionNo;
    private String administrativeDivisionName;
    private String invoiceCode;
    private String invoiceNo;
    private String invoiceDate;
    private String machineInvoiceNo;
    private String machineInvoiceCode;
    private String purchaserName;
    private String purchaserTaxNo;
    private String purchaserBank;
    private String purchaserAddressPhone;
    private String saleName;
    private String saleTaxNo;
    private String saleAddressPhone;
    private String saleBank;
    private String useRemark;
    private BigDecimal totalAmount;
    private BigDecimal totalTax;
    private BigDecimal otherTax;
    private BigDecimal civilAviationFund;
    private BigDecimal amountTax;
    private BigDecimal deductTax;
    private Integer isDeduct;
    private String amountTaxCn;
    private String checkCode;
    private String machineCode;
    private String paymentVoucherNo;
    private String ciphertext;
    private String drawer;
    private String checker;
    private String payee;
    private String remark;
    private String commodityName;
    private Integer num;
    private String leaveCity;
    private String arriveCity;
    private String trainNumber;
    private String carNo;
    private String idNum;
    private String trainSeat;
    private String leaveTime;
    private String arriveTime;
    private String mileage;
    private String entrance;
    private String roadExit;
    private String serialNum;
    private BigDecimal premium;
    private String serviceTime;
    private Integer hasPicture;
    private Integer hasPosition;
    private Integer hasSeal;
    private Integer hasInvAttached;
    private Integer orientation;
    private Integer collectWay;
    private Integer collectServiceType;
    private Integer collectType;
    private Integer collectSys;
    private Integer collectSysType;
    private Integer collectUseType;
    private String collectCompanyName;
    private String inspectionTime;
    private Integer inspectionStatus;
    private String inspectionUserId;
    private String inspectionUserName;
    private String inspectionErrorDesc;
    private String isComplianceCollect;
    private String complianceCollectDesc;
    private Integer verifyStatus;
    private Integer zeroTaxRateSign;
    private Integer invoiceState;
    private Integer agentMark;
    private Integer oilMark;
    private Integer isRed;
    private Integer isPull;
    private Integer entityStatus;
    private Integer reimburseStatus;
    private String createTime;
    private String userId;
    private String userAccount;
    private String userName;
    private String createUid;
    private String createUname;
    private String phone;
    private String orgId;
    private String orgName;
    private String updateTime;
    private String updateUser;
    private String updateUserAccount;
    private String updateUname;
    private String tradeType;
    private String deductStatus;
    private String storageStatus;
    private String deductType;
    private String repeatCollect;
    private String invoiceKey;
    private String relatedStatus;
    private String imagePushTime;
    private String checkResponseCode;
    private String authenticableState;
    private String watermaerk;
    private String position;
    private String invoiceMail;
    private String hasTraveller;
    private String createUser;
    private String invConfirmDate;
    private String invConfirmUser;
    private String invDeduResult;
    private String effectiveTax;
    private String invDeduDate;
    private String incomeMonth;
    private Long oldId;
    private String importCollectNo;
    private Integer verifySignStatus;
    private String verifySignMessage;
    private Integer signStatus;
    private Integer isBuyerOrg;
    private Integer isBuyerOrgCommon;
    private Integer isAllowOrgIdNotEqual;
    private String mailFrom;
    private String taskId;
    private String emailTitle;
    private String isFistPush;
    private String pushId;
    private String pushStatus;
    private String pushResult;
    private String pushTime;
    private String pushResultTime;
    private String pushOpertion;
    private String pushRemark;
    private String collectInvUseType;
    private Integer reimburseMode;
    private String exchangePaper;
    private String relatedInvoiceNumber;
    // 电子火车票 发车时间
    private String time;
    private ImageInvoicesRecogcollectInvoiceAttached invoiceAttached;
    private List<ImageInvoicesRecogcollectBizInvoiceDetail> invoiceDetailList;
    private ImageInvoicesRecogcollectBizInvoicePosition invoicePosition;
    private List<ImageInvoicesRecogcollectBizInvoiceTravel> invoiceTravelList;
    private List<ImageInvoicesRecogcollectBizInvoiceAttFile> bizInvoiceAttFileList;
    private List<ImageInvoicesRecogcollectBizInvoiceCompStatus> mediaInvoiceCompStatuses;
    private List<ImageInvoicesRecogcollectBizInvoiceNontaxDetail> bizInvoiceNontaxDetailList;
    private List<ImageInvoicesRecogcollectBizInvoiceNontaxExpend> bizInvoiceNontaxExpendList;
    private List<ImageInvoicesRecogcollectBizInvoiceNontaxList> bizInvoiceNontaxLists;

    public String getDigitalAirItineraryInvoiceDate() {
        if (!CollectionUtils.isEmpty(this.getInvoiceTravelList()) && !StringUtils.isEmpty(this.getInvoiceTravelList().get(0).getTravelDate())) {
            return this.getInvoiceTravelList().get(0).getTravelDate();
        }

        if (!StringUtils.isEmpty(this.getInvoiceDate())) {
            return this.getInvoiceDate();
        }
        return "";
    }
}

