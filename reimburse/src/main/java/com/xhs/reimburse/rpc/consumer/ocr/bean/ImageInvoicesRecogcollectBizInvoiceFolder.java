package com.xhs.reimburse.rpc.consumer.ocr.bean;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Data
public class ImageInvoicesRecogcollectBizInvoiceFolder extends BasicEntity {
    private Long id;
    private String folderNo;
    private String folderName;
    private Integer invoiceNum;
    private BigDecimal amountTax;
    private Integer isInit;
    private Integer isRelation;
    private String createUid;
    private String createUname;
    private String phone;
    private String orgId;
    private String orgName;
    private String createTime;
    private String updateTime;
    private String updateUid;
}