//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.xhs.reimburse.rpc.consumer.ocr.bean;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Data
public class ImageInvoicesRecogcollectInvoiceAttached extends BasicEntity {
    private Long id;
    private Long invoiceId;
    private String carrierName;
    private String carrierTaxNo;
    private String draweeName;
    private String draweeTaxNo;
    private String cargoInformation;
    private String transportRoute;
    private String tonnage;
    private String taxAuthorityNo;
    private String taxAuthorityName;
    private String idCardNo;
    private String vehicleType;
    private String vehicleNo;
    private String brandModel;
    private String originPlace;
    private String certiticateNo;
    private String inspectionListNo;
    private String engineNo;
    private String importCertificateNo;
    private String sellerName;
    private String phoneNo;
    private String account;
    private String address;
    private String bank;
    private BigDecimal goodsTaxRate;
    private String passengersLimited;
    private String usedCarBank;
    private String usedCarTaxNo;
    private String usedCarPhone;
    private String usedCarName;
    private String usedCarAddress;
    private String registrationNumber;
    private String purchaserPhone;
    private String auctionBank;
    private String auctionTaxNo;
    private String auctionPhone;
    private String auctionName;
    private String auctionAddress;
    private String tollSign;
    private String carNumber;
    private String vehiclePlaceName;
}
