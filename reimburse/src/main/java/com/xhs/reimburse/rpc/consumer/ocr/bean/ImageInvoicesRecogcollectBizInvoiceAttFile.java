//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.xhs.reimburse.rpc.consumer.ocr.bean;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class ImageInvoicesRecogcollectBizInvoiceAttFile extends BasicEntity {
    private Long id;
    private Long invoiceId;
    private String fileName;
    private String fileNo;
    private String fileAddress;
    private Integer fileOrder;
    private String fileType;
    private String fileFormat;
    private Integer ocrStatus;
    private String fileDesc;
    private String scanTime;
    private Integer pullType;
    private Integer collectUse;
    private Integer collectWay;
    private Integer collectType;
    private Integer collectSys;
    private Integer collectSysType;
    private Integer collectUseType;
    private String collectCompany;
    private String userId;
    private String userName;
    private String phone;
    private String orgId;
    private String orgName;
    private String createTime;
    private String updateTime;
    private String updateUser;
    private Integer isStorage;
    private Integer isBack;

    public ImageInvoicesRecogcollectBizInvoiceAttFile() {
    }

    @JsonProperty("id")
    public Long getId() {
        return this.id;
    }

    @JsonProperty("id")
    public void setId(Long id) {
        this.id = id;
    }

    @JsonProperty("invoiceId")
    public Long getInvoiceId() {
        return this.invoiceId;
    }

    @JsonProperty("invoiceId")
    public void setInvoiceId(Long invoiceId) {
        this.invoiceId = invoiceId;
    }

    @JsonProperty("fileName")
    public String getFileName() {
        return this.fileName;
    }

    @JsonProperty("fileName")
    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    @JsonProperty("fileNo")
    public String getFileNo() {
        return this.fileNo;
    }

    @JsonProperty("fileNo")
    public void setFileNo(String fileNo) {
        this.fileNo = fileNo;
    }

    @JsonProperty("fileAddress")
    public String getFileAddress() {
        return this.fileAddress;
    }

    @JsonProperty("fileAddress")
    public void setFileAddress(String fileAddress) {
        this.fileAddress = fileAddress;
    }

    @JsonProperty("fileOrder")
    public Integer getFileOrder() {
        return this.fileOrder;
    }

    @JsonProperty("fileOrder")
    public void setFileOrder(Integer fileOrder) {
        this.fileOrder = fileOrder;
    }

    @JsonProperty("fileType")
    public String getFileType() {
        return this.fileType;
    }

    @JsonProperty("fileType")
    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    @JsonProperty("fileFormat")
    public String getFileFormat() {
        return this.fileFormat;
    }

    @JsonProperty("fileFormat")
    public void setFileFormat(String fileFormat) {
        this.fileFormat = fileFormat;
    }

    @JsonProperty("ocrStatus")
    public Integer getOcrStatus() {
        return this.ocrStatus;
    }

    @JsonProperty("ocrStatus")
    public void setOcrStatus(Integer ocrStatus) {
        this.ocrStatus = ocrStatus;
    }

    @JsonProperty("fileDesc")
    public String getFileDesc() {
        return this.fileDesc;
    }

    @JsonProperty("fileDesc")
    public void setFileDesc(String fileDesc) {
        this.fileDesc = fileDesc;
    }

    @JsonProperty("scanTime")
    public String getScanTime() {
        return this.scanTime;
    }

    @JsonProperty("scanTime")
    public void setScanTime(String scanTime) {
        this.scanTime = scanTime;
    }

    @JsonProperty("pullType")
    public Integer getPullType() {
        return this.pullType;
    }

    @JsonProperty("pullType")
    public void setPullType(Integer pullType) {
        this.pullType = pullType;
    }

    @JsonProperty("collectUse")
    public Integer getCollectUse() {
        return this.collectUse;
    }

    @JsonProperty("collectUse")
    public void setCollectUse(Integer collectUse) {
        this.collectUse = collectUse;
    }

    @JsonProperty("collectWay")
    public Integer getCollectWay() {
        return this.collectWay;
    }

    @JsonProperty("collectWay")
    public void setCollectWay(Integer collectWay) {
        this.collectWay = collectWay;
    }

    @JsonProperty("collectType")
    public Integer getCollectType() {
        return this.collectType;
    }

    @JsonProperty("collectType")
    public void setCollectType(Integer collectType) {
        this.collectType = collectType;
    }

    @JsonProperty("collectSys")
    public Integer getCollectSys() {
        return this.collectSys;
    }

    @JsonProperty("collectSys")
    public void setCollectSys(Integer collectSys) {
        this.collectSys = collectSys;
    }

    @JsonProperty("collectSysType")
    public Integer getCollectSysType() {
        return this.collectSysType;
    }

    @JsonProperty("collectSysType")
    public void setCollectSysType(Integer collectSysType) {
        this.collectSysType = collectSysType;
    }

    @JsonProperty("collectUseType")
    public Integer getCollectUseType() {
        return this.collectUseType;
    }

    @JsonProperty("collectUseType")
    public void setCollectUseType(Integer collectUseType) {
        this.collectUseType = collectUseType;
    }

    @JsonProperty("collectCompany")
    public String getCollectCompany() {
        return this.collectCompany;
    }

    @JsonProperty("collectCompany")
    public void setCollectCompany(String collectCompany) {
        this.collectCompany = collectCompany;
    }

    @JsonProperty("userId")
    public String getUserId() {
        return this.userId;
    }

    @JsonProperty("userId")
    public void setUserId(String userId) {
        this.userId = userId;
    }

    @JsonProperty("userName")
    public String getUserName() {
        return this.userName;
    }

    @JsonProperty("userName")
    public void setUserName(String userName) {
        this.userName = userName;
    }

    @JsonProperty("phone")
    public String getPhone() {
        return this.phone;
    }

    @JsonProperty("phone")
    public void setPhone(String phone) {
        this.phone = phone;
    }

    @JsonProperty("orgId")
    public String getOrgId() {
        return this.orgId;
    }

    @JsonProperty("orgId")
    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    @JsonProperty("orgName")
    public String getOrgName() {
        return this.orgName;
    }

    @JsonProperty("orgName")
    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    @JsonProperty("createTime")
    public String getCreateTime() {
        return this.createTime;
    }

    @JsonProperty("createTime")
    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    @JsonProperty("updateTime")
    public String getUpdateTime() {
        return this.updateTime;
    }

    @JsonProperty("updateTime")
    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    @JsonProperty("updateUser")
    public String getUpdateUser() {
        return this.updateUser;
    }

    @JsonProperty("updateUser")
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    @JsonProperty("isStorage")
    public Integer getIsStorage() {
        return this.isStorage;
    }

    @JsonProperty("isStorage")
    public void setIsStorage(Integer isStorage) {
        this.isStorage = isStorage;
    }

    @JsonProperty("isBack")
    public Integer getIsBack() {
        return this.isBack;
    }

    @JsonProperty("isBack")
    public void setIsBack(Integer isBack) {
        this.isBack = isBack;
    }
}
