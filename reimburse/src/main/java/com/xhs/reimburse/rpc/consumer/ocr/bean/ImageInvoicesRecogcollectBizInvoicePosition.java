//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.xhs.reimburse.rpc.consumer.ocr.bean;


import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class ImageInvoicesRecogcollectBizInvoicePosition extends BasicEntity {
    private Long id;
    private Long invoiceId;
    private Long fileId;
    private Integer x1;
    private Integer y1;
    private Integer x2;
    private Integer y2;
    private String createUid;
    private String createUname;
    private String createTime;
}
