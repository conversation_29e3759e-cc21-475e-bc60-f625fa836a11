package com.xhs.reimburse.rpc.consumer;

import com.xhs.reimburse.modal.dto.FileInfoDto;
import com.xhs.reimburse.modal.dto.InvoiceDto;
import com.xhs.reimburse.utils.StreamUtil;
import com.xiaohongshu.erp.common.exception.BusinessException;
import com.xiaohongshu.infra.rpc.base.Context;
import com.xiaohongshu.media.services.sdk.service.RedUploadClient;
import com.xiaohongshu.sns.rpc.multicdn.Executor;
import com.xiaohongshu.sns.rpc.multicdn.GetCDNResourceRequest;
import com.xiaohongshu.sns.rpc.multicdn.GetCDNResourceResponse;
import com.xiaohongshu.sns.rpc.multicdn.URL;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName MultiCdnRpcService.java
 * @createTime 2025年02月21日 20:18:00
 */
@Slf4j
@Service
public class MultiCdnRpcService {

    @Resource
    private Executor.Iface cndService;

    @Resource
    private RedUploadClient redUploadClient;

    /**
     * 批量获取文件的CDN资源并设置URL
     */
    public void batchGetAndSetFileUrl(List<FileInfoDto> fileInfoList) {
        List<FileInfoDto> filteredList = fileInfoList
                .stream().filter(fileInfoDto -> {
                    return fileInfoDto != null && !StringUtils.isEmpty(fileInfoDto.getFileId()) && !StringUtils.isEmpty(fileInfoDto.getScene()) && !StringUtils.isEmpty(fileInfoDto.getBusiness());
                }).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(fileInfoList)) {
            return;
        }

        // 按照business和scene分组
        List<List<FileInfoDto>> groupedFiles = groupByBusinessAndScene(filteredList);

        batchSetDecryptedFileId(fileInfoList);

        // 处理每个分组，获取CDN资源并设置URL
        for (List<FileInfoDto> fileInfoGroup : groupedFiles) {
            updateFileUrls(fileInfoGroup);
        }
    }


    private void batchSetDecryptedFileId(List<FileInfoDto> fileInfoList) {
        List<FileInfoDto> decryotedFileIdIsEmptyList = fileInfoList.stream().filter(fileInfoDto -> {
            return StringUtils.isEmpty(fileInfoDto.getDecryptedFileId());
        }).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(decryotedFileIdIsEmptyList)) {
            return;
        }

        List<String> fileIds = StreamUtil.toList(decryotedFileIdIsEmptyList, FileInfoDto::getFileId);
        Map<String, String> stringStringMap = redUploadClient.batchGetFileKeyMap(fileIds);

        fileInfoList.stream().forEach(fileInfo -> {
            if (stringStringMap.containsKey(fileInfo.getFileId())) {
                fileInfo.setDecryptedFileId(stringStringMap.get(fileInfo.getFileId()));
            }
        });
    }

    /**
     * 根据business和scene字段分组文件信息
     */
    private List<List<FileInfoDto>> groupByBusinessAndScene(List<FileInfoDto> fileInfoList) {
        // 使用Collectors.groupingBy按business和scene分组
        Map<String, Map<String, List<FileInfoDto>>> groupedByBusinessAndScene = fileInfoList.stream()
                .collect(Collectors.groupingBy(FileInfoDto::getBusiness,
                        Collectors.groupingBy(FileInfoDto::getScene)));

        // 将分组的Map转化为List<List<FileInfoDto>>格式
        return groupedByBusinessAndScene.values().stream()
                .flatMap(sceneGroup -> sceneGroup.values().stream())
                .collect(Collectors.toList());
    }

    /**
     * 批量查询CDN资源并设置文件的URL
     */
    private void updateFileUrls(List<FileInfoDto> fileInfoGroup) {
        GetCDNResourceResponse response = batchQueryAndSetFileInfoUrl(fileInfoGroup);
        Map<String, URL> decryptedFileIdUrlMap = response.getResource().getUrls();

        // 设置URL到每个文件信息
        fileInfoGroup.forEach(fileInfoDto -> {
            URL url = decryptedFileIdUrlMap.get(fileInfoDto.getDecryptedFileId());
            if (url != null) {
                fileInfoDto.setUrl(url.getMaster());
            }
        });
    }

    /**
     * 批量查询CDN资源
     */
    private GetCDNResourceResponse batchQueryAndSetFileInfoUrl(List<FileInfoDto> fileInfoList) {
        try {
            // 构建CDN资源请求
            GetCDNResourceRequest request = buildCDNResourceRequest(fileInfoList);

            // 调用CDN服务获取资源
            GetCDNResourceResponse response = cndService.GetCDNResource(new Context(), request);

            // 如果获取失败，抛出业务异常
            if (!response.getResult().success) {
                throw new BusinessException(response.getResult().getMessage());
            }

            log.info("CDN资源请求成功 fileInfoList: {}, response: {}", fileInfoList, response);
            return response;
        } catch (Exception e) {
            log.error("文件下载URL失败 fileInfoList: {}", fileInfoList, e);
            throw new BusinessException("文件下载失败");
        }
    }

    /**
     * 构建CDN资源请求对象
     */
    private GetCDNResourceRequest buildCDNResourceRequest(List<FileInfoDto> fileInfoDtoList) {
        // 从列表中取出第一个文件信息用于构建请求
        String businessName = fileInfoDtoList.get(0).getBusiness();
        String scene = fileInfoDtoList.get(0).getScene();

        // 获取所有文件ID
        List<String> decryptedFiledList = fileInfoDtoList.stream()
                .map(FileInfoDto::getDecryptedFileId)
                .collect(Collectors.toList());

        // 创建并返回CDN资源请求对象
        GetCDNResourceRequest request = new GetCDNResourceRequest();
        request.setBusiness(businessName);
        request.setScene(scene);
        request.setFile_keys(decryptedFiledList);
        request.setTags(getCDNResourceRequestTags());
        return request;
    }

    private Map<String, String> getCDNResourceRequestTags() {
        HashMap<String, String> tagsMap = new HashMap<>(1);
        tagsMap.put("auth_offset", "0");
        return tagsMap;
    }
}