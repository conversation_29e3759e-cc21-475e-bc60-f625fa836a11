package com.xhs.reimburse.rpc.consumer.ocr;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName OcrInvoiceDataResponse.java
 * @createTime 2025年02月19日 17:35:00
 */
@Data
public class OcrInvoiceDataResponse {

    // 发票类型
    private String invoiceType;

    // 发票代码
    private String invoiceCode;

    // 发票号码
    private String invoiceNo;

    // 开票日期
    private String invoiceDate;

    // 不含税金额
    private BigDecimal totalAmount;

    // 合计金额
    private BigDecimal amountTax;

    // 合计税额
    private BigDecimal totalTax;

    // 其他税费
    private BigDecimal otherTax;

    // 税率
    private BigDecimal taxRate;

    // 民航发展基金
    private BigDecimal civilAviationFund;

    // 校验码
    private String checkCode;

    // 购方税号
    private String purchaserTaxNo;

    // 买方单位代码/个人身份证号
    private String buyerIdentification;

    // 销方税号
    private String saleTaxNo;

    // 二手车市场税号
    private String marketTaxNo;

    // 卖方单位代码/个人身份证号
    private String sellerId;

    // 开票人
    private String drawer;

    // 出发城市
    private String leaveCity;

    // 到达城市
    private String arriveCity;

    // 车次号或航班号
    private String trainNumber;

    // 身份证号
    private String idNum;

    // 座位、座舱等级
    private String trainSeat;

    // 出发时间
    private String leaveTime;

    // 到达时间
    private String arriveTime;

    // 里程
    private String mileage;

    // 发票在影像中的旋转角度
    private Integer orientation;

    // 是否有公司印章
    private String hasSeal;

    // 车牌号
    private String carNo;

    // 车架号/车辆识别代码
    private String carCode;

    // 发动机号码
    private String carEngineCode;

    // 机打号码
    private String machineInvoiceNo;

    // 机打代码
    private String machineInvoiceCode;

    // 异步查询码
    private String asyncCode;

    // 销售方名称
    private String saleName;

    // 购买方名称
    private String purchaserName;

    // 发票联次
    private String invoiceTemplateType;

    // 发票联次名称
    private String invoiceTemplateName;

    // 发票密文
    private String invoiceCiphertext;

    // 机器编号
    private String machineCode;

    // 备注
    private String remark;

    // 主管税务机关代码
    private String taxAuthoritiesCode;

    // 主管税务机关名称
    private String taxAuthoritiesName;

    // 厂牌型号
    private String carModel;

    // 合格证号
    private String certificateNo;

    // 二手车市场
    private String marketName;

    // 登记证号
    private String registrationNo;

    // 序列号
    private String serialNum;

    // 保险费
    private BigDecimal premium;

    // 印刷序号
    private String printNumber;

    // 开票时间
    private String invoiceTime;

    // 入口
    private String entrance;

    // 出口
    private String roadExit;

    // 高速标识
    private String isHighway;

    // 申请日期
    private String applyDate;

    // 手机号
    private String phone;

    // 省份名称
    private String province;

    // 城市名称
    private String city;

    // 行程开始日期
    private String travelStartDate;

    // 行程结束日期
    private String travelEndDate;

    // 通用机打发票标识
    private String printedMark;

    // 电子通行费标识
    private String transitMark;

    // 发票种类
    private String category;

    // 机打发票代码
    private String invoiceCodeConfirm;

    // 机打发票号码
    private String invoiceNoConfirm;

    // 开票人
    private String issuer;

    // 复核人
    private String reviewer;

    // 销方地址电话
    private String sellerAddrTel;

    // 销方开户行及账号
    private String sellerBankAccount;

    // 购方地址电话
    private String buyerAddrTel;

    // 购方开户行及账号
    private String buyerBankAccount;

    // 航班信息
    private List<OcrStandTicketsFlight> flightList;

    // 发票详情
    private List<OcrStandInvoiceDetail> detailList;

    // 行程单详情
    private List<OcrStandCarTravel> carList;

}
