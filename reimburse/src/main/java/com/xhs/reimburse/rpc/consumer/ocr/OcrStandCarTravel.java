package com.xhs.reimburse.rpc.consumer.ocr;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName OcrStandCarTravel.java
 * @createTime 2025年02月20日 10:39:00
 */
@Data
public class OcrStandCarTravel {
    // 车型
    private String carType;

    // 上车时间
    private String timeGeton;

    // 城市名称
    private String city;

    // 起点
    private String stationGeton;

    // 终点
    private String stationGetoff;

    // 里程
    private String mileage;

    // 金额
    private String amount;

}
