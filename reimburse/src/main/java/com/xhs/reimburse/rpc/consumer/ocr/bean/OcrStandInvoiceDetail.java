//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.xhs.reimburse.rpc.consumer.ocr.bean;

import lombok.Data;

import java.io.Serializable;

@Data
public class OcrStandInvoiceDetail implements Serializable {
    private String commodityName;
    private String specifictionModel;
    private String unit;
    private String quantity;
    private String unitPrice;
    private String totalAmount;
    private String taxRate;
    private String tax;
}
