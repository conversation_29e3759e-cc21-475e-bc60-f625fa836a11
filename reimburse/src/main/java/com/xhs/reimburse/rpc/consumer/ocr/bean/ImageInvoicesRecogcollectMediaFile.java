package com.xhs.reimburse.rpc.consumer.ocr.bean;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class ImageInvoicesRecogcollectMediaFile extends BasicEntity {
    private Long id;
    private Long billId;
    private String fileName;
    private String fileNo;
    private String fileAddress;
    private String originalAddress;
    private Integer fileOrder;
    private String fileType;
    private String fileFormat;
    private Integer ocrStatus;
    private String fileDesc;
    private String scanTime;
    private Integer pullType;
    private Integer collectUse;
    private Integer collectWay;
    private Integer collectType;
    private Integer collectSys;
    private Integer collectSysType;
    private Integer collectUseType;
    private String collectCompany;
    private String userId;
    private String userName;
    private String phone;
    private String orgId;
    private String orgName;
    private String createTime;
    private String updateTime;
    private String updateUser;
    private Integer isStorage;
    private Integer isBack;
    private Integer oldId;
    private String base64;
    private String base64Name;
    private List<ImageInvoicesRecogcollectFileBase64Info> attachs;
}