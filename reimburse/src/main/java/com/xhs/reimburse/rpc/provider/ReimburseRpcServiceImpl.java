package com.xhs.reimburse.rpc.provider;

import cn.hutool.core.util.StrUtil;
import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.dianping.cat.internal.shaded.com.google.common.collect.Lists;
import com.xhs.cache.RedisClient;
import com.xhs.oa.office.utils.AssertHelper;
import com.xhs.reimburse.assembler.*;
import com.xhs.reimburse.enums.ExpenseSourceEnum;
import com.xhs.reimburse.enums.InvoiceSourceEnum;
import com.xhs.reimburse.modal.dto.*;
import com.xhs.reimburse.modal.entity.ReimbursementFormEntity;
import com.xhs.reimburse.modal.request.BatchOcrParseRequest;
import com.xhs.reimburse.modal.request.BatchSaveInvoiceRequest;
import com.xhs.reimburse.modal.request.InvoiceExpenseBindRequest;
import com.xhs.reimburse.modal.request.ReimburseFormRequest;
import com.xhs.reimburse.modal.response.BatchOcrParseResponse;
import com.xhs.reimburse.service.*;
import com.xhs.reimburse.xhsoa.service.OAExpenseService;
import com.xiaohongshu.erp.common.exception.BusinessException;
import com.xiaohongshu.fls.rpc.oa.office.reimburse.ReimburseService;
import com.xiaohongshu.fls.rpc.oa.office.reimburse.entity.*;
import com.xiaohongshu.fls.rpc.oa.office.reimburse.request.*;
import com.xiaohongshu.fls.rpc.oa.office.reimburse.response.*;
import com.xiaohongshu.infra.rpc.base.Context;
import com.xiaohongshu.infra.rpc.springboot.support.annotions.EnableNettyThriftServer;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.testng.util.Strings;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date :2025/02/07 - 下午8:42
 * @description : 向外提供的报销RPC接口实现类
 */

@Service
@Slf4j
@EnableNettyThriftServer(genClass = ReimburseService.class, port = 8001, accessLog = true)
public class ReimburseRpcServiceImpl implements ReimburseService.Iface{
    // service
    @Resource
    private InvoiceService invoiceService;
    @Resource
    private ExpenseService expenseService;
    @Resource
    private ReimbursementFormService reimbursementFormService;
    @Resource
    private ReimburseFormFlowService reimburseFormFlowService;
    @Resource
    private RelationExpenseInvoiceService relationExpenseInvoiceService;
    @Resource
    private TravelApplyExpenseFormRelationService travelApplyExpenseFormRelationService;
    @Resource
    private RelationReimbursementFormExpenseService relationReimbursementFormExpenseService;
    @Resource
    private DynamicFormFieldService dynamicFormFieldService;
    @Resource
    private ReimburseCommonService reimburseCommonService;
    // convert
    @Resource
    private InvoiceAssembler invoiceAssembler;
    @Resource
    private ExpenseAssembler expenseAssembler;
    @Resource
    private ReimbursementFormAssembler reimbursementFormAssembler;
    @Resource
    private CommonAssembler commonAssembler;
    @Resource
    private MatchRuleAssembler matchRuleAssembler;
    @Autowired
    private OAExpenseService oAExpenseService;
    @Resource
    private RedisClient redisClient;

    // 发票OCR识别并添加 DONE
    @Override
    public OcrAndAddInvoiceResponse ocrAndAddInvoice(Context context, OcrAndAddInvoiceRequest request) {
        OcrAndAddInvoiceResponse response = new OcrAndAddInvoiceResponse();
        List<FileInfo> fileInfoList = request.fileInfoList;
        String userId = request.getUserId();
        String ocrOperateType = request.getOcrOperateType();

        try {
            BatchOcrParseRequest batchOcrParseRequest = invoiceAssembler.fileInfoListToRequest(fileInfoList);
            BatchOcrParseResponse batchOcrParseResponse = invoiceService.batchOcrParse(batchOcrParseRequest, false);
            List<InvoiceDto> ocrParseSuccessList = batchOcrParseResponse.getOcrParseSuccessList();
            List<InvoiceDto> ocrParseFailureList = batchOcrParseResponse.getOcrParseFailureList();
            List<InvoiceDto> ocrParseDuplicateList = batchOcrParseResponse.getOcrParseDuplicateList();

            // 保存识别好的发票
            if (Strings.isNullOrEmpty(ocrOperateType)) { // 已有接口，向前兼容逻辑，不传递参数为执行保存。
                BatchSaveInvoiceRequest batchSaveInvoiceRequest = new BatchSaveInvoiceRequest();
                batchSaveInvoiceRequest.setInvoiceSource(InvoiceSourceEnum.BOT.getCode());
                batchSaveInvoiceRequest.setInvoiceDtoList(ocrParseSuccessList);
                batchSaveInvoiceRequest.setUserId(userId);
                List<String> successInvoiceUuidList = invoiceService.batchSaveInvoices(batchSaveInvoiceRequest);
                ocrParseSuccessList = invoiceService.queryInvoice(successInvoiceUuidList);
            }

            // convert
            List<Invoice> successInvoiceList = invoiceAssembler.dtoListToRpc(ocrParseSuccessList);
            List<Invoice> failInvoiceList = invoiceAssembler.dtoListToRpc(ocrParseFailureList);
            List<Invoice> duplicateInvoiceList = invoiceAssembler.dtoListToRpc(ocrParseDuplicateList);

            response.setSuccessInvoiceList(successInvoiceList);
            response.setFailInvoiceList(failInvoiceList);
            response.setDuplicateInvoiceList(duplicateInvoiceList);
            response.setSuccess(true);
            response.setMessage("请求成功");
        } catch (Exception e) {
            log.error("ReimburseRpcService - invoiceOcrAndAdd - {}", e.getMessage(), e);
            response.setSuccess(false);
            response.setMessage("系统异常 " + e.getMessage());
        }
        return response;
    }

    // 查询发票 DONE
    @Override
    public QueryInvoiceResponse queryInvoice(Context context, QueryInvoiceRequest request) {
        QueryInvoiceResponse response = new QueryInvoiceResponse();
        List<String> invoiceUuidList = request.invoiceUuidList;

        try {
            List<InvoiceDto> invoiceDtoList = invoiceService.queryInvoice(invoiceUuidList);
            List<Invoice> invoiceList = invoiceAssembler.dtoListToRpc(invoiceDtoList);
            response.setInvoiceList(invoiceList);
            response.setSuccess(true);
            response.setMessage("请求成功");
        } catch (Exception e) {
            log.error("ReimburseRpcService - queryInvoice - {}", e.getMessage(), e);
            response.setSuccess(false);
            response.setMessage("系统异常 " + e.getMessage());
        }
        return response;
    }

    // 按规则查询发票
    @Override
    public QueryInvoiceByRuleResponse queryInvoiceByRule(Context context, QueryInvoiceByRuleRequest request) {
        QueryInvoiceByRuleResponse response = new QueryInvoiceByRuleResponse();
        InvoiceQueryRule invoiceQueryRule = request.getInvoiceQueryRule();

        try {
            InvoiceQueryRuleDto invoiceQueryRuleDto = matchRuleAssembler.invoiceQueryRuleToDto(invoiceQueryRule);
            List<String> invoiceUuidList = invoiceService.queryInvoiceByRule(invoiceQueryRuleDto);
            response.setInvoiceUuidList(invoiceUuidList);
            response.setSuccess(true);
            response.setMessage("请求成功");
        } catch (Exception e) {
            log.error("ReimburseRpcService - queryInvoiceByRule - {}", e.getMessage(), e);
            response.setSuccess(false);
            response.setMessage("系统异常 " + e.getMessage());

        }
        return response;
    }

    // 查询发票所属费用
    @Override
    public QueryInvoiceBelongExpenseResponse queryInvoiceBelongExpense(Context context, QueryInvoiceBelongExpenseRequest request) {
        QueryInvoiceBelongExpenseResponse response = new QueryInvoiceBelongExpenseResponse();
        String invoiceUuid = request.getInvoiceUuid();

        try {
            String expenseUuid = relationExpenseInvoiceService.queryInvoiceBelongExpense(invoiceUuid);
            response.setExpenseUuid(expenseUuid);
            response.setSuccess(true);
            response.setMessage("请求成功");
        } catch (Exception e) {
            log.error("ReimburseRpcService - queryInvoiceBelongExpense - {}", e.getMessage(), e);
            response.setSuccess(false);
            response.setMessage("系统异常 " + e.getMessage());
        }
        return response;
    }

    // 匹配发票 DONE
    @Override
    public MatchInvoiceResponse matchInvoice(Context context, MatchInvoiceRequest request) {
        MatchInvoiceResponse response = new MatchInvoiceResponse();
        InvoiceMatchRule invoiceMatchRule = request.getInvoiceMatchRule();

        try {
            InvoiceMatchRuleDto invoiceMatchRuleDto = matchRuleAssembler.invoiceMatchRuleToDto(invoiceMatchRule);
            List<String> expenseUuidList = invoiceService.matchInvoice(invoiceMatchRuleDto);
            response.setInvoiceUuidList(expenseUuidList);
            response.setSuccess(true);
            response.setMessage("请求成功");
        } catch (Exception e) {
            log.error("ReimburseRpcService - matchInvoice - {}", e.getMessage(), e);
            response.setSuccess(false);
            response.setMessage("系统异常 " + e.getMessage());
        }
        return response;
    }

    // 新增费用 DONE
    @Override
    public AddExpenseResponse addExpense(Context context, AddExpenseRequest request) {
        AddExpenseResponse response = new AddExpenseResponse();
        Expense expense = request.getExpense();

        try {
            ExpenseDto dto = expenseAssembler.rpcToDto(expense);
            String secondSubject = dto.getSecondSubject();
            String userId = dto.getCreatorUserId();
            List<DynamicFormFieldDto> dynamicFormFieldDtoList = dto.getDynamicFormFieldDtoList();

            // 自动填充base地和公司成员默认值
            reimburseCommonService.setUserId(userId);
            dynamicFormFieldService.buildDynamicFormDefaultValue(secondSubject, dynamicFormFieldDtoList);
            dynamicFormFieldService.buildDynamicFormDefaultValueMap(dynamicFormFieldDtoList);
            dto.setDynamicFormFieldDtoList(dynamicFormFieldDtoList);
            dto.setExpenseSource(ExpenseSourceEnum.BOT.getCode());

            // 保存费用
            String invoiceUuid = expenseService.saveExpense(dto);
            response.setExpenseUuid(invoiceUuid);
            response.setSuccess(true);
            response.setMessage("请求成功");
        } catch (Exception e) {
            log.error("ReimburseRpcService - addExpense - {}", e.getMessage(), e);
            response.setSuccess(false);
            response.setMessage("系统异常 " + e.getMessage());
        } finally {
            // 保证清除用户信息 防止内存泄漏
            reimburseCommonService.clean();
        }
        return response;
    }

    // 更新费用 DONE
    @Override
    public UpdateExpenseResponse updateExpense(Context context, UpdateExpenseRequest request) {
        UpdateExpenseResponse response = new UpdateExpenseResponse();
        Expense expense = request.getExpense();

        try {
            ExpenseDto dto = expenseAssembler.rpcToDto(expense);
            String secondSubject = dto.getSecondSubject();
            String userId = dto.getCreatorUserId();
            List<DynamicFormFieldDto> dynamicFormFieldDtoList = dto.getDynamicFormFieldDtoList();

            // 自动填充base地和公司成员默认值
            reimburseCommonService.setUserId(userId);
            dynamicFormFieldService.buildDynamicFormDefaultValue(secondSubject, dynamicFormFieldDtoList);
            dynamicFormFieldService.buildDynamicFormDefaultValueMap(dynamicFormFieldDtoList);
            dto.setDynamicFormFieldDtoList(dynamicFormFieldDtoList);

            // 更新费用
            String invoiceUuid = expenseService.updateExpense(dto);
            response.setExpenseUuid(invoiceUuid);
            response.setSuccess(true);
            response.setMessage("请求成功");
        } catch (BusinessException e) {
            response.setSuccess(false);
            response.setCode(e.getErrorCode());
            response.setMessage(e.getMessage());
        } catch (Exception e) {
            log.error("ReimburseRpcService - updateExpense - {}", e.getMessage(), e);
            response.setSuccess(false);
            response.setMessage("系统异常 " + e.getMessage());
        } finally {
            // 保证清除用户信息 防止内存泄漏
            reimburseCommonService.clean();
        }
        return response;
    }

    // 查询费用 DONE
    @Override
    public QueryExpenseResponse queryExpense(Context context, QueryExpenseRequest request) {
        QueryExpenseResponse response = new QueryExpenseResponse();
        List<String> expenseUuidList = request.getExpenseUuidList();

        try {
            List<ExpenseDto> dtoList = expenseService.queryExpense(expenseUuidList);
            List<Expense> expenseList = expenseAssembler.dtoListToRpc(dtoList);
            response.setExpenseList(expenseList);
            response.setSuccess(true);
            response.setMessage("请求成功");
        } catch (Exception e) {
            log.error("ReimburseRpcService - queryExpense - {}", e.getMessage(), e);
            response.setSuccess(false);
            response.setMessage("系统异常 " + e.getMessage());
        }
        return response;
    }

    // 按规则查询费用
    @Override
    public QueryExpenseByRuleResponse queryExpenseByRule(Context context, QueryExpenseByRuleRequest request) {
        QueryExpenseByRuleResponse response = new QueryExpenseByRuleResponse();
        ExpenseQueryRule expenseQueryRule = request.getExpenseQueryRule();

        try {
            ExpenseQueryRuleDto expenseQueryRuleDto = matchRuleAssembler.expenseQueryRuleToDto(expenseQueryRule);
            List<String> expenseUuidList = expenseService.queryExpenseByRule(expenseQueryRuleDto);
            response.setExpenseUuidList(expenseUuidList);
            response.setSuccess(true);
            response.setMessage("请求成功");
        } catch (Exception e) {
            log.error("ReimburseRpcService - queryExpenseByRule - {}", e.getMessage(), e);
            response.setSuccess(false);
            response.setMessage("系统异常 " + e.getMessage());

        }
        return response;
    }

    // 匹配费用 DONE
    @Override
    public MatchExpenseResponse matchExpense(Context context, MatchExpenseRequest request) {
        MatchExpenseResponse response = new MatchExpenseResponse();
        ExpenseMatchRule expenseMatchRule = request.getExpenseMatchRule();

        try {
            ExpenseMatchRuleDto expenseMatchRuleDto = matchRuleAssembler.expenseMatchRuleToDto(expenseMatchRule);
            List<String> expenseUuidList = expenseService.matchExpense(expenseMatchRuleDto);
            response.setExpenseUuidList(expenseUuidList);
            response.setSuccess(true);
            response.setMessage("请求成功");
        } catch (Exception e) {
            log.error("ReimburseRpcService - matchExpense - {}", e.getMessage(), e);
            response.setSuccess(false);
            response.setMessage("系统异常 " + e.getMessage());

        }
        return response;
    }

    // 修改费用-发票关系 DONE
    @Override
    public updateExpenseInvoiceRelationResponse updateExpenseInvoiceRelation(Context context, updateExpenseInvoiceRelationRequest request) {
        updateExpenseInvoiceRelationResponse response = new updateExpenseInvoiceRelationResponse();
        String userId = request.getUserId();
        String expenseUuid = request.getExpenseUuid();
        List<String> invoiceUuidList = request.getInvoiceUuidList();

        try {
            reimburseCommonService.setUserId(userId);
            relationExpenseInvoiceService.updateExpenseInvoiceRelation(userId, expenseUuid, invoiceUuidList);
            response.setSuccess(true);
            response.setMessage("请求成功");
        } catch (Exception e) {
            log.error("ReimburseRpcService - updateExpenseInvoiceRelation - {}", e.getMessage(), e);
            response.setSuccess(false);
            response.setMessage("系统异常 " + e.getMessage());
        } finally {
            // 保证清除用户信息 防止内存泄漏
            reimburseCommonService.clean();
        }
        return response;
    }

    // 新增报销单 DONE
    @Override
    public AddReimbursementFormResponse addReimbursementForm(Context context, AddReimbursementFormRequest request) {
        AddReimbursementFormResponse response = new AddReimbursementFormResponse();
        ReimbursementForm reimbursementForm = request.getReimbursementForm();

        try {
            ReimbursementFormDto dto = reimbursementFormAssembler.rpcToDto(reimbursementForm);
            String reimbursementFormUuid = reimbursementFormService.addReimbursementForm(dto);
            response.setReimbursementFormUuid(reimbursementFormUuid);
            response.setSuccess(true);
            response.setMessage("请求成功");
        } catch (Exception e) {
            log.error("ReimburseRpcService - addReimbursementForm - {}", e.getMessage(), e);
            response.setSuccess(false);
            response.setMessage("系统异常 " + e.getMessage());
        }
        return response;
    }

    // 查询报销单 DONE
    @Override
    public QueryReimbursementFormResponse queryReimbursementForm(Context context, QueryReimbursementFormRequest request) {
        QueryReimbursementFormResponse response = new QueryReimbursementFormResponse();
        List<String> reimbursementFormUuidList = request.getReimbursementFormUuidList();

        try {
            List<ReimbursementFormDto> dtoList = reimbursementFormService.queryReimbursementForm(reimbursementFormUuidList);
            List<ReimbursementForm> reimbursementFormList = reimbursementFormAssembler.dtoListToRpc(dtoList);
            response.setReimbursementFormList(reimbursementFormList);
            response.setSuccess(true);
            response.setMessage("请求成功");
        } catch (Exception e) {
            log.error("ReimburseRpcService - queryReimbursementForm - {}", e.getMessage(), e);
            response.setSuccess(false);
            response.setMessage("系统异常 " + e.getMessage());
        }
        return response;
    }

    @Override
    public QueryTravelApplyFormRelationResponse queryTravelApplyFormRelation(Context context, QueryTravelApplyFormRelationRequest request) throws TException {

        QueryTravelApplyFormRelationResponse response = new QueryTravelApplyFormRelationResponse();
        response.setSuccess(true).setMessage("");

        try {

            String travelApplyFormNum = request.getTravelApplyFormNum();
            AssertHelper.notBlank(travelApplyFormNum, "出差申请单号不能为空");

            List<String> formIds
                    = travelApplyExpenseFormRelationService.queryRelationReimbursementFormIds(travelApplyFormNum);
            List<ReimbursementFormDto> dtos = reimbursementFormService.queryReimbursementForm(formIds);

            response.setReimbursementForms(reimbursementFormAssembler.dtoListToRpc(dtos));
        } catch (Exception e) {

            log.error("ReimburseRpcService queryTravelApplyFormRelation error: {}", e.getMessage(), e);
            response.setSuccess(false);
            response.setMessage("系统异常: " + e.getMessage());
        }

        return response;
    }

    // 修改报销单-费用关系 DONE
    @Override
    public UpdateReimbursementFormExpenseRelationResponse updateReimbursementFormExpenseRelation(Context context, UpdateReimbursementFormExpenseRelationRequest request) {
        UpdateReimbursementFormExpenseRelationResponse response = new UpdateReimbursementFormExpenseRelationResponse();
        String userId = request.getUserId();
        String reimbursementFormUuid = request.getReimbursementFormUuid();
        List<String> expenseUuidList = request.getExpenseUuidList();

        try {

            List<ReimbursementFormEntity> reimbursementFormEntityList = reimbursementFormService.queryReimbursementFormEntity(Lists.newArrayList(reimbursementFormUuid));
            if (!CollectionUtils.isEmpty(reimbursementFormEntityList)) {
                ReimbursementFormEntity entity = reimbursementFormEntityList.get(0);

                ReimburseFormRequest reimburseFormRequest = StrUtil.isNotBlank(entity.getFormContent())
                        ? JSONObject.parseObject(entity.getFormContent(), ReimburseFormRequest.class) :
                        new ReimburseFormRequest();

                reimburseFormRequest.setId(entity.getId());
                reimburseFormRequest.setExpenseNos(expenseUuidList);
                reimburseFormRequest.setCreatorNo(userId);
                reimburseFormRequest.setFormType(entity.getFormType());
                reimburseFormRequest.setFormNum(entity.getFormNum());
                reimburseFormRequest.setUuid(entity.getUuid());
                reimburseFormFlowService.saveOrSubmitReimbursementForm(reimburseFormRequest, true);
            }
            response.setSuccess(true);
            response.setMessage("请求成功");
        } catch (Exception e) {
            log.error("ReimburseRpcService - updateReimbursementFormExpenseRelation - {}", e.getMessage(), e);
            response.setSuccess(false);
            response.setMessage("系统异常 " + e.getMessage());
        }
        return response;
    }

    // 获取动态字段
    @Override
    public QueryDynamicFormFieldListResponse queryDynamicFormFieldList(Context context, QueryDynamicFormFieldListRequest request) {
        QueryDynamicFormFieldListResponse response = new QueryDynamicFormFieldListResponse();
        String userId = request.getUserId();

        try {
            reimburseCommonService.setUserId(userId);
            DynamicFormFieldListQueryDto dto = commonAssembler.rpcToDynamicFormFieldListQueryDto(request);
            List<DynamicFormFieldDto> dynamicFormFieldList = dynamicFormFieldService.queryDynamicFormFieldList(dto);
            response.setDynamicFieldList(commonAssembler.dynamicFormFieldListToRpc(dynamicFormFieldList));
            response.setSuccess(true);
            response.setMessage("请求成功");
        } catch (Exception e) {
            log.error("ReimburseRpcService - queryDynamicFormFieldList - {}", e.getMessage(), e);
            response.setSuccess(false);
            response.setMessage("系统异常 " + e.getMessage());
        } finally {
            // 保证清除用户信息 防止内存泄漏
            reimburseCommonService.clean();
        }
        return response;
    }

    //主动关联差旅申请单
    @Override
    public CreateTravelApplyReimburseRelationshipResponse createTravelApplyReimburseRelationship(Context context
            , CreateTravelApplyReimburseRelationshipRequest request) {

        CreateTravelApplyReimburseRelationshipResponse response = new CreateTravelApplyReimburseRelationshipResponse();
        response.setSuccess(true).setMessage("");

        try {

            Map<String, List<String>> resultMap = new HashMap<>();
            String reimburseFormId = request.getReimburseFormId();

            List<String> travelApplyFormNums
                    = travelApplyExpenseFormRelationService.createRelationship(reimburseFormId
                    , request.getTravelApplyFormNum());

            resultMap.put(reimburseFormId, travelApplyFormNums);
            response.setRelationships(resultMap);
        } catch (Exception e) {

            response.setSuccess(false);
            response.setMessage("系统异常");
            log.error("ReimburseRpcService createTravelApplyReimburseRelationship error: {}", e.getMessage(), e);
        }

        return response;
    }

    @Override
    public TravelFormInfoResponse getMealBXDInfo(Context context, TravelFormQueryParam request) throws TException {
        TravelFormInfoResponse response = new TravelFormInfoResponse();
        response.setSuccess(true);
        try {
            List<TravelFromInfo> travelFormInfos = new ArrayList<>();
            oAExpenseService.getMealBXDInfo(request, travelFormInfos);
            response.setTravelFormInfos(travelFormInfos);
        } catch (Exception e) {
            log.error("getTravelFormInfoByParam request :{}, error message:{}", JSON.toJSONString(request), e.getMessage(), e);
            response.setSuccess(false);
            response.setErrorMessage(e.getMessage());
        }
        return response;
    }

    @Override
    public GetInvoiceExpenseBindInfoResponse getInvoiceExpenseBindInfo(Context context, InvoiceExpenseBindQueryRequest request) throws TException {
        GetInvoiceExpenseBindInfoResponse response = new GetInvoiceExpenseBindInfoResponse();
        response.setSuccess(true);
        String redisKey = String.format("invoice_expense_bind:%s:%s", request.getSessionId(), request.getSceneId());
        Object result = redisClient.get(redisKey);
        List<InvoiceExpenseBindRequest.InvoiceExpenseBind> list = (List<InvoiceExpenseBindRequest.InvoiceExpenseBind>) result;
        if (CollectionUtils.isEmpty(list)) {
            response.setMessage("预分类内容为空");
            return response;
        }
        List<InvoiceExpenseBind> collect = list.stream().map(l -> {
            InvoiceExpenseBind bind = new InvoiceExpenseBind();
            bind.setInvoiceUuid(l.getInvoiceUuid());
            bind.setExpenseType(l.getExpenseType());
            return bind;
        }).collect(Collectors.toList());
        response.setInvoiceExpenseBindList(collect);
        return response;
    }


    // 查询费用所属报销单
    @Override
    public QueryExpenseBelongReimbursementFormResponse queryExpenseBelongReimbursementForm(Context context, QueryExpenseBelongReimbursementFormRequest request) {
        QueryExpenseBelongReimbursementFormResponse response = new QueryExpenseBelongReimbursementFormResponse();
        String expenseUuid = request.getExpenseUuid();

        try {
            String reimbursementFormUuid = relationReimbursementFormExpenseService.queryExpenseBelongReimbursementForm(expenseUuid);
            response.setReimbursementFormUuid(reimbursementFormUuid);
            response.setSuccess(true);
            response.setMessage("请求成功");
        } catch (Exception e) {
            log.error("ReimburseRpcService - queryExpenseBelongReimbursementForm - {}", e.getMessage(), e);
            response.setSuccess(false);
            response.setMessage("系统异常 " + e.getMessage());
        }
        return response;
    }

    @Override
    public CheckReimbursementFormResponse checkReimbursementForm(Context context, CheckReimbursementFormRequest request) {
        CheckReimbursementFormResponse response = new CheckReimbursementFormResponse();
        String formNum = request.getFormNum();
        String userId = request.getUserId();
        try {
            reimburseCommonService.setUserId(userId);
            ReimbursementFormCheckResultDto dto = reimbursementFormService.checkReimbursementForm(formNum, userId);
            ReimbursementFormCheckResult result = reimbursementFormAssembler.reimbursementFormCheckResultDtoToRpc(dto);
            response.setReimbursementFormCheckResult(result);
            response.setSuccess(true);
            response.setMessage("请求成功");
        } catch (Exception e) {
            log.error("ReimburseRpcService - checkReimbursementForm - {}", e.getMessage(), e);
            response.setSuccess(false);
            response.setMessage("系统异常 " + e.getMessage());
        } finally {
            // 保证清除用户信息 防止内存泄漏
            reimburseCommonService.clean();
        }
        return response;
    }
}