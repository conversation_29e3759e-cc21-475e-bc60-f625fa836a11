package com.xhs.reimburse.modal.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TravelMealOverLimitReasonDto.java
 * @createTime 2025年03月13日 15:01:00
 */
@Data
public class TravelMealOverLimitReasonDto {

    @ApiModelProperty(notes = "超标原因")
    private String reason;

    @ApiModelProperty(notes = "超标原因类型")
    private String reasonType;

    @ApiModelProperty(notes = "超标原因类型名称")
    private String reasonTypeName;

    @ApiModelProperty(notes = "相关用户列表")
    private List<UserDto> userList;

    public Boolean isComplete() {
        if (!StringUtils.isEmpty(reasonType) && !StringUtils.isEmpty(reason)) {
            return true;
        }

        if (!StringUtils.isEmpty(reasonType) && !CollectionUtils.isEmpty(userList)) {
            return true;
        }

        return false;
    }
}
