package com.xhs.reimburse.modal.response;

import com.xhs.reimburse.modal.dto.*;
import com.xhs.reimburse.modal.entity.EmployeeEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ReimbursementFormPrintResponse {

    @ApiModelProperty(notes = "报销表单唯一键")
    private String uuid;

    @ApiModelProperty(notes = "报销/收款金额")
    private BigDecimal amount;

    @ApiModelProperty(notes = "发票总金额")
    private BigDecimal ticketTotalAmount;

    @ApiModelProperty(notes = "单据号")
    private String formNum;

    @ApiModelProperty(notes = "单据类型")
    private String formType;

    @ApiModelProperty(notes = "单据类型")
    private String formTypeName;

    @ApiModelProperty(notes = "收款人相关信息")
    private ReimbursePrintBasicInfo reimburseBasicInfoVo;

    @ApiModelProperty(notes = "提交人信息")
    private EmployeeEntity creatorInfo;

    @ApiModelProperty(notes = "费用发票信息")
    private List<ExpenseInvoicePrintVo> expenses;

    @ApiModelProperty(notes = "费用发票数量信息")
    private Map<String, Integer> invoiceTypeCountMap;

    @ApiModelProperty(notes = "打印单据的备注栏")
    private String remark;

}
