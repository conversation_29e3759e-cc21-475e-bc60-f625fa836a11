package com.xhs.reimburse.modal.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName QueryTravelCityRequest.java
 * @createTime 2025年03月13日 15:27:00
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryTravelCityRequest {
    @ApiModelProperty("查询对象id")
    private String userId;

    @ApiModelProperty("差旅申请单")
    private List<String> formNums;

    @ApiModelProperty("城市搜索关键字")
    private String keyWord;

    @ApiModelProperty("一级科目")
    private String subject;

    @ApiModelProperty("二级科目")
    private String secondSubject;
}
