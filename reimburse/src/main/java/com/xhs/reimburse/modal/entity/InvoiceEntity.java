package com.xhs.reimburse.modal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date :2025/02/08 - 上午11:20
 * @description : 发票
 */
@Data
@TableName("invoice")
public class InvoiceEntity {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("uuid")
    private String uuid;

    @TableField("invoice_type")
    private String invoiceType;

    @TableField("invoice_code")
    private String invoiceCode;

    @TableField("invoice_no")
    private String invoiceNo;

    @TableField("ticket_type")
    private String ticketType;

    @TableField("invoice_validate_result")
    private Integer invoiceValidateResult;

    @TableField("invoice_validate_fail_reason")
    private String invoiceValidateFailReason;

    @TableField("creator_user_id")
    private String creatorUserId;

    @TableField("create_time")
    private Date createTime;

    @TableField("update_time")
    private Date updateTime;

    @TableField("status")
    private Integer status;

    @TableField("invoice_status")
    private Integer invoiceStatus;

    @TableField("upload_file")
    private String uploadFile;

    @TableField("exception")
    private String exception;

    @TableField("details")
    private String details;

    @TableField("ocr_source_content")
    private String ocrSourceContent;

    @TableField("check_code")
    private String checkCode;

    @TableField("ocr_content_modified")
    private Integer ocrContentModified;

    @TableField("invoice_source")
    private Integer invoiceSource;

    @TableField("invoice_expense_status")
    private Integer invoiceExpenseStatus;

    @TableField("invoice_form_status")
    private Integer invoiceFormStatus;
}
