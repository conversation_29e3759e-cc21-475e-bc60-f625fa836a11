package com.xhs.reimburse.modal.dto;

import com.xhs.oa.office.utils.AssertHelper;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ReimburseBasicInfo {

    @ApiModelProperty(notes = "选择的付款公司")
    private String paymentCompanyName;

    @ApiModelProperty(notes = "选择的付款主体编码")
    private String paymentCompanyCode;

    @ApiModelProperty(notes = "收款银行名称")
    private String gatheringBank;

    @ApiModelProperty(notes = "收款银行联行号")
    private String gatheringBankCode;

    @ApiModelProperty(notes = "收款账号")
    private String gatheringAccount;

    @ApiModelProperty(notes = "收款账户名称")
    private String gatheringName;

    @ApiModelProperty(notes = "当前用户所在部门ID")
    private String departmentId;

    @ApiModelProperty(notes = "当前用户所在部门名称")
    private String departmentName;

    @ApiModelProperty(notes = "当前用户所在部门ID路径")
    private List<String> departmentIdPath;

    @ApiModelProperty(notes = "当前用户所在部门名称路径")
    private List<String> departmentNamePath;

    @ApiModelProperty(notes = "选择的预算归属部门ID")
    private String budgetDepartmentId;

    @ApiModelProperty(notes = "选择的预算归属部门名称")
    private String budgetDepartmentName;

    @ApiModelProperty(notes = "选择的预算归属部门ID路径")
    private List<String> budgetDepartmentNamePath;

    @ApiModelProperty(notes = "选择的预算归属部门名称路径")
    private List<String> budgetDepartmentIdPath;

    public boolean fieldsCheck4Save() {
        return StringUtils.isNotBlank(paymentCompanyName) &&
                StringUtils.isNotBlank(gatheringBank) &&
                StringUtils.isNotBlank(gatheringBankCode) &&
                StringUtils.isNotBlank(gatheringAccount) &&
                StringUtils.isNotBlank(gatheringName) &&
                StringUtils.isNotBlank(departmentId) &&
                StringUtils.isNotBlank(budgetDepartmentId) &&
                CollectionUtils.isNotEmpty(departmentIdPath) &&
                CollectionUtils.isNotEmpty(budgetDepartmentNamePath);
    }

    public void fieldsCheck4Submit() {
        AssertHelper.notBlank(paymentCompanyName, "请选择付款公司");
        AssertHelper.notBlank(gatheringBank, "收款银行名称不能为空");
        AssertHelper.notBlank(gatheringBankCode, "收款银行联行号不能为空");
        AssertHelper.notBlank(gatheringAccount, "收款账号不能为空");
        AssertHelper.notBlank(gatheringName, "收款账户名称不能为空");
        AssertHelper.notNull(departmentId, "请选择当前所属部门");
        AssertHelper.notNull(budgetDepartmentId, "请选择预算归属部门");
        AssertHelper.notEmpty(departmentIdPath, "请选择当前所属部门");
        AssertHelper.notEmpty(budgetDepartmentNamePath, "请选择预算归属部门");
    }
}
