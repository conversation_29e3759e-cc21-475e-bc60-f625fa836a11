package com.xhs.reimburse.modal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date :2025/02/12 - 下午8:54
 * @description :
 */
@Data
@TableName("relation_reimbursement_form_expense")
public class RelationReimbursementFormExpenseEntity {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("reimbursement_form_uuid")
    private String reimbursementFormUuid;

    @TableField("expense_uuid")
    private String expenseUuid;

    @TableField("create_time")
    private Date createTime;

    @TableField("update_time")
    private Date updateTime;
}
