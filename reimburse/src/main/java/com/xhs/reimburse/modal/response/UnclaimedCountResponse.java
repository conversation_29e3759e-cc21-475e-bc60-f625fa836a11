package com.xhs.reimburse.modal.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName UnclaimedCountResponse.java
 * @createTime 2025年02月11日 17:34:00
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UnclaimedCountResponse {
    @ApiModelProperty("发票夹未报销的数量")
    private Integer invoiceFolderUnclaimedCount;

    @ApiModelProperty("费用夹未报销的数量")
    private Integer expenseFolderUnclaimedCount;

    @ApiModelProperty("报销单待报销的数量")
    private Integer reimbursementFolderUnclaimedCount;
}
