package com.xhs.reimburse.modal.dto.travel;

import com.xhs.reimburse.constant.TravelConstant;
import com.xhs.reimburse.enums.ExpenseSecondSubjectEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ExpenseContentDto.java
 * @createTime 2025年03月21日 14:50:00
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExpenseContentDto {

    @ApiModelProperty("城市code")
    private String city;

    @ApiModelProperty("城市名字")
    private String cityName;

    @ApiModelProperty("出差天数")
    private Integer travelDays;

    @ApiModelProperty("金额")
    private BigDecimal amount;

    @ApiModelProperty("是否存在室友-酒店科目需要填写")
    private Boolean hasPartner = false;

    /**
     * 获取国家名称
     * @desc 差旅申请单国家展示:
     * 1.国内的城市 例如 北京
     * 2.国外的城市-1 例如 东京(国外)  //此格式为历史数据 新的国外地点名称拼接的时具体的国家名称
     * 3.国外的城市-2 例如 东京(日本)
     * @return 取值规则:
     * 没有()的说明时国内城市 return "中国"
     * 存在()并且包含国外说明是历史的数据 此数据不计算是否超标 return ""
     * 存在()并且不包含国外说明是 return "XX"
     */
    public String getCountryName() {

        if (StringUtils.isBlank(cityName)) {
            return "中国";
        }

        if (!cityName.contains("(")){
            return "中国";
        }

        if (cityName.contains(TravelConstant.OVERSEAS)){
            return "";
        }

        return cityName.substring( cityName.indexOf("(") + 1 , cityName.indexOf(")") );
    }

    /**
     * 获取差标时是否需要根据是否合住
     *存在合住人并且二级科目为 北上广深 || 国内其他
     * @param secondSubject 二级科目
     * @return 1需要 0不需要
     */
    public String hotelNeedPartner(String secondSubject) {
        return hasPartner && (ExpenseSecondSubjectEnum.BSGS.getSubjectCode().equals(secondSubject)
                || ExpenseSecondSubjectEnum.GNQTCS.getSubjectCode().equals(secondSubject))
                ? "1" : "0";
    }

    /**
     * 是否超标
     * @param travelBudgetStandard 差标总预算 每日差标 * 天数
     * @return 差标总预算 < 报销总金额
     */
    public boolean isOverBudget(BigDecimal travelBudgetStandard) {
        return travelBudgetStandard.compareTo(amount) < 0;
    }

    /**
     * 裁剪国外城市名称,源名称: 纽约(美国)
     * @return 纽约
     */
    public String subOverseasCityName() {
        if (StringUtils.isNotBlank(cityName) && cityName.contains("(")){
            return cityName.split("\\(")[0];
        }
        return cityName;
    }
}
