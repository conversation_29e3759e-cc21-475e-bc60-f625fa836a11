package com.xhs.reimburse.modal.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date :2025/02/11 - 上午10:37
 * @description :
 */
@Data
public class BankAccountDto {

    @ApiModelProperty("开户银行")
    private String bankName;

    @ApiModelProperty("收款人姓名")
    private String accountName;

    @ApiModelProperty(notes = "银行卡号")
    private String accountNo;

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("开户银行联行号")
    private String bankCode;

    @ApiModelProperty(notes = "是否默认选项")
    private Integer isDefault;

}