package com.xhs.reimburse.modal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName RelationExpenseInvoiceEntity.java
 * @createTime 2025年02月18日 14:01:00
 */
@Data
@TableName("relation_expense_invoice")
public class RelationExpenseInvoiceEntity {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("expense_uuid")
    private String expenseUuid;

    @TableField("invoice_uuid")
    private String invoiceUuid;

    @TableField("create_time")
    private Date createTime;

    @TableField("update_time")
    private Date updateTime;
}
