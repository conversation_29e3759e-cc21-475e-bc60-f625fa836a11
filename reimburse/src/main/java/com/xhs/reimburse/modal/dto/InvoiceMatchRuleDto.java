package com.xhs.reimburse.modal.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date :2025/02/22 - 下午3:14
 * @description :
 */
@Data
public class InvoiceMatchRuleDto {
    // 用户ID
    String userId;

    // 匹配金额
    String amount;

    // 金额差值
    String amountDiff;

    // 匹配时间
    String time;

    // 发票状态列表
    List<Integer> invoiceStatusList;

    // 发票-费用状态列表
    List<Integer> invoiceExpenseStatusList;
}
