package com.xhs.reimburse.modal.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date :2025/02/08 - 下午4:53
 * @description : 报销单
 */
@Data
public class ReimbursementFormDto {
    @ApiModelProperty("报销单ID")
    private String uuid;

    @ApiModelProperty("REDflow的formNum")
    private String formNum;

    @ApiModelProperty("单据类型")
    private String formType;

    @ApiModelProperty("人民币金额")
    private BigDecimal amount;

    @ApiModelProperty("报销状态")
    private Integer reimburseStatus;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("是否有效 0：无效 1：有效")
    private Integer isValid;

    @ApiModelProperty("创建人ID")
    private String creatorNo;

    @ApiModelProperty("创建人名称")
    private String creator;

    @ApiModelProperty("更新人ID")
    private String updaterNo;

    @ApiModelProperty("创建人名称")
    private String updater;

    @ApiModelProperty("表单内容")
    private String formContent;

    @ApiModelProperty("关联费用详情")
    private List<ExpenseDto> relationExpenseList;

    @ApiModelProperty(notes = "差旅申请单据号列表")
    private List<String> travelApplyFormNums;
}
