package com.xhs.reimburse.modal.request;

import com.xhs.reimburse.modal.dto.FileInfoDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName BatchOcrParseRequest.java
 * @createTime 2025年02月13日 17:12:00
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BatchOcrParseRequest {

    @ApiModelProperty("ocr识别的列表")
    @NotEmpty(message = "orcParseItems不能为空")
    List<FileInfoDto> orcParseItems;

    String userId;
}
