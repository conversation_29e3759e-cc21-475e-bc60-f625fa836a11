package com.xhs.reimburse.modal.dto.travel;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
public class TravelPlaceDto {

    //地点 code
    @NotBlank
    private String placeCode;

    //地点名称
    @NotBlank
    private String placeName;

    //地点所在国家ID
    private String countryId;

    //地点所在国家名称
    private String countryName;

    //行程中展示名称，国外地点展示: 东京(日本)。    历史展示:东京(国外)
    private String showPlaceName;

    //地点类型 北上广深，国内其他，亚洲城市或港澳台，亚洲以外
    private String placeType;

    //是否是大陆地点 true:大陆地点
    private Boolean domestic;

    //地点行程子类型 交通：飞机、火车、汽车、其他
    private String scheduleSubType;

}
