package com.xhs.reimburse.modal.dto;

import com.xhs.reimburse.enums.ExpenseIntegrityFieldEnum;
import com.xhs.reimburse.enums.ReimbursementFormBaseInfoEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date :2025/07/08 - 下午5:18
 * @description :
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BlockItemDto {
    /**
     * 阻碍项字段code
     * @see ReimbursementFormBaseInfoEnum
     * @see ExpenseIntegrityFieldEnum
     */
    private String fieldCode;

    /**
     * 阻碍项字段名称
     * @see ReimbursementFormBaseInfoEnum
     * @see ExpenseIntegrityFieldEnum
     */
    private String fieldName;
}
