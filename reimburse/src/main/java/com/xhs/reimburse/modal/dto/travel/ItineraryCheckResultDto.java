package com.xhs.reimburse.modal.dto.travel;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ItineraryCheckResultDto.java
 * @createTime 2025年03月18日 10:28:00
 */
@Data
public class ItineraryCheckResultDto {

    @ApiModelProperty("阻碍提报")
    private List<CheckResultItemDto> blockSubmissionCheckResultList;

    @ApiModelProperty("不阻碍提报")
    private List<CheckResultItemDto> allowSubmissionCheckResultList;
}
