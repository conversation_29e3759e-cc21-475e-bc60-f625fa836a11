package com.xhs.reimburse.modal.dto;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DynamicFormFieldDto.java
 * @createTime 2025年02月13日 15:08:00
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DynamicFormFieldDto {

    @ApiModelProperty("字段code")
    private String fieldCode;

    @ApiModelProperty("字段名称")
    private String fieldName;

    @ApiModelProperty("是否为必填项")
    private boolean must;

    @ApiModelProperty("前端使用的组件类型")
    private String component;

    @ApiModelProperty("是否在列表页展示")
    private boolean isDisplayed;

    @ApiModelProperty("提示文案")
    private String placeholder;

    @ApiModelProperty("是否有默认值")
    private boolean hasDefault = false;

    @ApiModelProperty("默认值")
    @JsonSetter(nulls = Nulls.SKIP)
    private Object defaultValue;

    @ApiModelProperty("默认值映射文案")
    private Map<String,Object> defaultValueMap;


    @PostConstruct
    public void init() {
        if (defaultValue == null) {
            defaultValue = new Object();
        }
        if (defaultValueMap == null) {
            defaultValueMap = new HashMap<>();
        }
    }

    public DynamicFormFieldDto(DynamicFormFieldDto fieldDto){
        this.fieldCode = fieldDto.getFieldCode();
        this.fieldName = fieldDto.getFieldName();
        this.must = fieldDto.isMust();
        this.component = fieldDto.getComponent();
        this.isDisplayed = fieldDto.isDisplayed();
        this.placeholder = fieldDto.getPlaceholder();
        this.defaultValueMap = new HashMap<>();
        // 不设置默认值和是否有默认值
    }

    public static List<DynamicFormFieldDto> deepCopyAndInitDefaults(List<DynamicFormFieldDto> fields) {
        return fields.stream().map(field -> {
            DynamicFormFieldDto newField = new DynamicFormFieldDto();
            newField.setFieldCode(field.getFieldCode());
            newField.setFieldName(field.getFieldName());
            newField.setMust(field.isMust());
            newField.setComponent(field.getComponent());
            newField.setDisplayed(field.isDisplayed());
            newField.setPlaceholder(field.getPlaceholder());
            newField.setHasDefault(field.isHasDefault());
            newField.setDefaultValue(field.getDefaultValue());
            newField.setDefaultValueMap(field.getDefaultValueMap());
            return newField;
        }).collect(Collectors.toList());
    }
}
