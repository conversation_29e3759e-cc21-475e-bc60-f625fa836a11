package com.xhs.reimburse.modal.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date :2025/02/07 - 下午8:39
 * @description :
 */
@Data
public class BankAccountRequest {

    @ApiModelProperty("开户银行")
    @NotBlank(message = "开户银行不能为空")
    private String bankName;

    @ApiModelProperty("收款人姓名")
    @NotBlank(message = "收款账户名称不能为空")
    private String accountName;

    @ApiModelProperty(notes = "银行卡号")
    @NotBlank(message = "银行卡号不能为空")
    private String accountNo;

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("开户银行联行号")
    @NotBlank(message = "开户银行联行号不能为空")
    private String bankCode;

    @ApiModelProperty(notes = "是否默认选项")
    @NotNull(message = "默认账户选项不能为空")
    private Integer isDefault;

    @ApiModelProperty(notes = "是否要删除")
    @NotNull(message = "是否删除项不能为空")
    private Integer isValid;

}
