package com.xhs.reimburse.modal.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ExpenseDeleteRequest.java
 * @createTime 2025年02月14日 10:44:00
 */
@Data
public class ExpenseDeleteRequest {
    @NotNull(message = "uuid 不能为空")
    @ApiModelProperty("费用uuid")
    private String uuid;
}
