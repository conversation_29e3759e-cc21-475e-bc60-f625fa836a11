package com.xhs.reimburse.modal.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ChangeSynStatusRequest.java
 * @createTime 2025年02月13日 15:49:00
 */
@Data
public class ChangeSynStatusRequest {

    @NotNull(message = "状态不能为空")
    @ApiModelProperty("true:开启 false:关闭")
    private Boolean status;
}
