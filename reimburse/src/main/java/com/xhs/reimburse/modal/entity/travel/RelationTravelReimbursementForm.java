package com.xhs.reimburse.modal.entity.travel;

import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Table: relation_travel_reimbursement_form
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RelationTravelReimbursementForm {
    /**
     * Column: id
     * Type: BIGINT UNSIGNED
     */
    private Long id;

    /**
     * Column: travel_form_num
     * Type: VARCHAR(64)
     * Remark: 差旅申请单号
     */
    private String travelFormNum;

    /**
     * Column: reimbursement_id
     * Type: VARCHAR(64)
     * Remark: 报销单UUID
     */
    private String reimbursementId;

    /**
     * Column: is_valid
     * Type: TINYINT(3)
     * Default value: 1
     * Remark: 是否生效  1：有效，0：无效
     */
    private Byte isValid;

    /**
     * Column: creator_no
     * Type: VARCHAR(64)
     * Remark: 创建人id
     */
    private String creatorNo;

    /**
     * Column: create_time
     * Type: DATETIME
     * Default value: CURRENT_TIMESTAMP
     * Remark: 创建时间
     */
    private Date createTime;

    /**
     * Column: updater_no
     * Type: VARCHAR(64)
     * Remark: 更新人id
     */
    private String updaterNo;

    /**
     * Column: update_time
     * Type: DATETIME
     * Default value: CURRENT_TIMESTAMP
     * Remark: 更新时间
     */
    private Date updateTime;

    public RelationTravelReimbursementForm(Byte isValid){
        this.isValid = isValid;
    }

    public RelationTravelReimbursementForm(String travelFormNum, String reimbursementId) {
        this.travelFormNum = travelFormNum;
        this.reimbursementId = reimbursementId;
        this.isValid = 1;
    }
}