package com.xhs.reimburse.modal.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApiHubRequest {
    public String apiCode;
    public String businessType;
    public String businessSubType;
    public String businessCode;
    public String businessElement;
    public String content;
    public Map<String, List<String>> headers;
    public Map<String, String> extra;
    public Class<?> clazz;
}
