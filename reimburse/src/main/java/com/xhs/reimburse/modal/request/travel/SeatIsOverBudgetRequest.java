package com.xhs.reimburse.modal.request.travel;

import com.xhs.reimburse.modal.dto.travel.SeatContentDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName SeatIsOverBudgetRequest.java
 * @createTime 2025年03月22日 15:03:00
 */
@Data
public class SeatIsOverBudgetRequest {

    @ApiModelProperty("需要校验的座位类型")
    private List<SeatContentDto> checkList;

    @ApiModelProperty("单据类型")
    private String formType;

    @ApiModelProperty("费用一级科目")
    private String firstSubject;
}
