package com.xhs.reimburse.modal.dto.travel;

import com.xhs.reimburse.enums.travel.TravelScheduleTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TravelCheckParamsDto.java
 * @createTime 2025年03月18日 20:42:00
 */
@Data
@AllArgsConstructor
public class TravelCheckParamsDto {

    // 差旅类型
    private final List<String> validTravelTypes;
    //
    private final TravelScheduleTypeEnum scheduleType;

    public boolean isApplicable(String travelType) {
        return CollectionUtils.isEmpty(validTravelTypes) || (!StringUtils.isEmpty(travelType) && validTravelTypes.contains(travelType));
    }

    public TravelScheduleTypeEnum getScheduleType() {
        return scheduleType;
    }
}
