package com.xhs.reimburse.modal.dto.travel;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.xhs.reimburse.enums.travel.TravelScheduleTypeEnum.HOTEL;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TravelDateDto.java
 * @createTime 2025年03月20日 15:27:00
 */
@Data
@Slf4j
public class TravelDateDto {

    private Date startDate;
    private Date endDate;
    private String scheduleType;
    private Integer travelPeopleNumber;
    private String startD;
    private String endD;

    public Integer getDateScope(){
        if (startDate == null || endDate == null) {
            return null;
        }

        long diff = endDate.getTime() - startDate.getTime();
        if (diff < 0) {
            return null;
        }

        int days = (int) TimeUnit.DAYS.convert(diff, TimeUnit.MILLISECONDS);
        if (!HOTEL.getRouteType().equals(scheduleType)) {
            days ++;
        }
        return days;
    }

    public List<String> findDates(Boolean isHotel){
        if (isHotel) {
            return findDates(startD, addDays(endD, -1));
        }
        return findDates(startD, endD);
    }

    private String addDays(String dateStr, int days) {
        String endTime = "";
        try {
            Date startTime = null;
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date date = sdf.parse(dateStr);
            Calendar rightNow = Calendar.getInstance();
            rightNow.setTime(date);
            rightNow.add(Calendar.DAY_OF_YEAR, days);
            endTime = sdf.format(rightNow.getTime());
        } catch (Exception e) {
            log.error("addDays error", e);
        }
        return endTime;
    }

    /***
     * 获取二个时间点的日期
     */
    private List<String> findDates(String beginTime, String endTime) {
        try {
            List<String> allDate = new ArrayList<>();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date dBegin = sdf.parse(beginTime);
            Date dEnd = sdf.parse(endTime);
            allDate.add(sdf.format(dBegin));
            Calendar calBegin = Calendar.getInstance();
            // 使用给定的 Date 设置此 Calendar 的时间
            calBegin.setTime(dBegin);
            Calendar calEnd = Calendar.getInstance();
            // 使用给定的 Date 设置此 Calendar 的时间
            calEnd.setTime(dEnd);
            // 测试此日期是否在指定日期之后
            while (dEnd.after(calBegin.getTime())) {
                // 根据日历的规则，为给定的日历字段添加或减去指定的时间量
                calBegin.add(Calendar.DAY_OF_MONTH, 1);
                allDate.add(sdf.format(calBegin.getTime()));
            }
            return allDate;
        } catch (Exception e) {
            log.error("获取时间天数异常{}", e.getMessage());
        }
        return new ArrayList<>();
    }
}
