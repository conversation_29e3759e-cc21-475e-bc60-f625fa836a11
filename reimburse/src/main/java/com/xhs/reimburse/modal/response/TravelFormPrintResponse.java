package com.xhs.reimburse.modal.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class TravelFormPrintResponse extends ReimbursementFormPrintResponse{

    @ApiModelProperty(notes = "差旅申请单号")
    private List<String> travelApplyFormNums;

    @ApiModelProperty(notes = "关联的差旅申请单流程")
    private List<TravelApplySummaryInfo> travelScheduleSummaryInfos;;

}
