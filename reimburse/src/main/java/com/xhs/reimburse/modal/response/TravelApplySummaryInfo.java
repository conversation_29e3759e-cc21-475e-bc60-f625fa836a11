package com.xhs.reimburse.modal.response;

import com.xhs.reimburse.modal.dto.travel.TravelerDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class TravelApplySummaryInfo {

    @ApiModelProperty("差旅申请单")
    private String travelFormNum;

    @ApiModelProperty("出差人")
    private String traveler;

    @ApiModelProperty("出差人")
    private List<TravelerDto> travelers;

    @ApiModelProperty("出差时间范围")
    private String datePhase;

    @ApiModelProperty("地点")
    private List<List<String>> cities;

    @ApiModelProperty("出差事由")
    private String travelReason;

    @ApiModelProperty("行程开始时间")
    private String startDate;

    @ApiModelProperty("行程结束时间")
    private String endDate;

}
