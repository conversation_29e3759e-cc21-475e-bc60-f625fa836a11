package com.xhs.reimburse.modal.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ExpenseInvoicePrintVo {

    @ApiModelProperty("uuid")
    private String uuid;

    @ApiModelProperty("一级科目")
    private String firstSubject;

    @ApiModelProperty("二级科目")
    private String secondSubject;

    @ApiModelProperty("日期")
    private String period;

    @ApiModelProperty("费用说明")
    private String expenseDetail;

    @ApiModelProperty("费用报销总金额")
    private BigDecimal amount;

    @ApiModelProperty("费用报销总金额")
    private List<InvoicePrintVo> invoiceVos;

}
