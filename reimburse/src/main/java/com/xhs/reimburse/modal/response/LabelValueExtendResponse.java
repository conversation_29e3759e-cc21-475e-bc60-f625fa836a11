package com.xhs.reimburse.modal.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> jiang<PERSON>xiang
 * @version : 1.0
 * @Description : Description
 * @date ：Created in 2025/5/14 20:32
 */
@Data
public class LabelValueExtendResponse {

    @ApiModelProperty("展示内容")
    private String label;

    @ApiModelProperty("提交值")
    private String value;

    @ApiModelProperty("描述")
    private String description = "";

    @ApiModelProperty("子集")
    private List<LabelValueExtendResponse> children;

    @ApiModelProperty("有效性true有效false失效")
    private Boolean validFlag;

}
