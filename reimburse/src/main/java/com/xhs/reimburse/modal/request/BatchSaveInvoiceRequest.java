package com.xhs.reimburse.modal.request;

import com.xhs.reimburse.modal.dto.InvoiceDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName BatchSaveInvoiceRequest.java
 * @createTime 2025年02月13日 19:09:00
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BatchSaveInvoiceRequest {

    @ApiModelProperty("需要保存的发票List")
    private List<InvoiceDto> invoiceDtoList;

    private String userId;

    private Integer invoiceSource;

}
