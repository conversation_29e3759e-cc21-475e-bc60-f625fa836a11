package com.xhs.reimburse.modal.response;

import com.xhs.reimburse.modal.dto.InvoiceDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName BatchOcrParseResponse.java
 * @createTime 2025年02月13日 17:19:00
 */
@Data
public class BatchOcrParseResponse {

    @ApiModelProperty("OCR解析成功的发票")
    private List<InvoiceDto> ocrParseSuccessList;

    @ApiModelProperty("OCR解析失败的报销单")
    private List<InvoiceDto> ocrParseFailureList;

    @ApiModelProperty("验证重复的报销单")
    private List<InvoiceDto> ocrParseDuplicateList;
}
