package com.xhs.reimburse.modal.entity.travel;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Table: travel_standard
 * <AUTHOR>
 */
@Data
public class TravelStandard {
    /**
     * Column: id
     * Type: BIGINT UNSIGNED
     * Remark: id
     */
    private Long id;

    /**
     * Column: subject
     * Type: VARCHAR(64)
     * Remark: 科目
     */
    private String subject;

    /**
     * Column: city_id
     * Type: VARCHAR(128)
     * Remark: 城市id
     */
    private String cityId;

    /**
     * Column: city_name
     * Type: VARCHAR(128)
     * Remark: 城市名称
     */
    private String cityName;

    /**
     * Column: country_id
     * Type: VARCHAR(64)
     * Remark: 国家id
     */
    private String countryId;

    /**
     * Column: country_name
     * Type: VARCHAR(64)
     * Remark: 国家名称
     */
    private String countryName;

    /**
     * Column: time_type
     * Type: VARCHAR(64)
     * Remark: 出差时间 1：<15 ;2:16-90;3:91
     */
    private String timeType;

    /**
     * Column: is_together
     * Type: VARCHAR(64)
     * Remark: 是否合住 1：合住 0：不合住
     */
    private String isTogether;

    /**
     * Column: currency
     * Type: VARCHAR(64)
     * Remark: 币种
     */
    private String currency;

    /**
     * Column: currency_name
     * Type: VARCHAR(64)
     * Remark: 币种名称
     */
    private String currencyName;

    /**
     * Column: amount
     * Type: DECIMAL
     * Default value: 0.00
     * Remark: 标准金额
     */
    private BigDecimal amount;

    /**
     * Column: is_valid
     * Type: TINYINT(3)
     * Default value: 0
     * Remark: 是否生效 0：无效 1：有效
     */
    private Byte isValid;

    /**
     * Column: creator_no
     * Type: VARCHAR(64)
     * Remark: 创建人id
     */
    private String creatorNo;

    /**
     * Column: create_time
     * Type: DATETIME
     * Default value: CURRENT_TIMESTAMP
     * Remark: 创建时间
     */
    private Date createTime;

    /**
     * Column: updater_no
     * Type: VARCHAR(64)
     * Remark: 更新人id
     */
    private String updaterNo;

    /**
     * Column: update_time
     * Type: DATETIME
     * Default value: CURRENT_TIMESTAMP
     * Remark: 更新时间
     */
    private Date updateTime;
}