package com.xhs.reimburse.modal.entity;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EhrDepartmentEntity {

    //required
    private long departmentId;

    //required
    private long parentId;

    //required
    private String departmentCode;

    //required
    private String departmentName;

    //required
    private int level;

    //required
    private int isLeaf;

    //required
    private String departmentStatus;

    //required
    private String departmentIdPath;

    //required
    private String departmentNamePath;

    //required
    private String departmentLeader;

    //required
    private long firstLevelDepartmentId;

    //required
    private long secondLevelDepartmentId;

    //required
    private String hrbp;

    //required
    private String hrbpLeader;

    //required
    private String salaryLeader;

    //required
    private String hrManager;

    //required
    private int isValid;

    //required
    private String validTime;

    public List<Long> buildDepartmentIdList() {
        return Arrays.stream(this.getDepartmentIdPath().split(",")).map(Long::parseLong).collect(Collectors.toList());
    }

}
