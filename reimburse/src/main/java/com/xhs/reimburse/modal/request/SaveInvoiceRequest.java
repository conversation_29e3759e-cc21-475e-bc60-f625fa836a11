package com.xhs.reimburse.modal.request;

import com.xhs.reimburse.modal.dto.InvoiceDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> jiang<PERSON>xiang
 * @version : 1.0
 * @Description : Description
 * @date ：Created in 2025/5/20 14:38
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SaveInvoiceRequest {

    @ApiModelProperty("需要保存的发票List")
    private List<InvoiceDto> invoiceDtoList;
}
