package com.xhs.reimburse.modal.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date :2025/03/28 - 下午6:30
 * @description :
 */
@Data
public class InvoiceQueryRuleDto {
    // 用户ID
    String userId;

    // 金额
    String amount;

    // 金额差值
    String amountDiff;

    // 开始时间
    String startTime;

    // 结束时间
    String endTime;

    // 发票状态列表
    List<Integer> invoiceStatusList;

    // 发票-费用状态列表
    List<Integer> invoiceExpenseStatusList;
}