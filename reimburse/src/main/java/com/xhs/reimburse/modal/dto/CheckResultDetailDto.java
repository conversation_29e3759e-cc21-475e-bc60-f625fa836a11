package com.xhs.reimburse.modal.dto;

import com.xhs.reimburse.enums.ReimbursementFormCheckItemEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date :2025/07/08 - 下午5:01
 * @description :校验结果详情
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CheckResultDetailDto {
    /**
     * 校验项code
     * @see ReimbursementFormCheckItemEnum
     */
    private String checkItemCode;

    /**
     * 校验项名称
     * @see ReimbursementFormCheckItemEnum
     */
    private String checkItemName;

    /**
     * 是否阻碍提报
     * true: 阻碍提报
     * false: 不阻碍提报
     */
    @Builder.Default
    private Boolean blockSubmit = false;

    // 阻碍项列表
    @Builder.Default
    private List<BlockItemDto> blockItemList = new ArrayList<>();
}
