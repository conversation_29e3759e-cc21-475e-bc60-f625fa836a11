package com.xhs.reimburse.modal.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ReimbursePrintBasicInfo {

    @ApiModelProperty(notes = "选择的付款公司")
    private String paymentCompanyName;

    @ApiModelProperty(notes = "选择的付款主体编码")
    private String paymentCompanyCode;

    @ApiModelProperty(notes = "收款银行名称")
    private String gatheringBank;

    @ApiModelProperty(notes = "收款银行联行号")
    private String gatheringBankCode;

    @ApiModelProperty(notes = "收款账号")
    private String gatheringAccount;

    @ApiModelProperty(notes = "收款账户名称")
    private String gatheringName;

    @ApiModelProperty(notes = "当前用户所在部门ID")
    private String departmentId;

    @ApiModelProperty(notes = "当前用户所在部门名称")
    private String departmentName;

    @ApiModelProperty(notes = "当前用户所在部门ID路径")
    private List<String> departmentIdPath;

    @ApiModelProperty(notes = "当前用户所在部门名称路径")
    private List<String> departmentNamePath;

    @ApiModelProperty(notes = "选择的预算归属部门ID")
    private String budgetDepartmentId;

    @ApiModelProperty(notes = "选择的预算归属部门名称")
    private String budgetDepartmentName;

    @ApiModelProperty(notes = "选择的预算归属部门ID路径")
    private List<String> budgetDepartmentNamePath;

    @ApiModelProperty(notes = "选择的预算归属部门名称路径")
    private List<String> budgetDepartmentIdPath;

    @ApiModelProperty(notes = "付款状态")
    private String payStatusDes;

}
