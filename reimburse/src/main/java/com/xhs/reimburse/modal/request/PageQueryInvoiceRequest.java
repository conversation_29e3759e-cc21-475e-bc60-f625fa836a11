package com.xhs.reimburse.modal.request;

import com.xhs.finance.framework.page.PageQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName PageQueryInvoiceRequest.java
 * @createTime 2025年02月11日 17:41:00
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PageQueryInvoiceRequest extends PageQuery {

    @NotNull(message = "发票夹状态不能为空")
    @ApiModelProperty("发票夹-待处理:1 发票夹-已处理:2 添加票据-发票夹导入: 3")
    private Integer invoiceStatus;

    @ApiModelProperty("返回所有发票:false 返回验真成功的发票: true")
    private Boolean filterByInvoiceValidate = false;
}
