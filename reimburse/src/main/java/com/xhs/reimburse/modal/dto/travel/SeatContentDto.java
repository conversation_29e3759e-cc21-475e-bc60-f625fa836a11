package com.xhs.reimburse.modal.dto.travel;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName SeatContentDto.java
 * @createTime 2025年03月22日 14:52:00
 */
@Data
public class SeatContentDto {

    @ApiModelProperty("到达地")
    private String endCity;

    @ApiModelProperty("出发地")
    private String startCity;

    @ApiModelProperty("座位类型")
    private String seatType;

    @ApiModelProperty("校验结果")
    private Boolean isOverBudget = false;

    @ApiModelProperty("校验类型 train:火车票 air:飞机票")
    private String checkType;

    @ApiModelProperty("发票uuId")
    private String invoiceUuid;
}

