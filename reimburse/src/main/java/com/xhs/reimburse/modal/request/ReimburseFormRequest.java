package com.xhs.reimburse.modal.request;

import com.xhs.oa.office.utils.AssertHelper;
import com.xhs.reimburse.modal.dto.ExpenseDto;
import com.xhs.reimburse.modal.dto.FileInfoDto;
import com.xhs.reimburse.modal.dto.PaymentInfoDto;
import com.xhs.reimburse.modal.dto.ReimburseBasicInfo;
import com.xhs.reimburse.modal.entity.EmployeeEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ReimburseFormRequest {

    @ApiModelProperty(notes = "报销表单主键")
    private Long id;

    @ApiModelProperty(notes = "金额")
    private BigDecimal amount;

    @ApiModelProperty(notes = "单据号")
    private String formNum;

    @ApiModelProperty(notes = "uuid")
    private String uuid;

    @NotBlank(message = "当前单据类型不能为空")
    @ApiModelProperty(notes = "单据类型")
    private String formType;

    @ApiModelProperty(notes = "费用编号")
    private List<String> expenseNos;

    @ApiModelProperty(notes = "差旅申请编号")
    private List<String> travelApplyFormNums;

    @ApiModelProperty(notes = "基础报销信息")
    private ReimburseBasicInfo reimburseBasicInfoVo;

    @ApiModelProperty(notes = "附件")
    private List<FileInfoDto> fileAttachmentList;

    @ApiModelProperty(notes = "创建人员工编号")
    private String creatorNo;

    //收款账户对私 PRIVATE 对私 PUBLIC 对公
    private String accountType = "PRIVATE";

    //收款账户属性 0境内 1境外
    private Integer paymentType = 0;

    //默认币种 CNY
    private String currency = "CNY";

    //************************************预补充信息************************************

    //是否需要打印
    private Boolean needPrint;

    //费用
    private List<ExpenseDto> expenses;

    //创建人信息
    private EmployeeEntity creatorInfo;

    //线上付款信息
    private PaymentInfoDto paymentInfo;

    public void fieldsCommonCheck4Submit() {
        AssertHelper.notEmpty(expenseNos, "费用编号为空");
        reimburseBasicInfoVo.fieldsCheck4Submit();
    }

    public String generatedUuid() {
        uuid = UUID.randomUUID().toString().replaceAll("-", "");
        return uuid;
    }
}


