package com.xhs.reimburse.modal.request;

import com.xhs.finance.framework.page.PageQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * @ClassName PageQueryExpenseRequest.java
 * <AUTHOR>
 * @version 1.0.0
 * @createTime 2025年02月14日 10:24:00
 */
@Data
@EqualsAndHashCode(callSuper=false)
public class PageQueryExpenseRequest extends PageQuery {

    @NotNull(message = "费用夹状态不能为空")
    @ApiModelProperty("费用夹-待处理:1 费用夹-已处理:2 费用明细-从费用夹导入:3")
    private Integer expenseStatus;
}
