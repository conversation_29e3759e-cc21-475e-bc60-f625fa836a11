package com.xhs.reimburse.modal.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


/**
 * <AUTHOR>
 */
@Data
public class EmployeeEntity {

    @ApiModelProperty(notes = "提交人所在公司")
    private String company;

    @ApiModelProperty(notes = "提交人工作地")
    private String workplace;

    @ApiModelProperty(notes = "提交人工作地编码")
    private String workplaceCode;

    @ApiModelProperty(notes = "创建人员工编号")
    private String userId;

    @ApiModelProperty(notes = "用户名")
    private String userName;

    @ApiModelProperty(notes = "邮箱")
    private String userEmail;

    @ApiModelProperty(notes = "邮箱")
    private String departmentId;

    @ApiModelProperty(notes = "员工类型")
    private int employeeType;

    @ApiModelProperty(notes = "部门名称")
    private String departmentName;

    @ApiModelProperty(notes = "部门路径")
    private List<String> departmentIdPath;

    @ApiModelProperty(notes = "部门名称路径")
    private List<String> departmentNamePath;

}