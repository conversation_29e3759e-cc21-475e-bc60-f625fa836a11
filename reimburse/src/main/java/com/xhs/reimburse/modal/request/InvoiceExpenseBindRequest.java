package com.xhs.reimburse.modal.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 发票与费用类型绑定请求
 */
@Data
public class InvoiceExpenseBindRequest {

    @ApiModelProperty(value = "会话ID", required = true)
    @NotBlank(message = "会话ID不能为空")
    private String sessionId;

    @ApiModelProperty(value = "场景标识", required = true)
    @NotBlank(message = "场景标识不能为空")
    private String sceneId;

    @ApiModelProperty(value = "发票与费用类型绑定列表", required = true)
    @NotEmpty(message = "绑定列表不能为空")
    @Valid
    private List<InvoiceExpenseBind> invoiceExpenseBindList;

    /**
     * 发票与费用类型绑定信息
     */
    @Data
    public static class InvoiceExpenseBind {
        
        @ApiModelProperty(value = "发票UUID", required = true)
        @NotBlank(message = "发票UUID不能为空")
        private String invoiceUuid;

        @ApiModelProperty(value = "费用类型", required = true)
        @NotBlank(message = "费用类型不能为空")
        private String expenseType;
    }
}
