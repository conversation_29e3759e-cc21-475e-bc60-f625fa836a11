package com.xhs.reimburse.modal.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ReimbursementFormSimpleDto {

    @ApiModelProperty(notes = "单据号吗")
    private String formNum;

    @ApiModelProperty(notes = "报销单UID")
    private String uuid;

    @ApiModelProperty(notes = "报销类型")
    private String formType;

    @ApiModelProperty(notes = "报销类型")
    private String formTypeName;

    @ApiModelProperty(notes = "单据创建时间")
    private String createTime;

    @ApiModelProperty(notes = "单据首次提交时间")
    private String submitTime;

    @ApiModelProperty(notes = "单据金额")
    private BigDecimal amount;

    @ApiModelProperty(notes = "报销单当前状态")
    private String formStatus;

    @ApiModelProperty(notes = "报销单当前状态")
    private Integer formStatusCode;
}
