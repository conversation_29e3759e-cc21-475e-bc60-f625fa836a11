package com.xhs.reimburse.modal.request.travel;

import com.xhs.reimburse.modal.dto.travel.ExpenseContentDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ExpenseBudgetCheckRequest.java
 * @createTime 2025年03月21日 10:22:00
 */
@Data
public class ExpenseBudgetCheckRequest {
    @ApiModelProperty("报销类型")
    private String formType;

    @ApiModelProperty("一级科目")
    private String firstSubject;

    @ApiModelProperty("二级科目")
    private String secondSubject;

    @ApiModelProperty("费用信息")
    private ExpenseContentDto expenseContentDto;
}
