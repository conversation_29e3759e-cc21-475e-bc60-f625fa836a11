package com.xhs.reimburse.modal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.models.auth.In;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @date :2025/02/08 - 下午5:28
 * @description :
 */
@Data
@TableName("expense")
public class ExpenseEntity {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("uuid")
    private String uuid;

    @TableField("form_type")
    private String formType;

    @TableField("first_subject")
    private String firstSubject;

    @TableField("second_subject")
    private String secondSubject;

    @TableField("details")
    private String details;

    @TableField("creator_user_id")
    private String creatorUserId;

    @TableField("create_time")
    private Date createTime;

    @TableField("update_time")
    private Date updateTime;

    @TableField("status")
    private Integer status;

    @TableField("expense_status")
    private Integer expenseStatus;

    @TableField("expense_source")
    private Integer expenseSource;

    @TableField("beyond_standard_info")
    private String beyondStandardInfo;

    @TableField("exception_details")
    private String exceptionDetails;

    @TableField("expense_invoice_status")
    private Integer expenseInvoiceStatus;

    @TableField("expense_form_status")
    private Integer expenseFormStatus;

    @TableField("travel_meal_over_limit_reason")
    private String travelMealOverLimitReason;

    @TableField("relation_invoice_start_time")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date relationInvoiceStartTime;

    @TableField("relation_invoice_end_time")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date relationInvoiceEndTime;
}
