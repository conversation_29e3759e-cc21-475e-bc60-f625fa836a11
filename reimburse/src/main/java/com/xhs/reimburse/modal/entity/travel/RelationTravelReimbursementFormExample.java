package com.xhs.reimburse.modal.entity.travel;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class RelationTravelReimbursementFormExample {
    /**
     * @mbg.generated generated automatically, do not modify!
     */
    protected String orderByClause;

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    protected boolean distinct;

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    protected List<Criteria> oredCriteria;

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    public RelationTravelReimbursementFormExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTravelFormNumIsNull() {
            addCriterion("travel_form_num is null");
            return (Criteria) this;
        }

        public Criteria andTravelFormNumIsNotNull() {
            addCriterion("travel_form_num is not null");
            return (Criteria) this;
        }

        public Criteria andTravelFormNumEqualTo(String value) {
            addCriterion("travel_form_num =", value, "travelFormNum");
            return (Criteria) this;
        }

        public Criteria andTravelFormNumNotEqualTo(String value) {
            addCriterion("travel_form_num <>", value, "travelFormNum");
            return (Criteria) this;
        }

        public Criteria andTravelFormNumGreaterThan(String value) {
            addCriterion("travel_form_num >", value, "travelFormNum");
            return (Criteria) this;
        }

        public Criteria andTravelFormNumGreaterThanOrEqualTo(String value) {
            addCriterion("travel_form_num >=", value, "travelFormNum");
            return (Criteria) this;
        }

        public Criteria andTravelFormNumLessThan(String value) {
            addCriterion("travel_form_num <", value, "travelFormNum");
            return (Criteria) this;
        }

        public Criteria andTravelFormNumLessThanOrEqualTo(String value) {
            addCriterion("travel_form_num <=", value, "travelFormNum");
            return (Criteria) this;
        }

        public Criteria andTravelFormNumLike(String value) {
            addCriterion("travel_form_num like", value, "travelFormNum");
            return (Criteria) this;
        }

        public Criteria andTravelFormNumNotLike(String value) {
            addCriterion("travel_form_num not like", value, "travelFormNum");
            return (Criteria) this;
        }

        public Criteria andTravelFormNumIn(List<String> values) {
            addCriterion("travel_form_num in", values, "travelFormNum");
            return (Criteria) this;
        }

        public Criteria andTravelFormNumNotIn(List<String> values) {
            addCriterion("travel_form_num not in", values, "travelFormNum");
            return (Criteria) this;
        }

        public Criteria andTravelFormNumBetween(String value1, String value2) {
            addCriterion("travel_form_num between", value1, value2, "travelFormNum");
            return (Criteria) this;
        }

        public Criteria andTravelFormNumNotBetween(String value1, String value2) {
            addCriterion("travel_form_num not between", value1, value2, "travelFormNum");
            return (Criteria) this;
        }

        public Criteria andReimbursementIdIsNull() {
            addCriterion("reimbursement_id is null");
            return (Criteria) this;
        }

        public Criteria andReimbursementIdIsNotNull() {
            addCriterion("reimbursement_id is not null");
            return (Criteria) this;
        }

        public Criteria andReimbursementIdEqualTo(String value) {
            addCriterion("reimbursement_id =", value, "reimbursementId");
            return (Criteria) this;
        }

        public Criteria andReimbursementIdNotEqualTo(String value) {
            addCriterion("reimbursement_id <>", value, "reimbursementId");
            return (Criteria) this;
        }

        public Criteria andReimbursementIdGreaterThan(String value) {
            addCriterion("reimbursement_id >", value, "reimbursementId");
            return (Criteria) this;
        }

        public Criteria andReimbursementIdGreaterThanOrEqualTo(String value) {
            addCriterion("reimbursement_id >=", value, "reimbursementId");
            return (Criteria) this;
        }

        public Criteria andReimbursementIdLessThan(String value) {
            addCriterion("reimbursement_id <", value, "reimbursementId");
            return (Criteria) this;
        }

        public Criteria andReimbursementIdLessThanOrEqualTo(String value) {
            addCriterion("reimbursement_id <=", value, "reimbursementId");
            return (Criteria) this;
        }

        public Criteria andReimbursementIdLike(String value) {
            addCriterion("reimbursement_id like", value, "reimbursementId");
            return (Criteria) this;
        }

        public Criteria andReimbursementIdNotLike(String value) {
            addCriterion("reimbursement_id not like", value, "reimbursementId");
            return (Criteria) this;
        }

        public Criteria andReimbursementIdIn(List<String> values) {
            addCriterion("reimbursement_id in", values, "reimbursementId");
            return (Criteria) this;
        }

        public Criteria andReimbursementIdNotIn(List<String> values) {
            addCriterion("reimbursement_id not in", values, "reimbursementId");
            return (Criteria) this;
        }

        public Criteria andReimbursementIdBetween(String value1, String value2) {
            addCriterion("reimbursement_id between", value1, value2, "reimbursementId");
            return (Criteria) this;
        }

        public Criteria andReimbursementIdNotBetween(String value1, String value2) {
            addCriterion("reimbursement_id not between", value1, value2, "reimbursementId");
            return (Criteria) this;
        }

        public Criteria andIsValidIsNull() {
            addCriterion("is_valid is null");
            return (Criteria) this;
        }

        public Criteria andIsValidIsNotNull() {
            addCriterion("is_valid is not null");
            return (Criteria) this;
        }

        public Criteria andIsValidEqualTo(Byte value) {
            addCriterion("is_valid =", value, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidNotEqualTo(Byte value) {
            addCriterion("is_valid <>", value, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidGreaterThan(Byte value) {
            addCriterion("is_valid >", value, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidGreaterThanOrEqualTo(Byte value) {
            addCriterion("is_valid >=", value, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidLessThan(Byte value) {
            addCriterion("is_valid <", value, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidLessThanOrEqualTo(Byte value) {
            addCriterion("is_valid <=", value, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidIn(List<Byte> values) {
            addCriterion("is_valid in", values, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidNotIn(List<Byte> values) {
            addCriterion("is_valid not in", values, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidBetween(Byte value1, Byte value2) {
            addCriterion("is_valid between", value1, value2, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidNotBetween(Byte value1, Byte value2) {
            addCriterion("is_valid not between", value1, value2, "isValid");
            return (Criteria) this;
        }

        public Criteria andCreatorNoIsNull() {
            addCriterion("creator_no is null");
            return (Criteria) this;
        }

        public Criteria andCreatorNoIsNotNull() {
            addCriterion("creator_no is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorNoEqualTo(String value) {
            addCriterion("creator_no =", value, "creatorNo");
            return (Criteria) this;
        }

        public Criteria andCreatorNoNotEqualTo(String value) {
            addCriterion("creator_no <>", value, "creatorNo");
            return (Criteria) this;
        }

        public Criteria andCreatorNoGreaterThan(String value) {
            addCriterion("creator_no >", value, "creatorNo");
            return (Criteria) this;
        }

        public Criteria andCreatorNoGreaterThanOrEqualTo(String value) {
            addCriterion("creator_no >=", value, "creatorNo");
            return (Criteria) this;
        }

        public Criteria andCreatorNoLessThan(String value) {
            addCriterion("creator_no <", value, "creatorNo");
            return (Criteria) this;
        }

        public Criteria andCreatorNoLessThanOrEqualTo(String value) {
            addCriterion("creator_no <=", value, "creatorNo");
            return (Criteria) this;
        }

        public Criteria andCreatorNoLike(String value) {
            addCriterion("creator_no like", value, "creatorNo");
            return (Criteria) this;
        }

        public Criteria andCreatorNoNotLike(String value) {
            addCriterion("creator_no not like", value, "creatorNo");
            return (Criteria) this;
        }

        public Criteria andCreatorNoIn(List<String> values) {
            addCriterion("creator_no in", values, "creatorNo");
            return (Criteria) this;
        }

        public Criteria andCreatorNoNotIn(List<String> values) {
            addCriterion("creator_no not in", values, "creatorNo");
            return (Criteria) this;
        }

        public Criteria andCreatorNoBetween(String value1, String value2) {
            addCriterion("creator_no between", value1, value2, "creatorNo");
            return (Criteria) this;
        }

        public Criteria andCreatorNoNotBetween(String value1, String value2) {
            addCriterion("creator_no not between", value1, value2, "creatorNo");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdaterNoIsNull() {
            addCriterion("updater_no is null");
            return (Criteria) this;
        }

        public Criteria andUpdaterNoIsNotNull() {
            addCriterion("updater_no is not null");
            return (Criteria) this;
        }

        public Criteria andUpdaterNoEqualTo(String value) {
            addCriterion("updater_no =", value, "updaterNo");
            return (Criteria) this;
        }

        public Criteria andUpdaterNoNotEqualTo(String value) {
            addCriterion("updater_no <>", value, "updaterNo");
            return (Criteria) this;
        }

        public Criteria andUpdaterNoGreaterThan(String value) {
            addCriterion("updater_no >", value, "updaterNo");
            return (Criteria) this;
        }

        public Criteria andUpdaterNoGreaterThanOrEqualTo(String value) {
            addCriterion("updater_no >=", value, "updaterNo");
            return (Criteria) this;
        }

        public Criteria andUpdaterNoLessThan(String value) {
            addCriterion("updater_no <", value, "updaterNo");
            return (Criteria) this;
        }

        public Criteria andUpdaterNoLessThanOrEqualTo(String value) {
            addCriterion("updater_no <=", value, "updaterNo");
            return (Criteria) this;
        }

        public Criteria andUpdaterNoLike(String value) {
            addCriterion("updater_no like", value, "updaterNo");
            return (Criteria) this;
        }

        public Criteria andUpdaterNoNotLike(String value) {
            addCriterion("updater_no not like", value, "updaterNo");
            return (Criteria) this;
        }

        public Criteria andUpdaterNoIn(List<String> values) {
            addCriterion("updater_no in", values, "updaterNo");
            return (Criteria) this;
        }

        public Criteria andUpdaterNoNotIn(List<String> values) {
            addCriterion("updater_no not in", values, "updaterNo");
            return (Criteria) this;
        }

        public Criteria andUpdaterNoBetween(String value1, String value2) {
            addCriterion("updater_no between", value1, value2, "updaterNo");
            return (Criteria) this;
        }

        public Criteria andUpdaterNoNotBetween(String value1, String value2) {
            addCriterion("updater_no not between", value1, value2, "updaterNo");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}