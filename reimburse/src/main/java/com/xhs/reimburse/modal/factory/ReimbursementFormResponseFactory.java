package com.xhs.reimburse.modal.factory;


import com.xhs.oa.office.utils.AssertHelper;
import com.xhs.reimburse.enums.ReimburseTypeEnum;
import com.xhs.reimburse.modal.response.*;

/**
 * <AUTHOR>
 * @desc 报销单简单工厂
 */
public class ReimbursementFormResponseFactory {

    /**
     * 构建表单详情
     *
     * @param formType 单据类型
     * @return 表单详情resp
     */
    public static ReimbursementFormResponse createResponse(String formType) {

        ReimburseTypeEnum reimburseTypeEnum = ReimburseTypeEnum.getEnumByType(formType);
        AssertHelper.notNull(reimburseTypeEnum, "报销单类型不存在");

        switch (reimburseTypeEnum) {
            case YBFYBXD:
                return new CommonFormResponse();
            case CLBXD:
                return new TravelFormResponse();
            default:
                return new ReimbursementFormResponse();
        }
    }

    /**
     * 构建表单打印详情
     *
     * @param formType 单据类型
     * @return 表单打印详情resp
     */
    public static ReimbursementFormPrintResponse createPrintResponse(String formType) {

        ReimburseTypeEnum reimburseTypeEnum = ReimburseTypeEnum.getEnumByType(formType);
        AssertHelper.notNull(reimburseTypeEnum, "报销单类型不存在");

        switch (reimburseTypeEnum) {
            case YBFYBXD:
                return new CommonFormPrintResponse();
            case CLBXD:
                return new TravelFormPrintResponse();
            default:
                return new ReimbursementFormPrintResponse();
        }
    }
}
