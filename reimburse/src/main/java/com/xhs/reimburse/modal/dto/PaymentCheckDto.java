package com.xhs.reimburse.modal.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class PaymentCheckDto {
    private String subject;
    private BigDecimal amount;
    private String paymentCurrency;
    private String paymentAbstract;
    private String gatheringName;
    private String paymentNo;
    private String gatheringAccount;
    private String bankName;
    private String accountType;
    private int paymentType;
    private String bankCode;
    private String hwBankCodeType;
    private String hwBankCode;
}
