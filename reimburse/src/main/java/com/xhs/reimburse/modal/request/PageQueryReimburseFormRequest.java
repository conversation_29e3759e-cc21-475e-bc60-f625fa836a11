package com.xhs.reimburse.modal.request;

import com.xhs.finance.framework.page.PageQuery;
import com.xhs.reimburse.enums.ReimbursementFormStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PageQueryReimburseFormRequest extends PageQuery {

    @ApiModelProperty("单据类型")
    private String formType;

    @ApiModelProperty("查询状态")
    private String status;

    //status转换成DB的状态枚举 用于DB状态查询！
    public List<Integer> getTransferStatus() {
        return ReimbursementFormStatusEnum.getCodeByQueryStatus(status);
    }

    //当前用户ID
    private String userId;
}
