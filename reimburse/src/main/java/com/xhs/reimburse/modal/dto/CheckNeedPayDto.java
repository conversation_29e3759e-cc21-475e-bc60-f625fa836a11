package com.xhs.reimburse.modal.dto;

import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Data
public class CheckNeedPayDto {

    /*
        required 单据类型
        */
    private String formType;

    /*
    required 付款公司主体
     */
    private List<String> companyNameList;

    /*
       收款公司 境内外类型
     */
    private Set<Integer> paymentTypeList;

    /*
     optional 对公还是对私
     */
    private String accountType;

}
