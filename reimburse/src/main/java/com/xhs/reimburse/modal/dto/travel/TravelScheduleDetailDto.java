package com.xhs.reimburse.modal.dto.travel;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TravelScheduleDetailDto {

    //行程类型 住宿、交通
    @NotBlank
    private String scheduleType;

    //行程子类型 交通：飞机、火车、汽车、其他   ，住宿：酒店
    private List<String> scheduleSubTypes;

    //目的地
    @NotEmpty
    private List<TravelPlaceDto> arrTripPlaces;

    //出发地
    private List<TravelPlaceDto> depTripPlaces;

    //行程开始时间 yyyy-MM-dd
    @NotBlank
    private String startDate;

    //行程结束时间 yyyy-MM-dd
    @NotBlank
    private String endDate;

    //是否是往返 true:往返 false:单程
    private Boolean roundWay;

    //有无同住人 true:有同住人 false:无
    private Boolean shared;

    //同住人
    private List<TravelerDto> sharedTravelers;

    //携程侧行程标准
    private String rankStandard;

    //行程展示差标金额，非住宿行程为空，币种：CNY
    private String showStandardAmount;

}
