package com.xhs.reimburse.modal.entity;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.TableName;
import com.xhs.reimburse.constant.CommonConstant;
import com.xhs.reimburse.enums.ReimbursementFormStatusEnum;
import lombok.Data;

/**
 * Table: reimbursement_form
 */
@Data
@TableName("reimbursement_form")
public class ReimbursementFormEntity {
    /**
     * Column: id
     * Type: BIGINT
     */
    private Long id;

    /**
     * Column: uuid
     * Type: VARCHAR(128)
     * Remark: 报销单唯一ID
     */
    private String uuid;

    /**
     * Column: form_num
     * Type: VARCHAR(200)
     * Remark: 单据编号
     */
    private String formNum;

    /**
     * Column: form_type
     * Type: VARCHAR(200)
     * Remark: 单据类型
     */
    private String formType;

    /**
     * Column: amount
     * Type: DECIMAL
     * Default value: 0.00
     * Remark: 人民币金额
     */
    private BigDecimal amount;

    /**
     * Column: checked_status
     * Type: INT
     * Default value: 1
     * Remark: 表单检查状态
     */
    private Integer checkStatus;

    /**
     * Column: reimburse_status
     * Type: INT
     * Default value: 1
     * Remark: 报销单状态
     */
    private Integer reimburseStatus;

    /**
     * Column: submit_time
     * Type: DATETIME
     * Remark: 单据首次提交时间
     */
    private Date submitTime;

    /**
     * Column: remark
     * Type: VARCHAR(2000)
     * Remark: 备注
     */
    private String remark;

    /**
     * Column: is_valid
     * Type: INT
     * Default value: 1
     * Remark: 是否有效 0：无效 1：有效
     */
    private Integer isValid;

    /**
     * Column: creator_no
     * Type: VARCHAR(64)
     * Remark: 创建人ID
     */
    private String creatorNo;

    /**
     * Column: creator
     * Type: VARCHAR(45)
     * Remark: 创建人名称
     */
    private String creator;

    /**
     * Column: updater_no
     * Type: VARCHAR(64)
     * Remark: 更新人ID
     */
    private String updaterNo;

    /**
     * Column: updater
     * Type: VARCHAR(45)
     * Remark: 更新人名称
     */
    private String updater;

    /**
     * Column: create_time
     * Type: DATETIME
     * Default value: CURRENT_TIMESTAMP
     * Remark: 创建时间
     */
    private Date createTime;

    /**
     * Column: update_time
     * Type: DATETIME
     * Default value: CURRENT_TIMESTAMP
     * Remark: 更新时间
     */
    private Date updateTime;

    /**
     * Column: form_content
     * Type: TEXT
     * Remark: 表单内容
     */
    private String formContent;

    /**
     * 是否在查询时再次表单可提交检查
     *
     * @return true 需要再次表单可提交检查
     */
    public boolean needBaseCheck() {
        //草稿、驳回、撤回
        List<Integer> status = Arrays.asList(ReimbursementFormStatusEnum.CG.getCode()
                , ReimbursementFormStatusEnum.BH.getCode(), ReimbursementFormStatusEnum.CH.getCode());

        return status.contains(reimburseStatus) && Integer.valueOf(CommonConstant.VALID).equals(this.isValid);
    }
}