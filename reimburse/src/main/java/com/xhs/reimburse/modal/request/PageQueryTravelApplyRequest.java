package com.xhs.reimburse.modal.request;

import com.xhs.finance.framework.page.PageQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;


/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class PageQueryTravelApplyRequest extends PageQuery {

    @ApiModelProperty("查询对象id")
    private String userId;

    @ApiModelProperty("差旅申请单")
    private List<String> formNums;

    @ApiModelProperty("城市搜索关键字")
    private String keyWord;

    @ApiModelProperty("一级科目")
    private String subject;

    @ApiModelProperty("二级科目")
    private String secondSubject;

    @ApiModelProperty("开始时间")
    private String startDateTime;

    @ApiModelProperty("结束时间")
    private String endDateTime;

    @ApiModelProperty("审批状态")
    private List<Integer> auditStatus;

    public PageQueryTravelApplyRequest(List<String> formNums, String userId, List<Integer> auditStatus) {
        this.formNums = formNums;
        this.userId = userId;
        this.auditStatus = auditStatus;
    }
}
