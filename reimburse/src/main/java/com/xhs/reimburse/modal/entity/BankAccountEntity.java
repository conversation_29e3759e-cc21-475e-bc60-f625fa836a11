package com.xhs.reimburse.modal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date :2025/02/11 - 上午10:38
 * @description : 银行账户
 */
@Data
@TableName("reimburse_user_bank_account")
public class BankAccountEntity {
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("用户ID")
    private String userId;

    @ApiModelProperty("开户银行")
    private String bankName;

    @ApiModelProperty("开户银行联行号")
    private String bankCode;

    @ApiModelProperty("收款人姓名")
    private String accountName;

    @ApiModelProperty(notes = "银行卡号")
    private String accountNo;

    @ApiModelProperty(notes = "是否默认选项")
    private Integer isDefault;

    @ApiModelProperty(notes = "是否生效")
    private Integer isValid;

    @ApiModelProperty(notes = "创建时间")
    private Date createTime;

    @ApiModelProperty(notes = "更新时间")
    private Date updateTime;
}
