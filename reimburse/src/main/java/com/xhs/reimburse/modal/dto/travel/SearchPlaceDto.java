package com.xhs.reimburse.modal.dto.travel;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName SearchPlaceDto.java
 * @createTime 2025年03月21日 15:10:00
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SearchPlaceDto {
    @ApiModelProperty("查询关键词")
    private String keyword;

    @ApiModelProperty("行程类型列表")
    private List<String> routeTypeList;

    /**
     * 差旅类型
     */
    private String placeType;

    /**
     * 地点类型
     */
    private String type;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 国家ID
     */
    private String countryId;

    /**
     * 国家名称
     */
    private String countryName;

    public SearchPlaceDto(String type, String cityName, String countryId) {
        this.type = type;
        this.cityName = cityName;
        this.countryId = countryId;
    }
}
