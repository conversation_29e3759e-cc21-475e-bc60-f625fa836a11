package com.xhs.reimburse.modal.dto.travel;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class TravelApplyFormInfoDto {

    //差旅申请单
    @NotBlank
    private String travelFormNum;

    //出差类别: 短期、长期...
    @NotBlank
    private String travelType;

    //出差事由
    @NotBlank
    private String travelReason;

    //出差相信愿意
    @NotBlank
    private String travelRemark;

    //差旅申请单创建人
    @NotBlank
    private String creatorNo;

    //出行人
    @NotEmpty
    private List<TravelerDto> travelers;

    //差旅行程
    @NotEmpty
    private List<TravelScheduleDetailDto> travelScheduleDetails;

}
