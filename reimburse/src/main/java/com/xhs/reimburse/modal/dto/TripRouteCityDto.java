package com.xhs.reimburse.modal.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TripRouteCityDto.java
 * @createTime 2025年03月13日 10:49:00
 */
@Data
public class TripRouteCityDto {
    @ApiModelProperty(notes = "出发城市")
    private String startCity;
    @ApiModelProperty(notes = "到达城市")
    private String endCity;
    @ApiModelProperty(notes = "座位类型")
    private String seatType;
}
