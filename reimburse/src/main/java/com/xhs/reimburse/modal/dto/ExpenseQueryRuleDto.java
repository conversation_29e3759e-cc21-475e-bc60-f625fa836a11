package com.xhs.reimburse.modal.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date :2025/03/28 - 下午6:30
 * @description :
 */
@Data
public class ExpenseQueryRuleDto {
    // 用户ID
    String userId;

    // 金额
    String amount;

    // 金额差值
    String amountDiff;

    // 开始时间
    String startTime;

    // 结束时间
    String endTime;

    // 费用状态列表
    List<Integer> expenseStatusList;

    // 费用-报销单状态列表
    List<Integer> expenseFormStatusList;
}