package com.xhs.reimburse.listener;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.xhs.reimburse.constant.CommonConstant;
import com.xhs.reimburse.event.InvoiceAmountChangedEvent;
import com.xhs.reimburse.event.InvoiceLogicDeleteEvent;
import com.xhs.reimburse.modal.dto.DynamicFormFieldDto;
import com.xhs.reimburse.modal.dto.ExpenseDto;
import com.xhs.reimburse.modal.dto.InvoiceDto;
import com.xhs.reimburse.service.DynamicFormFieldService;
import com.xhs.reimburse.service.ExpenseService;
import com.xhs.reimburse.service.InvoiceService;
import com.xhs.reimburse.service.RelationExpenseInvoiceService;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

import static com.xhs.reimburse.constant.CommonConstant.EXPENSE_FIELD_ID_CODE_RELATION_INVOICE;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName InvoiceEventListener.java
 * @createTime 2025年04月02日 11:29:00
 */
@Component
public class InvoiceEventListener {
    @Resource
    private ExpenseService expenseService;
    @Resource
    private RelationExpenseInvoiceService relationExpenseInvoiceService;
    @Resource
    private DynamicFormFieldService dynamicFormFieldService;
    @Resource
    private InvoiceService invoiceService;

    public InvoiceEventListener(ExpenseService expenseService) {
        this.expenseService = expenseService;
    }

    @EventListener
    public void handleInvoiceAmountChanged(InvoiceAmountChangedEvent event) {
        String expenseUuid = relationExpenseInvoiceService.queryInvoiceBelongExpense(event.getInvoiceUuid());
        if (StringUtils.isEmpty(expenseUuid)) {
            return;
        }

        ExpenseDto expense = expenseService.getExpenseDtoByUuid(expenseUuid);
        List<String> relationInvoiceUuids = expense.getRelationInvoiceList().stream().map(InvoiceDto::getUuid).collect(Collectors.toList());
        BigDecimal invoicesAmount = invoiceService.calculateInvoicesAmount(relationInvoiceUuids);
        dynamicFormFieldService.updateDynamicFromDefaultValue(expense.getDynamicFormFieldDtoList(), CommonConstant.EXPENSE_FIELD_ID_CODE_AMOUNT, invoicesAmount);
        expenseService.updateExpense(expense);
    }

    @EventListener
    public void handleInvoiceLogicDelete(InvoiceLogicDeleteEvent event) {
        String invoiceUuid = event.getInvoiceUuid();
        List<String> expenseUuids = relationExpenseInvoiceService.queryRelationExpenseUuids(invoiceUuid);
        if (CollectionUtils.isEmpty(expenseUuids)) {
            return;
        }
        ExpenseDto expenseDto = expenseService.getExpenseDtoByUuid(expenseUuids.get(0));
        List<DynamicFormFieldDto> dynamicFormFieldDtoList = expenseDto.getDynamicFormFieldDtoList();
        Object object = dynamicFormFieldService.getDefaultValueObject(dynamicFormFieldDtoList, EXPENSE_FIELD_ID_CODE_RELATION_INVOICE);
        if (dynamicFormFieldService.isValueEmpty(object)) {
            return;
        }

        List<String> relationUuIds = (List<String>) object;
        if (!relationUuIds.contains(invoiceUuid)) {
            return;
        }
        relationUuIds.remove(invoiceUuid);

        // 更新费用关联的发票和总金额
        BigDecimal invoicesAmount = invoiceService.calculateInvoicesAmount(relationUuIds);
        dynamicFormFieldService.updateDynamicFromDefaultValue(dynamicFormFieldDtoList, CommonConstant.EXPENSE_FIELD_ID_CODE_RELATION_INVOICE, relationUuIds);
        dynamicFormFieldService.updateDynamicFromDefaultValue(dynamicFormFieldDtoList, CommonConstant.EXPENSE_FIELD_ID_CODE_AMOUNT, invoicesAmount);
        expenseService.updateExpense(expenseDto);
    }
}
