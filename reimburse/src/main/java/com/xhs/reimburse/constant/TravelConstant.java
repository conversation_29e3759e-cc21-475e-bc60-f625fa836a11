package com.xhs.reimburse.constant;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TravelConstant.java
 * @createTime 2025年03月21日 14:52:00
 */
public class TravelConstant {

    public static final String TRAVEL_APPLY_MANUAL_CONFIG_KEY = "travel_apply_manual_config_key";

    public static final String TYPE_TRAFFIC = "traffic";

    public static final String TYPE_HOTEL = "hotel";

    public static final String TYPE_NAME_TRAFFIC = "交通";

    public static final String TYPE_NAME_HOTEL = "住宿";

    public static final String OVERSEAS = "国外";

    public static final String MISSING_TRAFFIC_MSG = "行程中未包含交通，如需预定机票或报销火车票，请新增交通行程";

    public static final String MISSING_HOTEL_MSG = "行程中未包含住宿，如需预定酒店请新增住宿行程";

    public static final String APPROVAL_WHITE_LIST = "travel_ctrip_approval_white_list";

}
