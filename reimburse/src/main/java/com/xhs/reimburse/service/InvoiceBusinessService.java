package com.xhs.reimburse.service;

import com.xhs.reimburse.modal.dto.InvoiceDto;
import com.xhs.reimburse.modal.entity.ReimbursementFormEntity;

/**
 * <AUTHOR> jiangjixiang
 * @version : 1.0
 * @Description : 发票域为底域的业务服务
 * @date ：Created in 2025/5/20 15:24
 */
public interface InvoiceBusinessService {

    /**
     * @description 根据发票uuid查询报销单
     * <AUTHOR>
     * @param invoiceUuid 发票uuid
     * @return String
     * @date 2025/5/20 15:18
     */
    ReimbursementFormEntity getReimbursementFormNumByInvoiceUuid(String invoiceUuid);


    /**
     * @description 根据发票uuid查询发票-鉴权
     * 未关联报销单：仅本人可查看
     * 关联报销单：follow 流程redFlow的权限
     * <AUTHOR>
     * @param uuid 发票uuid
     * @param needExistMsg true不存在直接返回null，false发票不存在异常校验会抛异常
     * @return InvoiceDto
     * @date 2025/5/20 16:03
     */
    InvoiceDto getInvoiceDtoByUuidCheckAuth(String uuid, boolean needExistMsg);

    /***
     * @description bot入口根据发票uuid查询发票-鉴权
     * 未关联报销单：仅本人可查看
     * 关联报销单：follow 流程redFlow的权限
     * <AUTHOR>
     * @param uuId 发票uuid
     * @param needExistMsg true不存在直接返回null，false发票不存在异常校验会抛异常
     * @return InvoiceDto
     * @date 2025/5/20 16:35
     */
    InvoiceDto getInvoiceForBotByUuidCheckAuth(String uuId, boolean needExistMsg);

    /**
     * @description 查询发票详情（包含已经删除的）-仅单据创建者可调用
     * <AUTHOR>
     * @param uuId 单据id
     * @return InvoiceDto
     * @date 2025/5/21 11:43
     */
    InvoiceDto getInvoiceByIdWithDeleted(String uuId);
}
