package com.xhs.reimburse.service.impl;

import com.xhs.finance.sso.UserInfoBag;
import com.xhs.finance.sso.model.UserInfo;
import com.xhs.oa.office.exception.BusinessException;
import com.xhs.reimburse.service.ReimburseCommonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

import static java.time.format.ResolverStyle.SMART;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ReimburseCommonService.java
 * @createTime 2025年02月18日 11:31:00
 */
@Slf4j
@Service
public class ReimburseCommonServiceImpl implements ReimburseCommonService {

    public String getUserId() {
        UserInfo userInfo = UserInfoBag.get();
        if (userInfo != null) {
            return userInfo.getUserId();
        }
        throw new BusinessException("获取当前用户异常");
    }

    public void setUserId(String userId) {
        UserInfo userInfo = new UserInfo();
        userInfo.setUserId(userId);
        UserInfoBag.cleanAndSet(userInfo);
    }

    public void clean() {
        UserInfoBag.clean();
    }

    public String getUuid() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    public boolean amountFormatIsError(String amount) {
        try {
            Double.parseDouble(amount);
            return false;
        } catch (Exception e) {
            log.warn("amountFormatIsError - amount = {}, e = {}", amount, e.getMessage(), e);
            return true;
        }
    }

    public boolean dateFormatIsError(String date) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd").withResolverStyle(SMART);
            LocalDate.parse(date, formatter);
            return false;
        } catch (Exception e) {
            log.warn("dateFormatIsError - date = {}, e = {}", date, e.getMessage(), e);
            return true;
        }
    }


    /**
     * 时间段在匹配时间之内
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param queryStartTime 查询开始时间
     * @param queryEndTime 查询结束时间
     * @return
     */
    public boolean timeRangeInTimeRange(String startTime, String endTime, String queryStartTime, String queryEndTime) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        LocalDate start = LocalDate.parse(startTime, formatter);
        LocalDate end = LocalDate.parse(endTime, formatter);
        LocalDate matchStart = LocalDate.parse(queryStartTime, formatter);
        LocalDate matchEnd = LocalDate.parse(queryEndTime, formatter);

        return !start.isBefore(matchStart) && !end.isAfter(matchEnd);
    }

    /**
     * 时间点在匹配时间之内
     *
     * @param time 时间点
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return
     */
    public boolean timeInTimeRange(String time, String startTime, String endTime) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        LocalDate dateTime = LocalDate.parse(time, formatter);
        LocalDate start = LocalDate.parse(startTime, formatter);
        LocalDate end = LocalDate.parse(endTime, formatter);

        return !dateTime.isBefore(start) && !dateTime.isAfter(end);
    }
}
