package com.xhs.reimburse.service.external.finance;

import com.alibaba.fastjson2.JSON;
import com.xhs.oa.office.exception.BusinessException;
import com.xhs.oa.office.utils.AssertHelper;
import com.xhs.reimburse.modal.dto.PaymentCheckDto;
import com.xiaohongshu.fls.rpc.finance.cashiercenter.PaymentService;
import com.xiaohongshu.fls.rpc.finance.cashiercenter.model.*;
import com.xiaohongshu.infra.rpc.base.Context;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class PaymentRpcService {

    @Resource
    private PaymentService.Iface paymentService;

    /**
     * 付款申请批量校验的接口
     *
     * @return 失败原因
     */
    public String batchCheckPayments(PaymentCheckDto checkDto) {
        BatchCheckPaymentApplyRequest request = new BatchCheckPaymentApplyRequest();
        try {
            List<PaymentApplyRequest> paymentApplyRequests = new ArrayList<>(2);
            paymentApplyRequests.add(convertToCheckRequests(checkDto));
            request.setBatch_payment_requests(paymentApplyRequests);
            request.setAreaType(checkDto.getPaymentType() > 1 ? AreaType.OVERSEAS : AreaType.DOMESTIC);
            log.info("PaymentRpcService batchCheckPayments req :{}", JSON.toJSONString(request));
            BatchCheckPaymentApplyResponse response = paymentService.batchCheckApplyPayment(new Context(), request);
            AssertHelper.notNull(response, "批量校验付款信息接口异常");
            if (!response.isSuccess()) {
                log.info("PaymentRpcService batchCheckPayments fail :{}", response.getFail_reason());
                if (CollectionUtils.isNotEmpty(response.getError_infos())) {
                    return genResultDesc(response.getError_infos());
                } else {
                    throw new BusinessException("批量校验付款信息失败，失败原因为空");
                }
            }
            List<RequestErrorInfo> errorInfos = response.getError_infos();
            return genResultDesc(errorInfos);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("PaymentRpcService batchCheckPayments error:{}", e.getMessage(), e);
            throw new BusinessException("批量校验付款信息失败");
        }
    }

    /**
     * 封装付款参数
     */
    private PaymentApplyRequest convertToCheckRequests(PaymentCheckDto checkDto) {
        PaymentApplyRequest paymentApplyRequest = new PaymentApplyRequest();
        paymentApplyRequest.setSource_type(SourceType.OA);
        AreaType areaType = checkDto.getPaymentType() > 1 ? AreaType.OVERSEAS : AreaType.DOMESTIC;
        paymentApplyRequest.setAreaType(areaType);
        paymentApplyRequest.setSource_no(checkDto.getPaymentNo());
        paymentApplyRequest.setSubject(checkDto.getSubject());
        paymentApplyRequest.setPayment_amount(checkDto.getAmount().doubleValue());
        paymentApplyRequest.setPayment_currency(checkDto.getPaymentCurrency());
        paymentApplyRequest.setBeneficiary_name(checkDto.getGatheringName());
        paymentApplyRequest.setBeneficiary_account_number(checkDto.getGatheringAccount());
        paymentApplyRequest.setBeneficiary_bank_name(checkDto.getBankName());
        paymentApplyRequest.setPayment_details(checkDto.getPaymentAbstract());
        PaymentApplyExtendRequest paymentApplyExtendRequest = new PaymentApplyExtendRequest();
        PubOrPriType pubOrPriType = PubOrPriType.PRIVATE.toString().equals(checkDto.getAccountType()) ? PubOrPriType.PRIVATE : PubOrPriType.PUBLIC;
        paymentApplyExtendRequest.setPriate_or_public(pubOrPriType);
        // 境内付款
        if (AreaType.DOMESTIC.equals(areaType)) {
            paymentApplyRequest.setBeneficiary_bank_code(checkDto.getBankCode());
            paymentApplyRequest.setBeneficiary_bank_code_type(BankCodeTypeEnum.CNAPS.toString());
        } else {
            paymentApplyRequest.setBeneficiary_bank_code_type(checkDto.getHwBankCodeType());
            paymentApplyRequest.setBeneficiary_bank_code(checkDto.getHwBankCode());
        }
        paymentApplyRequest.setExtend_request(paymentApplyExtendRequest);
        return paymentApplyRequest;
    }

    private String genResultDesc(List<RequestErrorInfo> errorInfos) {
        if (CollectionUtils.isEmpty(errorInfos)) {
            return null;
        }
        StringBuilder resultDesc = new StringBuilder();
        for (RequestErrorInfo errorInfo : errorInfos) {
            resultDesc.append(errorInfo.getError_detail()).append(System.lineSeparator());
        }
        return resultDesc.toString();
    }
}
