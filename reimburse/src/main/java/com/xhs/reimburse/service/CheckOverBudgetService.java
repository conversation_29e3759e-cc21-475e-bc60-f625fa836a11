package com.xhs.reimburse.service;

import com.xhs.reimburse.modal.dto.ExpenseDto;
import com.xhs.reimburse.modal.dto.travel.CheckResultItemDto;
import com.xhs.reimburse.modal.dto.travel.SeatContentDto;
import com.xhs.reimburse.modal.request.travel.ExpenseBudgetCheckRequest;
import com.xhs.reimburse.modal.request.travel.SeatIsOverBudgetRequest;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName CheckOverBudgetService.java
 * @createTime 2025年03月20日 20:49:00
 */
public interface CheckOverBudgetService {

    /**
     * 差旅费用差标校验
     *
     * @param allowList
     * @param expenseDto
     */
    void checkExpenseOverBudget(List<CheckResultItemDto> allowList, List<CheckResultItemDto> blockList, ExpenseDto expenseDto);

    /**
     * 差旅油电车费是否超标
     * @param expenseDto
     * @return
     */
    Boolean fuelParkingIsOverBudget(ExpenseDto expenseDto);

    /**
     * 火车票/飞机票是否超标
     *
     * @param request
     * @return
     */
    List<SeatContentDto> seatIsOverBudget(SeatIsOverBudgetRequest request);

    /**
     * 差旅工作餐是否超标
     *
     * @param param
     * @return
     */
    Boolean checkBusinessMealOverBudget(ExpenseBudgetCheckRequest param);

    ExpenseBudgetCheckRequest buildBusinessMealRequest(ExpenseDto dto);
}
