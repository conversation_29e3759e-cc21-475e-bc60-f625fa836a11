package com.xhs.reimburse.service;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ExpenseCityService.java
 * @createTime 2025年03月04日 14:59:00
 */
@Slf4j
@Component
public class ExpenseCityService {

    @ApolloJsonValue("${tier-one-cities:[]}")
    private List<String> tierOneCities;

    public BigDecimal getWaterLevelByCityName(String cityName) {
        int waterLevel = tierOneCities.contains(cityName) ? 200 : 100;
        return new BigDecimal(waterLevel);
    }

    public Boolean overWaterLevel(String cityName, BigDecimal perCapitaCost) {
        BigDecimal waterLevel = getWaterLevelByCityName(cityName);
        return perCapitaCost.compareTo(waterLevel) > 0;
    }
}
