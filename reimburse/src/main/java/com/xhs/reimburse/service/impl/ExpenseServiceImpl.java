package com.xhs.reimburse.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.google.common.collect.Lists;
import com.xhs.oa.office.utils.AssertHelper;
import com.xhs.reimburse.assembler.ExpenseAssembler;
import com.xhs.reimburse.assembler.InvoiceAssembler;
import com.xhs.reimburse.constant.CommonConstant;
import com.xhs.reimburse.enums.*;
import com.xhs.reimburse.enums.travel.ExpenseCheckItemEnum;
import com.xhs.reimburse.enums.travel.InvoiceCheckItemEnum;
import com.xhs.reimburse.enums.travel.TravelScheduleTypeEnum;
import com.xhs.reimburse.enums.travel.TravelTypeEnum;
import com.xhs.reimburse.mapper.ExpenseMapper;
import com.xhs.reimburse.mapper.RelationReimbursementFormExpenseMapper;
import com.xhs.reimburse.modal.dto.*;
import com.xhs.reimburse.modal.dto.travel.*;
import com.xhs.reimburse.modal.entity.EmployeeEntity;
import com.xhs.reimburse.modal.entity.ExpenseEntity;
import com.xhs.reimburse.modal.entity.ReimbursementFormEntity;
import com.xhs.reimburse.modal.request.PageQueryExpenseRequest;
import com.xhs.reimburse.modal.request.ReimburseFormRequest;
import com.xhs.reimburse.modal.request.travel.ExpenseBudgetCheckRequest;
import com.xhs.reimburse.modal.response.LabelValueExtendResponse;
import com.xhs.reimburse.modal.response.LabelValueResponse;
import com.xhs.reimburse.service.*;
import com.xhs.reimburse.service.external.ehr.EhrEmployeeRpcService;
import com.xhs.reimburse.utils.DateUtil;
import com.xhs.reimburse.utils.StreamUtil;
import com.xhs.reimburse.xhsoa.mapper.TravelPlaceMapper;
import com.xhs.reimburse.xhsoa.modal.TravelPlace;
import com.xhs.reimburse.xhsoa.modal.TravelStation;
import com.xiaohongshu.erp.common.exception.BusinessException;
import com.xiaohongshu.erp.common.framework.page.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.xhs.reimburse.constant.CommonConstant.EXPENSE_FIELD_ID_CODE_RELATION_INVOICE;

/**
 * <AUTHOR>
 * @date :2025/02/12 - 下午2:44
 * @description :
 */
@Slf4j
@Service
public class ExpenseServiceImpl extends ServiceImpl<ExpenseMapper, ExpenseEntity> implements ExpenseService {
    @Resource
    private InvoiceAssembler invoiceAssembler;
    @Resource
    private ExpenseAssembler expenseAssembler;
    @Resource
    private ReimburseCommonService reimburseCommonService;
    @Resource
    private DynamicFormFieldServiceImpl dynamicFormFieldService;
    @Resource
    private RelationExpenseInvoiceServiceImpl relationExpenseInvoiceService;
    @Resource
    private RelationReimbursementFormExpenseService relationReimbursementFormExpenseService;
    @Resource
    private ReimbursementFormService reimbursementFormService;
    @Resource
    private InvoiceServiceImpl invoiceServiceImpl;
    @Resource
    private ExpenseCityService expenseCityService;
    @Resource
    private EhrEmployeeRpcService ehrEmployeeRpcService;
    @Resource
    private TravelApplyService travelApplyService;
    @Resource
    private TravelApplyExpenseFormRelationServiceImpl travelApplyExpenseFormRelationService;
    @Resource
    private CheckOverBudgetService checkOverBudgetService;
    @Resource
    private RelationReimbursementFormExpenseMapper relationReimbursementFormExpenseMapper;
    @Resource
    private ExpenseMapper expenseMapper;
    @Resource
    private TravelPlaceMapper travelPlaceMapper;

    @ApolloJsonValue("${expense_from_type_open_list:[]}")
    private List<String> expenseFromTypeOpenList;

    // 常量
    private final String FIELD_CODE = "fieldCode";
    private final String FIELD_CODE_TOTAL_AMOUNT = "amount";
    private final String FIELD_CODE_EXPENSE_AMOUNT = "amount";
    private final String FIELD_CODE_EXPENSE_DATE = "datePhase";
    private final String DEFAULT_VALUE = "defaultValue";

    @Override
    public Integer getPendingCount() {
        String userId = reimburseCommonService.getUserId();
        LambdaQueryWrapper<ExpenseEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ExpenseEntity::getStatus, RecordStatusEnum.VALID.getCode());
        queryWrapper.eq(ExpenseEntity::getCreatorUserId, userId);
        queryWrapper.eq(ExpenseEntity::getExpenseFormStatus, ExpenseFormStatusEnum.WGL.getCode());
        return baseMapper.selectCount(queryWrapper);
    }

    @Override
    public List<LabelValueResponse> getExpenseFormType() {
        List<LabelValueResponse> formTypes = ExpenseFormTypeEnum.getFormTypes();

        return formTypes.stream().filter(item -> {
            return expenseFromTypeOpenList.contains(item.getValue());
        }).collect(Collectors.toList());
    }

    @Override
    public List<LabelValueExtendResponse> getExpenseSubject(String formType) {
        return ExpenseFormTypeEnum.getSubjectsByFromType(formType);
    }

    @Override
    public List<String> getAllowedInvoiceTypes(String formType, String firstSubject, String secondSubject) {
        if (!StringUtils.isEmpty(secondSubject)) {
            return ExpenseSecondSubjectEnum.getAllowedLabelValues(secondSubject);
        }

        if (!StringUtils.isEmpty(firstSubject)) {
            return ExpenseFirstSubjectEnum.getAllowedLabelValues(firstSubject);
        }

        return Collections.emptyList();
    }

    @Override
    public PageResult<ExpenseDto> pageQueryExpense(PageQueryExpenseRequest request) {
        String userId = reimburseCommonService.getUserId();
        Page<ExpenseEntity> page = new Page<>(request.getPageNum(), request.getPageSize());

        LambdaQueryWrapper<ExpenseEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ExpenseEntity::getStatus, RecordStatusEnum.VALID.getCode());
        queryWrapper.eq(ExpenseEntity::getCreatorUserId, userId);

        if (request.getExpenseStatus() == 1) {
            queryWrapper.eq(ExpenseEntity::getExpenseFormStatus, ExpenseFormStatusEnum.WGL.getCode());
        }

        if (request.getExpenseStatus() == 2) {
            queryWrapper.notIn(ExpenseEntity::getExpenseFormStatus, Arrays.asList(ExpenseFormStatusEnum.WGL.getCode()));
        }

        if (request.getExpenseStatus() == 3) {
            queryWrapper.eq(ExpenseEntity::getExpenseStatus, ExpenseStatusEnum.YBC.getCode());
            queryWrapper.in(ExpenseEntity::getExpenseFormStatus, Arrays.asList(ExpenseFormStatusEnum.WGL.getCode(), ExpenseFormStatusEnum.CH.getCode(), ExpenseFormStatusEnum.BH.getCode()));
        }

        queryWrapper.orderByDesc(ExpenseEntity::getId);
        Page<ExpenseEntity> expenseEntityPage = baseMapper.selectPage(page, queryWrapper);

        PageResult<ExpenseDto> pageResult = new PageResult<>();
        List<ExpenseDto> expenseDtoList = expenseAssembler.toDtoList(expenseEntityPage.getRecords());
        pageResult.setList(expenseDtoList);
        pageResult.setTotal((int) expenseEntityPage.getTotal());
        pageResult.setTotalPage((int) expenseEntityPage.getPages());
        return pageResult;
    }

    @Override
    public ExpenseDto getExpenseDtoByUuid(String uuid) {
        return this.getExpenseDtoByUuid(uuid, false);
    }

    @Override
    public ExpenseDto getExpenseByUuIdWithDeleted(String uuid) {
        ExpenseDto expenseDto = this.getExpenseDtoByUuid(uuid, true);
        expenseDateConvertToDotSeparatedDate(expenseDto);
        return expenseDto;
    }

    @Override
    public void expenseDateConvertToDotSeparatedDate(ExpenseDto expenseDto) {
        if(Objects.isNull(expenseDto)) {
            return;
        }
        // 费用关联发票日期转换
        invoiceServiceImpl.invoiceDateConvertToDotSeparatedDate(expenseDto.getRelationInvoiceList());
        // 费用消费日期格式转换
        List<DynamicFormFieldDto> dynamicFormFieldDtoList = expenseDto.getDynamicFormFieldDtoList();
        Object datePhaseObject = dynamicFormFieldService.getDefaultValueObject(dynamicFormFieldDtoList, CommonConstant.EXPENSE_FIELD_ID_CODE_DATE_PHASE);
        if (!dynamicFormFieldService.isValueEmpty(datePhaseObject)) {
            List<String> list = (List<String>) datePhaseObject;
            List<String> newList = new ArrayList<>();
            for (String date : list) {
                newList.add(DateUtil.convertToDotSeparatedDate(date));
                dynamicFormFieldService.updateDynamicFromDefaultValue(dynamicFormFieldDtoList, CommonConstant.EXPENSE_FIELD_ID_CODE_DATE_PHASE, newList);
            }
        }
    }

    @Override
    public ExpenseDto getExpenseDtoByUuid(String uuid, boolean withDeleted) {
        ExpenseEntity expenseEntity = getExpenseEntityByUuid(uuid, withDeleted);
        ExpenseDto dto = expenseAssembler.toDto(expenseEntity);

        // 费用明细
        Object object = dynamicFormFieldService.getDefaultValueObject(dto.getDynamicFormFieldDtoList(), EXPENSE_FIELD_ID_CODE_RELATION_INVOICE);
        List<InvoiceDto> invoiceDtoList = new ArrayList<>();
        if (!dynamicFormFieldService.isValueEmpty(object)) {
            invoiceDtoList = invoiceServiceImpl.queryInvoice((List<String>) object);
        }
        dto.setRelationInvoiceList(invoiceDtoList);
        for (InvoiceDto invoiceDto : invoiceDtoList) {
            validateInvoiceItinerary(invoiceDto, dto);
        }

        // 费用下发票是否被修改过
        dto.setRelationInvoicesHasModified(checkInvoicesModified(invoiceDtoList));

        // 附件
        Object uploadObject = dynamicFormFieldService.getDefaultValueObject(dto.getDynamicFormFieldDtoList(), CommonConstant.EXPENSE_FIELD_ID_CODE_UPLOADER);
        List<FileInfoDto> fileInfoDtoList = new ArrayList<>();
        if (!dynamicFormFieldService.isValueEmpty(uploadObject)) {
            fileInfoDtoList = getFileInfoDtoListFromDynamicFormField(uploadObject);
        }
        dto.setUploadFileInfoList(fileInfoDtoList);

        //补充校验信息
        ExpenseCheckDto checkDto = checkExpenseDto(dto);
        dto.setExpenseCheckDto(checkDto);

        // 按模板顺序返回
        String realKey = getRealKey(dto.getFormType(), dto.getFirstSubject(), dto.getSecondSubject());
        List<DynamicFormFieldDto> templateList = dynamicFormFieldService.getExpenseDynamicFormFieldList(realKey);
        sortedDynamicFormFieldList(templateList, dto.getDynamicFormFieldDtoList());
        dto.setDynamicFormFieldDtoList(templateList);

        // 关联的费用formNum
        if (!CollectionUtils.isEmpty(dto.getRelationFormUuidList())) {
            List<ReimbursementFormEntity> formList = reimbursementFormService.queryReimbursementFormEntity(dto.getRelationFormUuidList())
                    .stream().filter(entity -> {
                        return entity != null && entity.getIsValid().equals(1);
                    }).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(formList)) {
                dto.setRelationFormNum(formList.get(0).getFormNum());
            }
        }

        return dto;
    }

    @Override
    public void sortedDynamicFormFieldList(List<DynamicFormFieldDto> sortList, List<DynamicFormFieldDto> list) {
        Map<String, DynamicFormFieldDto> fileCodeToDtoMap = StreamUtil.toMap(list, DynamicFormFieldDto::getFieldCode);
        sortList.forEach(field -> {
            if (fileCodeToDtoMap.containsKey(field.getFieldCode())) {
                DynamicFormFieldDto dynamicFormFieldDto = fileCodeToDtoMap.get(field.getFieldCode());
                field.setHasDefault(dynamicFormFieldDto.isHasDefault());
                field.setDefaultValue(dynamicFormFieldDto.getDefaultValue());
                field.setDefaultValueMap(dynamicFormFieldDto.getDefaultValueMap());
            }
        });
    }

    private ExpenseCheckDto checkExpenseDto(ExpenseDto dto) {
        ExpenseCheckDto checkDto = new ExpenseCheckDto();
        // 无科目类型情况下不做校验
        if (dto.getFirstSubject() == null || dto.getSecondSubject() == null) {
            return checkDto;
        }

        // 判断发票类型符合科目可用类型
        List<String> allowedInvoiceTypes = ExpenseSecondSubjectEnum.getAllowedLabelValues(dto.getSecondSubject());
        List<String> ticketTypes = dto.getRelationInvoiceList().stream().map(InvoiceDto::getTicketType).collect(Collectors.toList());
        checkDto.setSubjectMatchInvoicePass(new HashSet<>(allowedInvoiceTypes).containsAll(ticketTypes));

        // 业务招待费-餐饮 判断平均餐标
        if (Objects.equals(ExpenseSecondSubjectEnum.YWZDCY.getSubjectCode(), dto.getSecondSubject())) {
            Object city = dynamicFormFieldService.getDefaultValueObject(dto.getDynamicFormFieldDtoList(), CommonConstant.EXPENSE_FIELD_ID_CODE_CITY);
            Object peopleAmount = dynamicFormFieldService.getDefaultValueObject(dto.getDynamicFormFieldDtoList(), CommonConstant.EXPENSE_FIELD_ID_CODE_AMOUNT);
            Object peopleNum = dynamicFormFieldService.getDefaultValueObject(dto.getDynamicFormFieldDtoList(), CommonConstant.EXPENSE_FIELD_ID_CODE_NUM_OF_PEOPLE);
            if (dynamicFormFieldService.isValueEmpty(city) || dynamicFormFieldService.isValueEmpty(peopleAmount) || dynamicFormFieldService.isValueEmpty(peopleNum)) {
                checkDto.setAvgAmountPass(true);
            } else {
                BigDecimal amount = new BigDecimal((String) peopleAmount);
                BigDecimal num = new BigDecimal((String) peopleNum);
                BigDecimal cityAvgAmount = expenseCityService.getWaterLevelByCityName((String) city);
                BigDecimal totalThreshold = num.multiply(cityAvgAmount);
                checkDto.setAvgAmountPass(amount.compareTo(totalThreshold) <= 0);
            }
        } else if (Objects.equals(ExpenseFirstSubjectEnum.CL_BUSINESS_MEAL.getSubjectCode(), dto.getFirstSubject())) {
            ExpenseBudgetCheckRequest request = checkOverBudgetService.buildBusinessMealRequest(dto);
            checkDto.setAvgAmountPass(!checkOverBudgetService.checkBusinessMealOverBudget(request));
        } else {
            checkDto.setAvgAmountPass(true);
        }
        return checkDto;
    }

    private Boolean checkInvoicesModified(List<InvoiceDto> invoiceDtoList) {
        if (CollectionUtils.isEmpty(invoiceDtoList)) {
            return false;
        }

        String userId = reimburseCommonService.getUserId();
        for (InvoiceDto invoiceDto : invoiceDtoList) {
            if (OcrContentModifiedEnum.MODIFIED.getCode() == invoiceDto.getOcrContentModified() && !invoiceDto.getCreatorUserId().equals(userId)) {
                return true;
            }
        }
        return false;
    }

    private List<FileInfoDto> getFileInfoDtoListFromDynamicFormField(Object uploadObject) {
        List<FileInfoDto> fileInfoDtoList = new ArrayList<>();
        if (uploadObject instanceof JSONArray) {
            fileInfoDtoList = ((JSONArray) uploadObject).toJavaList(FileInfoDto.class);
        } else if (uploadObject instanceof List) {
            List<?> list = (List<?>) uploadObject;
            if (!list.isEmpty() && list.get(0) instanceof FileInfoDto) {
                fileInfoDtoList = (List<FileInfoDto>) list;
            }
        }
        invoiceAssembler.batchSetFileNewUrl(fileInfoDtoList);
        return fileInfoDtoList;
    }

    @Override
    public ExpenseEntity getExpenseEntityByUuid(String uuid, boolean withDeleted) {
        LambdaQueryWrapper<ExpenseEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ExpenseEntity::getUuid, uuid);
        if (!withDeleted) {
            queryWrapper.eq(ExpenseEntity::getStatus, RecordStatusEnum.VALID.getCode());
        }
        List<ExpenseEntity> expenseEntities = baseMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(expenseEntities)) {
            throw new BusinessException("费用不存在");
        }
        return expenseEntities.get(0);
    }

    @Override
    public String updateExpense(ExpenseDto expenseDto) {
        log.info("updateExpense expenseDto:{}", expenseDto);
        ExpenseEntity expenseEntity = checkUpdateExpense(expenseDto.getUuid());

        buildExpenseRelationInvoices(expenseDto);
        buildExceptionDetailsMap(expenseDto);
        buildCalculationValue(expenseDto);
        expenseDto.setExpenseStatus(getSaveExpenseStatus(expenseDto).getCode());

        ExpenseEntity updateEntity = expenseAssembler.toEntity(expenseDto);
        updateEntity.setId(expenseEntity.getId());
        updateEntity.setUpdateTime(new Date());
        baseMapper.updateById(updateEntity);

        return expenseDto.getUuid();
    }

    public ExpenseEntity checkUpdateExpense(String uuid) {
        AssertHelper.notBlank(uuid, "费用编号不能为空");

        ExpenseEntity expenseEntity = getExpenseEntityByUuid(uuid, false);
        AssertHelper.notNull(expenseEntity, "费用不存在");

        Integer expenseFormStatus = expenseEntity.getExpenseFormStatus();
        List<Integer> errorCodeList = Lists.newArrayList(ExpenseFormStatusEnum.SHZ.getCode(), ExpenseFormStatusEnum.YBX.getCode());
        if (errorCodeList.contains(expenseFormStatus)) {
            throw new BusinessException(999, "费用关联报销单状态不可修改");
        }

        return expenseEntity;
    }

    public void buildExpenseRelationInvoices(ExpenseDto expenseDto) {
        Object object = dynamicFormFieldService.getDefaultValueObject(expenseDto.getDynamicFormFieldDtoList(), CommonConstant.EXPENSE_FIELD_ID_CODE_RELATION_INVOICE);
        if (!dynamicFormFieldService.isValueEmpty(object)) {
            List<String> relationUuIds = (List<String>) object;
            expenseDto.setExpenseInvoiceStatus(CollectionUtils.isEmpty(relationUuIds) ? InvoiceExpenseStatusEnum.WGL.getCode() : InvoiceExpenseStatusEnum.YGL.getCode());
            relationExpenseInvoiceService.updateExpenseInvoiceRelation(reimburseCommonService.getUserId(), expenseDto.getUuid(), relationUuIds);
            updateExpenseInvoiceDateRange(expenseDto, relationUuIds);
        } else {
            relationExpenseInvoiceService.clearRelationByExpenseUuid(expenseDto.getUuid());
            expenseDto.setExpenseInvoiceStatus(InvoiceExpenseStatusEnum.WGL.getCode());
        }
    }

    @Transactional(rollbackFor = Exception.class, transactionManager = "oaOfficeTransactionManager")
    public void buildExceptionDetailsMap(ExpenseDto dto) {
        Map<String, String> exceptionDetailsMap = dto.getExceptionDetailsMap();

        // 餐饮-超水位
        if (!CollectionUtils.isEmpty(exceptionDetailsMap) && !StringUtils.isEmpty(dto.getSecondSubject()) && dto.getSecondSubject().equals(ExpenseSecondSubjectEnum.YWZDCY.getSubjectCode())) {
            Object object = dynamicFormFieldService.getDefaultValueObject(dto.getDynamicFormFieldDtoList(), CommonConstant.EXPENSE_FIELD_ID_CODE_CITY);
            if (!dynamicFormFieldService.isValueEmpty(object)) {
                BigDecimal waterLevelByCityName = expenseCityService.getWaterLevelByCityName((String) object);
                exceptionDetailsMap.put(CommonConstant.EXPENSE_CUSTOMER_INFO_WATER_LEVEL, waterLevelByCityName.toString());
            }
        }

        // 费用金额不等于发票金额
        if (dto.getAmount() != null && dto.getAmount().compareTo(BigDecimal.ZERO) > 0) {
            Object object = dynamicFormFieldService.getDefaultValueObject(dto.getDynamicFormFieldDtoList(), EXPENSE_FIELD_ID_CODE_RELATION_INVOICE);
            if (!dynamicFormFieldService.isValueEmpty(object)) {
                List<InvoiceDto> invoiceDtoList = invoiceServiceImpl.queryInvoice((List<String>) object);
                BigDecimal totalAmount = BigDecimal.ZERO;
                for (InvoiceDto invoiceDto : invoiceDtoList) {
                    if (invoiceDto.getAmount() != null && invoiceDto.getAmount().compareTo(BigDecimal.ZERO) > 0) {
                        totalAmount = totalAmount.add(invoiceDto.getAmount());
                    }
                }
                if (dto.getAmount().compareTo(totalAmount) > 0) {
                    exceptionDetailsMap.put(CommonConstant.EXPENSE_CUSTOMER_INFO_RELATION_INVOICE_AMOUNT, totalAmount.toString());
                }
            }
        }
    }

    /**
     * 计算餐饮-人均费用
     *
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class, transactionManager = "oaOfficeTransactionManager")
    public void buildCalculationValue(ExpenseDto dto) {
        List<DynamicFormFieldDto> dynamicFormFieldDtoList = dto.getDynamicFormFieldDtoList();

        if (!StringUtils.isEmpty(dto.getFirstSubject()) && dto.getFirstSubject().equals(ExpenseFirstSubjectEnum.YBFY_RECEPTION.getSubjectCode())) {
            Object numberObject = dynamicFormFieldService.getDefaultValueObject(dynamicFormFieldDtoList, CommonConstant.EXPENSE_FIELD_ID_CODE_NUM_OF_PEOPLE);
            Object amountObject = dynamicFormFieldService.getDefaultValueObject(dynamicFormFieldDtoList, CommonConstant.EXPENSE_FIELD_ID_CODE_AMOUNT);
            if (!dynamicFormFieldService.isValueEmpty(numberObject) && !dynamicFormFieldService.isValueEmpty(amountObject)) {
                BigDecimal numberOfPeople = new BigDecimal(numberObject.toString());
                BigDecimal amount = new BigDecimal(amountObject.toString());

                BigDecimal perCapitaCost = numberOfPeople.compareTo(BigDecimal.ZERO) > 0
                        ? amount.divide(numberOfPeople, 2, RoundingMode.HALF_UP)
                        : BigDecimal.ZERO;

                dynamicFormFieldDtoList.stream()
                        .filter(item -> CommonConstant.EXPENSE_FIELD_ID_CODE_PER_CAPITA_COST.equals(item.getFieldCode()))
                        .forEach(item -> {
                            item.setHasDefault(true);
                            item.setDefaultValue(perCapitaCost.toString());
                        });
            }
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "oaOfficeTransactionManager")
    public String saveExpense(ExpenseDto expenseDto) {
        log.info("saveExpense expenseDto:{}", expenseDto);
        // 设置通用值
        String uuId = reimburseCommonService.getUuid();
        String userId = StringUtils.isEmpty(expenseDto.getCreatorUserId()) ? reimburseCommonService.getUserId() : expenseDto.getCreatorUserId();
        expenseDto.setUuid(uuId);
        expenseDto.setCreatorUserId(userId);
        expenseDto.setCreateTime(new Date());
        expenseDto.setUpdateTime(new Date());
        expenseDto.setStatus(RecordStatusEnum.VALID.getCode());
        expenseDto.setExpenseInvoiceStatus(InvoiceExpenseStatusEnum.WGL.getCode());
        expenseDto.setExpenseFormStatus(InvoiceFormStatusEnum.WGL.getCode());

        List<String> relationUuIds = new ArrayList<>();
        Object object = dynamicFormFieldService.getDefaultValueObject(expenseDto.getDynamicFormFieldDtoList(), EXPENSE_FIELD_ID_CODE_RELATION_INVOICE);
        if (!dynamicFormFieldService.isValueEmpty(object)) {
            relationUuIds = (List<String>) object;
        }

        // 校验发票是否被用过
        invoiceServiceImpl.usedCheck(relationUuIds);

        // 是否关联发票
        InvoiceExpenseStatusEnum invoiceExpenseStatusEnum = CollectionUtils.isEmpty(relationUuIds) ? InvoiceExpenseStatusEnum.WGL : InvoiceExpenseStatusEnum.YGL;
        expenseDto.setExpenseInvoiceStatus(invoiceExpenseStatusEnum.getCode());
        updateExpenseInvoiceDateRange(expenseDto, relationUuIds);

        Integer expenseSource = Objects.isNull(expenseDto.getExpenseSource()) ? InvoiceSourceEnum.BUSINESS.getCode() : expenseDto.getExpenseSource();
        expenseDto.setExpenseSource(expenseSource);

        buildExceptionDetailsMap(expenseDto);
        buildCalculationValue(expenseDto);

        expenseDto.setExpenseStatus(getSaveExpenseStatus(expenseDto).getCode());
        ExpenseEntity expenseEntity = expenseAssembler.toEntity(expenseDto);
        baseMapper.insert(expenseEntity);

        relationExpenseInvoiceService.updateExpenseInvoiceRelation(userId, uuId, relationUuIds);
        return uuId;
    }

    public ExpenseStatusEnum getSaveExpenseStatus(ExpenseDto expenseDto) {
        if (StringUtils.isEmpty(expenseDto.getFormType()) || StringUtils.isEmpty(expenseDto.getFirstSubject())) {
            return ExpenseStatusEnum.DBC;
        }

        // 对动态表单排序-和配置顺序保持一致
        String realKey = getRealKey(expenseDto.getFormType(), expenseDto.getFirstSubject(), expenseDto.getSecondSubject());
        List<DynamicFormFieldDto> templateList = dynamicFormFieldService.getExpenseDynamicFormFieldList(realKey);
        sortedDynamicFormFieldList(templateList, expenseDto.getDynamicFormFieldDtoList());
        expenseDto.setDynamicFormFieldDtoList(templateList);

        if (!dynamicFormFieldService.validateRequiredFields(templateList)) {
            return ExpenseStatusEnum.DBC;
        }

        // 业务招待费-超标原因必填校验
        if (expenseDto.getFirstSubject().equals(ExpenseFirstSubjectEnum.YBFY_RECEPTION.getSubjectCode()) && expenseDto.getSecondSubject().equals(ExpenseSecondSubjectEnum.YWZDCY.getSubjectCode())) {
            BigDecimal perCapitaCost = new BigDecimal((String) dynamicFormFieldService.getDefaultValueObject(templateList, CommonConstant.EXPENSE_FIELD_ID_CODE_PER_CAPITA_COST));
            String city = (String) dynamicFormFieldService.getDefaultValueObject(templateList, CommonConstant.EXPENSE_FIELD_ID_CODE_CITY);
            if (expenseCityService.overWaterLevel(city, perCapitaCost) && StringUtils.isEmpty(expenseDto.getBeyondStandardInfo())) {
                return ExpenseStatusEnum.DBC;
            }
        }

        // 差旅工作餐
        if (expenseDto.getFirstSubject().equals(ExpenseFirstSubjectEnum.CL_BUSINESS_MEAL.getSubjectCode())) {
            // 校验是否超标
            ExpenseBudgetCheckRequest request = checkOverBudgetService.buildBusinessMealRequest(expenseDto);
            Boolean isOverBudget = checkOverBudgetService.checkBusinessMealOverBudget(request);

            TravelMealOverLimitReasonDto travelMealOverLimitReasonDto = expenseDto.getTravelMealOverLimitReasonDto();
            if (isOverBudget && (travelMealOverLimitReasonDto == null || !travelMealOverLimitReasonDto.isComplete())) {
                return ExpenseStatusEnum.DBC;
            }

            // 未超标,清空超标理由
            if (!isOverBudget) {
                expenseDto.setTravelMealOverLimitReasonDto(null);
            }
        }


        return ExpenseStatusEnum.YBC;
    }

    @Override
    public void logicDeleteExpense(String uuId) {
        //校验是否可逻辑删除: 费用当前状态为有效，并且提报状态不能为审核中和已报销
        ExpenseEntity expenseEntity = checkUpdateExpense(uuId);
        //清除发票的关联关系
        relationExpenseInvoiceService.clearRelationByExpenseUuid(uuId);
        //清除单据的关联关系
        relationReimbursementFormExpenseService.clearRelationByExpenseUuid(uuId);
        //置为无效
        expenseEntity.setStatus(RecordStatusEnum.INVALID.getCode());
        baseMapper.updateById(expenseEntity);
    }

    @Override
    public List<DynamicFormFieldDto> getExpenseDynamicFormFields(String formType, String firstSubjectCode, String secondSubjectCode) {
        if (StringUtils.isEmpty(formType) && StringUtils.isEmpty(firstSubjectCode) && StringUtils.isEmpty(secondSubjectCode)) {
            throw new BusinessException("报销类型和费用科目不能同时为空");
        }

        String realKey = getRealKey(formType, firstSubjectCode, secondSubjectCode);
        List<DynamicFormFieldDto> expenseDynamicFormFieldList = dynamicFormFieldService.getExpenseDynamicFormFieldList(realKey);

        dynamicFormFieldService.buildDynamicFormDefaultValue(secondSubjectCode, expenseDynamicFormFieldList);
        dynamicFormFieldService.buildDynamicFormDefaultValueMap(expenseDynamicFormFieldList);
        return expenseDynamicFormFieldList;
    }

    private String getRealKey(String formType, String firstSubjectCode, String secondSubjectCode) {
        StringJoiner realKeyJoiner = new StringJoiner("_");
        if (!StringUtils.isEmpty(formType)) {
            realKeyJoiner.add(formType);
        }
        if (!StringUtils.isEmpty(firstSubjectCode)) {
            realKeyJoiner.add(firstSubjectCode);
        }
        if (!StringUtils.isEmpty(secondSubjectCode)) {
            realKeyJoiner.add(secondSubjectCode);
        }
        return realKeyJoiner.toString().toUpperCase();
    }

    /**
     * 查询费用
     *
     * @param expenseUuidList 费用ID列表
     * @return List<ExpenseDto> 费用列表
     */
    @Override
    public List<ExpenseDto> queryExpense(List<String> expenseUuidList) {
        if (CollectionUtils.isEmpty(expenseUuidList)) {
            return Collections.emptyList();
        }

        List<ExpenseDto> expenseDtoList = expenseAssembler.toDtoList(queryExpenseEntity(expenseUuidList));
        expenseDtoList.forEach(expenseDto -> {
            String expenseUuid = expenseDto.getUuid();
            List<InvoiceDto> invoiceDtoList = relationExpenseInvoiceService.getExpenseAllInvoice(expenseUuid);
            expenseDto.setRelationInvoiceList(invoiceDtoList);

            // 异常校验
            ExpenseCheckDto checkDto = checkExpenseDto(expenseDto);
            expenseDto.setExpenseCheckDto(checkDto);

            // 附件
            Object uploadObject = dynamicFormFieldService.getDefaultValueObject(expenseDto.getDynamicFormFieldDtoList(), CommonConstant.EXPENSE_FIELD_ID_CODE_UPLOADER);
            List<FileInfoDto> fileInfoDtoList = new ArrayList<>();
            if (!dynamicFormFieldService.isValueEmpty(uploadObject)) {
                fileInfoDtoList = getFileInfoDtoListFromDynamicFormField(uploadObject);
            }
            expenseDto.setUploadFileInfoList(fileInfoDtoList);

        });
        return expenseDtoList;
    }

    public List<ExpenseEntity> queryExpenseEntity(List<String> expenseUuidList) {
        if (CollectionUtils.isEmpty(expenseUuidList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<ExpenseEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(ExpenseEntity::getUuid, expenseUuidList);
        return baseMapper.selectList(queryWrapper);
    }

    /**
     * 匹配费用
     * 时间匹配规则：
     * （1）水单 match 费用： time in [startTime, endTime]
     * （1）发票 match 费用： time in [startTime, endTime]
     * <p>
     * 一般费用报销单：amount(diff)
     * 差旅报销单：amount(diff) && time(in)
     *
     * @param expenseMatchRuleDto 匹配规则
     * @return 匹配的费用ID列表
     */
    public List<String> matchExpense(ExpenseMatchRuleDto expenseMatchRuleDto) {
        // 用户ID
        String userId = expenseMatchRuleDto.getUserId();
        // 匹配金额
        String matchAmount = expenseMatchRuleDto.getAmount();
        // 金额差值
        BigDecimal matchAmountDiff = new BigDecimal(expenseMatchRuleDto.getAmountDiff());
        // 匹配时间
        String matchTime = expenseMatchRuleDto.getTime();
        // 费用状态列表
        List<Integer> expenseStatusList = expenseMatchRuleDto.getExpenseStatusList();
        // 费用-报销单状态列表
        List<Integer> expenseFormStatusList = expenseMatchRuleDto.getExpenseFormStatusList();

        // 是否开启「金额匹配模式」
        boolean amountMatchMode = StringUtils.isNotBlank(matchAmount);
        // 是否开启「时间匹配模式」
        boolean timeMatchMode = StringUtils.isNotBlank(matchTime);

        // 用户所有满足条件的费用
        LambdaQueryWrapper<ExpenseEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ExpenseEntity::getCreatorUserId, userId).eq(ExpenseEntity::getStatus, RecordStatusEnum.VALID.getCode());
        if (CollectionUtils.isNotEmpty(expenseStatusList)) {
            queryWrapper.in(ExpenseEntity::getExpenseStatus, expenseStatusList);
        }
        if (CollectionUtils.isNotEmpty(expenseFormStatusList)) {
            queryWrapper.in(ExpenseEntity::getExpenseFormStatus, expenseFormStatusList);
        }
        List<ExpenseEntity> expenseList = baseMapper.selectList(queryWrapper);

        // 遍历所有费用
        return expenseList.stream()
                .filter(invoice -> {
                    JSONArray fields = JSONArray.parseArray(invoice.getDetails());
                    boolean amountMatchResult = true;
                    boolean timeMatchResult = true;

                    // 遍历本费用的动态表单
                    for (Object object : fields) {
                        JSONObject field = (JSONObject) object;

                        // 仅在开启「金额匹配模式」时才进行金额匹配
                        if (amountMatchMode && FIELD_CODE_EXPENSE_AMOUNT.equals(field.getString(FIELD_CODE))) {
                            String invoiceAmount = field.getString(DEFAULT_VALUE);
                            // 保证「发票金额」、「匹配金额」格式正确
                            if (reimburseCommonService.amountFormatIsError(invoiceAmount) || reimburseCommonService.amountFormatIsError(matchAmount)) {
                                return false;
                            }

                            // 计算本费用金额是否匹配上
                            BigDecimal matchAmountBD = new BigDecimal(matchAmount);
                            BigDecimal invoiceAmountBD = new BigDecimal(invoiceAmount);
                            BigDecimal amountDiff = matchAmountBD.subtract(invoiceAmountBD).abs();
                            amountMatchResult = amountDiff.compareTo(matchAmountDiff) <= 0;
                        }

                        // 仅在开启「时间匹配模式」时才进行时间匹配
                        if (timeMatchMode && FIELD_CODE_EXPENSE_DATE.equals(field.getString(FIELD_CODE))) {
                            String datePhase = field.getString(DEFAULT_VALUE);
                            // 保证「消费时间」、「匹配开始时间」、「匹配结束时间」格式正确
                            if (dynamicFormFieldService.isValueEmpty(datePhase) || reimburseCommonService.dateFormatIsError(matchTime)) {
                                return false;
                            }

                            // 保证费用的「开始时间」、「结束时间」格式正确
                            JSONArray timeList = JSONArray.parseArray(datePhase);
                            String startTime = timeList.getString(0);
                            String endTime = timeList.getString(1);
                            if (reimburseCommonService.dateFormatIsError(startTime) || reimburseCommonService.dateFormatIsError(endTime)) {
                                return false;
                            }

                            // 计算本发票时间是否匹配上
                            timeMatchResult = reimburseCommonService.timeInTimeRange(matchTime, startTime, endTime);
                        }
                    }

                    // 本费用是否匹配上
                    return amountMatchResult && timeMatchResult;
                })
                .map(ExpenseEntity::getUuid)
                .collect(Collectors.toList());
    }


    /**
     * 按条件查询费用
     *
     * @param expenseQueryRuleDto 查询条件
     * @return
     */
    public List<String> queryExpenseByRule(ExpenseQueryRuleDto expenseQueryRuleDto) {
        // 用户ID
        String userId = expenseQueryRuleDto.getUserId();
        // 匹配金额
        String matchAmount = expenseQueryRuleDto.getAmount();
        // 金额差值
        BigDecimal matchAmountDiff = new BigDecimal(expenseQueryRuleDto.getAmountDiff());
        // 匹配开始时间
        String queryStartTime = expenseQueryRuleDto.getStartTime();
        // 匹配结束时间
        String queryEndTime = expenseQueryRuleDto.getEndTime();
        // 费用状态列表
        List<Integer> expenseStatusList = expenseQueryRuleDto.getExpenseStatusList();
        // 费用-报销单状态列表
        List<Integer> expenseFormStatusList = expenseQueryRuleDto.getExpenseFormStatusList();

        // 是否开启「金额匹配模式」
        boolean amountMatchMode = StringUtils.isNotBlank(matchAmount);
        // 是否开启「时间匹配模式」
        boolean timeMatchMode = StringUtils.isNotBlank(queryStartTime) && StringUtils.isNotBlank(queryEndTime);

        // 用户所有满足条件的费用
        LambdaQueryWrapper<ExpenseEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ExpenseEntity::getCreatorUserId, userId).eq(ExpenseEntity::getStatus, RecordStatusEnum.VALID.getCode());
        if (CollectionUtils.isNotEmpty(expenseStatusList)) {
            queryWrapper.in(ExpenseEntity::getExpenseStatus, expenseStatusList);
        }
        if (CollectionUtils.isNotEmpty(expenseFormStatusList)) {
            queryWrapper.in(ExpenseEntity::getExpenseFormStatus, expenseFormStatusList);
        }
        List<ExpenseEntity> expenseList = baseMapper.selectList(queryWrapper);

        // 遍历所有费用
        return expenseList.stream()
                .filter(invoice -> {
                    JSONArray fields = JSONArray.parseArray(invoice.getDetails());
                    boolean amountMatchResult = true;
                    boolean timeMatchResult = true;

                    // 遍历本费用的动态表单
                    for (Object object : fields) {
                        JSONObject field = (JSONObject) object;

                        // 仅在开启「金额匹配模式」时才进行金额匹配
                        if (amountMatchMode && FIELD_CODE_EXPENSE_AMOUNT.equals(field.getString(FIELD_CODE))) {
                            String invoiceAmount = field.getString(DEFAULT_VALUE);
                            // 保证「发票金额」、「匹配金额」格式正确
                            if (reimburseCommonService.amountFormatIsError(invoiceAmount) || reimburseCommonService.amountFormatIsError(matchAmount)) {
                                return false;
                            }

                            // 计算本费用金额是否匹配上
                            BigDecimal matchAmountBD = new BigDecimal(matchAmount);
                            BigDecimal invoiceAmountBD = new BigDecimal(invoiceAmount);
                            BigDecimal amountDiff = matchAmountBD.subtract(invoiceAmountBD).abs();
                            amountMatchResult = amountDiff.compareTo(matchAmountDiff) <= 0;
                        }

                        // 仅在开启「时间匹配模式」时才进行时间匹配
                        if (timeMatchMode && FIELD_CODE_EXPENSE_DATE.equals(field.getString(FIELD_CODE))) {
                            String datePhase = field.getString(DEFAULT_VALUE);
                            // 保证「消费时间」、「匹配开始时间」、「匹配结束时间」格式正确
                            if (dynamicFormFieldService.isValueEmpty(datePhase) || reimburseCommonService.dateFormatIsError(queryStartTime) || reimburseCommonService.dateFormatIsError(queryEndTime)) {
                                return false;
                            }

                            // 保证费用的「开始时间」、「结束时间」格式正确
                            JSONArray timeList = JSONArray.parseArray(datePhase);
                            String startTime = timeList.getString(0);
                            String endTime = timeList.getString(1);
                            if (reimburseCommonService.dateFormatIsError(startTime) || reimburseCommonService.dateFormatIsError(endTime)) {
                                return false;
                            }

                            // 计算本发票时间是否匹配上
                            timeMatchResult = reimburseCommonService.timeRangeInTimeRange(startTime, endTime, queryStartTime, queryEndTime);
                        }
                    }

                    // 本费用是否匹配上
                    return amountMatchResult && timeMatchResult;
                })
                .map(ExpenseEntity::getUuid)
                .collect(Collectors.toList());
    }

    /**
     * @param userId          用户ID
     * @param expenseUuidList 费用ID列表
     */
    public void existCheck(String userId, List<String> expenseUuidList) {
        List<ExpenseEntity> expenseList = queryExpenseEntity(expenseUuidList);
        if (CollectionUtils.isEmpty(expenseList)) {
            throw new BusinessException("未找到该费用");
        }

        List<String> userIdList = expenseList.stream().map(ExpenseEntity::getCreatorUserId).distinct().collect(Collectors.toList());
        if (userIdList.size() != 1 || !userIdList.contains(userId)) {
            throw new BusinessException("非本人费用");
        }
    }

    /**
     * 计算所有费用金额总和
     *
     * @param expenses 费用编号
     * @return 费用金额总和，默认值为0.00
     */
    @Override
    public BigDecimal expenseSumAmount(List<ExpenseDto> expenses) {
        if (CollectionUtils.isEmpty(expenses)) {
            return BigDecimal.ZERO;
        }
        return expenses.stream().map(ExpenseDto::getAmount)
                .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public void updateExpenseListStatus(List<String> expenseUuidList, InvoiceExpenseStatusEnum invoiceExpenseStatus, ExpenseStatusEnum expenseStatus, ExpenseFormStatusEnum expenseFormStatus) {
        List<ExpenseEntity> expenseEntityList = queryExpenseEntity(expenseUuidList);
        expenseEntityList.forEach(expenseEntity -> {
            if (invoiceExpenseStatus != null) {
                expenseEntity.setExpenseInvoiceStatus(invoiceExpenseStatus.getCode());
            }

            if (expenseStatus != null) {
                expenseEntity.setExpenseStatus(expenseStatus.getCode());
            }

            if (expenseFormStatus != null) {
                expenseEntity.setExpenseFormStatus(expenseFormStatus.getCode());
            }

            baseMapper.updateById(expenseEntity);
        });
    }

    @Override
    public void updateExpenseRelationFild(String expenseUuid, List<String> invoiceUuidList) {
        ExpenseEntity expenseEntity = queryExpenseEntity(Lists.newArrayList(expenseUuid)).get(0);
        List<DynamicFormFieldDto> expenseDynamicFormFields = getExpenseDynamicFormFields(expenseEntity.getFormType(), expenseEntity.getFirstSubject(), expenseEntity.getSecondSubject());
        Map<String, DynamicFormFieldDto> dynamicFormFieldMap = expenseDynamicFormFields.stream().collect(Collectors.toMap(DynamicFormFieldDto::getFieldCode, Function.identity()));

        List<DynamicFormFieldDto> oldDynamicFormFieldList = JSON.parseArray(expenseEntity.getDetails(), DynamicFormFieldDto.class);
        for (DynamicFormFieldDto dynamicFormField : oldDynamicFormFieldList) {
            String oldFieldCode = dynamicFormField.getFieldCode();
            dynamicFormFieldMap.put(oldFieldCode, dynamicFormField);
        }
        List<DynamicFormFieldDto> newDynamicFormFieldList = Lists.newArrayList(dynamicFormFieldMap.values());
        for (DynamicFormFieldDto dynamicFormField : newDynamicFormFieldList) {
            if (EXPENSE_FIELD_ID_CODE_RELATION_INVOICE.equals(dynamicFormField.getFieldCode())) {
                dynamicFormField.setDefaultValue(Lists.newArrayList(invoiceUuidList));
                dynamicFormField.setHasDefault(CollectionUtils.isNotEmpty(invoiceUuidList));
            }
        }
        expenseEntity.setDetails(JSON.toJSONString(newDynamicFormFieldList));
        expenseMapper.updateById(expenseEntity);
    }

    @Override
    public void setDynamicFormFieldList(String userId, String secondSubject, List<DynamicFormFieldDto> dynamicFormFieldDtoList) {
        // 自动填充base地和公司成员默认值
        dynamicFormFieldService.buildDynamicFormDefaultValue(secondSubject, dynamicFormFieldDtoList);
        dynamicFormFieldService.buildDynamicFormDefaultValueMap(dynamicFormFieldDtoList);
    }

    @Override
    public List<LabelValueResponse> getSeats(String seatType) {
        if ("train".equals(seatType)) {
            return TrainSeatTypeEnum.getTrainSeatTypeList();
        }

        if ("plane".equals(seatType)) {
            return PlaneCabinTypeEnum.getPlaneCabinList();
        }

        return new ArrayList<>();
    }

    @Override
    public List<LabelValueResponse> getTravelMealOverLimitReasonTypes() {
        return TravelMealOverLimitReasonTypeEnum.getList();
    }

    @Override
    public BigDecimal queryTravelMealLimit(String userId) {
        userId = StringUtils.isEmpty(userId)
                ? reimburseCommonService.getUserId()
                : userId;
        EmployeeEntity employeeEntity = ehrEmployeeRpcService.queryEhrEmployeeEntity(userId, true);
        if (employeeEntity != null && EmploymentTypeEnum.FORMAL_EMP.getCode().equals(employeeEntity.getEmployeeType())) {
            return new BigDecimal(150);
        }
        return new BigDecimal(100);
    }

    public void updateExpenseInvoiceDateRange(ExpenseDto expenseDto, List<String> relationInvoiceUuIds) {
        if (CollectionUtils.isEmpty(relationInvoiceUuIds)) {
            return;
        }

        List<InvoiceDto> invoiceDtoList = invoiceServiceImpl.queryInvoice(relationInvoiceUuIds);
        List<String> invoiceDateList = invoiceDtoList
                .stream()
                .filter(invoiceDto -> invoiceDto != null && !StringUtils.isEmpty(invoiceDto.getInvoiceDate()))
                .map(InvoiceDto::getInvoiceDate)
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(invoiceDateList)) {
            expenseDto.setRelationInvoiceStartTime(null);
            expenseDto.setRelationInvoiceEndTime(null);
            return;
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        // 解析日期，忽略解析失败的
        List<Date> validDates = invoiceDateList.stream()
                .map(dateStr -> {
                    try {
                        return sdf.parse(dateStr);
                    } catch (ParseException e) {
                        return null;
                    }
                })
                .filter(date -> date != null)
                .sorted()
                .collect(Collectors.toList());

        if (validDates.isEmpty()) {
            expenseDto.setRelationInvoiceStartTime(null);
            expenseDto.setRelationInvoiceEndTime(null);
            return;
        }

        expenseDto.setRelationInvoiceStartTime(validDates.get(0));
        expenseDto.setRelationInvoiceEndTime(validDates.get(validDates.size() - 1));
    }

    private void checkExpenseSelf(ExpenseDto expenseDto, List<CheckResultItemDto> blockList, List<CheckResultItemDto> allowList) {
        // 存在验真失败的发票
        CheckResultItemDto checkResultItemDto = ExpenseCheckItemEnum.getCheckResultItemDto(ExpenseCheckItemEnum.EXPENSE_INVOICE_VERIFICATION_CHECK);
        checkExistValidateFailInvoice(blockList, expenseDto.getRelationInvoiceList(), checkResultItemDto);
        // 超标校验
        checkOverBudgetService.checkExpenseOverBudget(allowList, blockList, expenseDto);
    }

    @Override
    public void validateInvoiceItinerary(InvoiceDto invoiceDto, ExpenseDto expenseDto) {
        if (Objects.isNull(invoiceDto)) {
            return;
        }

        ItineraryCheckResultDto itineraryCheckResultDto = new ItineraryCheckResultDto();
        List<CheckResultItemDto> blockList = new ArrayList<>();
        List<CheckResultItemDto> allowList = new ArrayList<>();

        // 费用状态校验
        if (ExpenseStatusEnum.DBC.getCode().equals(expenseDto.getExpenseStatus())) {
            blockList.add(InvoiceCheckItemEnum.getCheckResultItemDto(InvoiceCheckItemEnum.INVOICE_RELATION_EXPENSE_STATUS_CHECK));
            itineraryCheckResultDto.setAllowSubmissionCheckResultList(allowList);
            itineraryCheckResultDto.setBlockSubmissionCheckResultList(blockList);
            invoiceDto.setItineraryCheckResultDto(itineraryCheckResultDto);
            return;
        }
        // 类型校验
        validateInvoiceItineraryExpenseMatch(blockList, expenseDto, invoiceDto);


        // 差旅申请单相关
        List<String> relationFormUuidList = expenseDto.getRelationFormUuidList();
        List<ReimbursementFormEntity> reimbursementFormEntities = reimbursementFormService.queryReimbursementFormEntity(relationFormUuidList);
        if (CollectionUtils.isEmpty(reimbursementFormEntities)) {
            itineraryCheckResultDto.setAllowSubmissionCheckResultList(allowList);
            itineraryCheckResultDto.setBlockSubmissionCheckResultList(blockList);
            invoiceDto.setItineraryCheckResultDto(itineraryCheckResultDto);
            return;
        }
        ReimbursementFormEntity formEntity = reimbursementFormEntities.get(0);

        // 主体是否一致
        String paymentCompanyName = reimbursementFormService.getPaymentCompanyName(formEntity);
        if (!invoiceDto.purchaserNameCheck(paymentCompanyName)) {
            CheckResultItemDto checkResultItemDto = InvoiceCheckItemEnum.getCheckResultItemDto(InvoiceCheckItemEnum.INVOICE_ENTITY_CHECK);
            allowList.add(checkResultItemDto);
        }

        // 差旅行程时间范围校验
        if (ExpenseFormTypeEnum.CL.getFormTypeCode().equals(formEntity.getFormType())) {
            // 差旅行程
            ReimburseFormRequest request = JSONObject.parseObject(formEntity.getFormContent(), ReimburseFormRequest.class);
            List<TravelApplyFormInfoDto> travelApplyList = travelApplyService.queryTravelApplyScheduleDetail(request.getTravelApplyFormNums());
            if (!CollectionUtils.isEmpty(travelApplyList)) {
                List<TravelDateDto> travelScheduleDateScope = getTravelScheduleDateScope(travelApplyList);
                checkInvoiceData(allowList, travelScheduleDateScope, Arrays.asList(invoiceDto), InvoiceCheckItemEnum.getCheckResultItemDto(InvoiceCheckItemEnum.INVOICE_TIME_CHECK));
            }
        }

        itineraryCheckResultDto.setBlockSubmissionCheckResultList(blockList);
        itineraryCheckResultDto.setAllowSubmissionCheckResultList(allowList);
        invoiceDto.setItineraryCheckResultDto(itineraryCheckResultDto);
    }

    @Override
    public List<ExpenseDto> getExpenseCheckInfos(List<String> expenseUuidList, List<TravelApplyFormInfoDto> travelApplyList, String formPaymentCompanyName) {
        if (CollectionUtils.isEmpty(expenseUuidList)) {
            return Collections.emptyList();
        }
        List<ExpenseDto> expenseDtoList = queryExpense(expenseUuidList);

        // 并行流校验
        expenseDtoList.parallelStream()
                .forEach(expenseDto -> buildExpenseCheckInfos(expenseDto, travelApplyList, formPaymentCompanyName));

        return expenseDtoList;
    }


    /**
     * 设置费用校验结果
     *
     * @param expenseDto
     * @param travelApplyList
     * @param formPaymentCompanyName
     * @return
     */
    public void buildExpenseCheckInfos(ExpenseDto expenseDto, List<TravelApplyFormInfoDto> travelApplyList, String formPaymentCompanyName) {
        if (Objects.isNull(expenseDto)) {
            return;
        }

        ItineraryCheckResultDto itineraryCheckResultDto = new ItineraryCheckResultDto();
        List<CheckResultItemDto> blockList = new ArrayList<>();
        List<CheckResultItemDto> allowList = new ArrayList<>();

        // 费用自身校验
        checkExpenseSelf(expenseDto, blockList, allowList);

        // 主体校验
        if (!StringUtils.isEmpty(formPaymentCompanyName)) {
            checkExistCompanyNoMatchInvoice(blockList, expenseDto.getRelationInvoiceList(), formPaymentCompanyName);
        }

        // 差旅行程相关校验
        if (!CollectionUtils.isEmpty(travelApplyList)) {
            // 行程校验
            buildItineraryCheckResultItinerary(blockList, travelApplyList, expenseDto);
            // 地点校验
            buildItineraryCheckResultCity(blockList, travelApplyList, expenseDto);
            // 出差天数
            buildItineraryCheckResultDays(blockList, travelApplyList, expenseDto);
            // 开票日期校验
            List<TravelDateDto> travelScheduleDateScope = getTravelScheduleDateScope(travelApplyList);
            checkInvoiceData(allowList, travelScheduleDateScope, expenseDto.getRelationInvoiceList(), ExpenseCheckItemEnum.getCheckResultItemDto(ExpenseCheckItemEnum.EXPENSE_TIME_CHECK));
        }

        itineraryCheckResultDto.setAllowSubmissionCheckResultList(allowList);
        itineraryCheckResultDto.setBlockSubmissionCheckResultList(blockList);
        expenseDto.setItineraryCheckResultDto(itineraryCheckResultDto);
    }


    private void validateInvoiceItineraryExpenseMatch(List<CheckResultItemDto> blockList, ExpenseDto expenseDto, InvoiceDto invoiceDto) {
        List<String> allowedInvoiceTypes = getAllowedInvoiceTypes(expenseDto.getFormType(), expenseDto.getFirstSubject(), expenseDto.getSecondSubject());
        if (!CollectionUtils.isEmpty(allowedInvoiceTypes) && !allowedInvoiceTypes.contains(invoiceDto.getTicketType())) {
            CheckResultItemDto checkResultItemDto = InvoiceCheckItemEnum.getCheckResultItemDto(InvoiceCheckItemEnum.INVOICE_TYPE_CHECK);
            checkResultItemDto.setDescription(String.format(checkResultItemDto.getDescription(), expenseDto.getExpenseSubjectName()));
            blockList.add(checkResultItemDto);
        }
    }

    private void buildItineraryCheckResultItinerary(List<CheckResultItemDto> blockList,
                                                    List<TravelApplyFormInfoDto> travelApplications,
                                                    ExpenseDto expense) {
        String firstSubjectCode = expense.getFirstSubject();
        if (StringUtils.isEmpty(firstSubjectCode)) {
            return;
        }

        // 定义费用科目与对应的校验参数
        Map<String, TravelCheckParamsDto> checkParamsMap = new HashMap<>();
        checkParamsMap.put(ExpenseFirstSubjectEnum.CL_HOTEL.getSubjectCode(),
                new TravelCheckParamsDto(Arrays.asList(TravelTypeEnum.SHORT_TERM.getCode(), TravelTypeEnum.EXTERNAL.getCode()), TravelScheduleTypeEnum.HOTEL));

        checkParamsMap.put(ExpenseFirstSubjectEnum.CL_LONG_TRIP.getSubjectCode(),
                new TravelCheckParamsDto(Arrays.asList(TravelTypeEnum.LONG_TERM.getCode()), TravelScheduleTypeEnum.HOTEL));

        checkParamsMap.put(ExpenseFirstSubjectEnum.CL_TRAIN_TICKET.getSubjectCode(),
                new TravelCheckParamsDto(null, TravelScheduleTypeEnum.TRAIN));

        checkParamsMap.put(ExpenseFirstSubjectEnum.CL_AIR_TICKET.getSubjectCode(),
                new TravelCheckParamsDto(null, TravelScheduleTypeEnum.FLIGHT));

        TravelCheckParamsDto checkParams = checkParamsMap.get(firstSubjectCode);
        if (checkParams != null && !isItineraryValid(travelApplications, checkParams)) {
            log.info("buildItineraryCheckResultItinerary travelApplications:{},checkParams:{}", travelApplications, checkParams);
            CheckResultItemDto checkResultItemDto = ExpenseCheckItemEnum.getCheckResultItemDto(ExpenseCheckItemEnum.EXPENSE_ITINERARY_CHECK);
            checkResultItemDto.setDescription(String.format(checkResultItemDto.getDescription(), expense.getFirstSubjectName()));
            blockList.add(checkResultItemDto);
        }
    }

    /**
     * 校验行程是否符合要求
     */
    private boolean isItineraryValid(List<TravelApplyFormInfoDto> travelApplications, TravelCheckParamsDto params) {
        for (TravelApplyFormInfoDto application : travelApplications) {
            String travelType = application.getTravelType();
            if (params.isApplicable(travelType)) {
                for (TravelScheduleDetailDto schedule : application.getTravelScheduleDetails()) {
                    if (!CollectionUtils.isEmpty(schedule.getScheduleSubTypes()) && schedule.getScheduleSubTypes().contains(params.getScheduleType().getCode())) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    public void buildItineraryCheckResultCity(List<CheckResultItemDto> blockedCheckResults, List<TravelApplyFormInfoDto> travelApplications, ExpenseDto expenseDto) {
        String firstSubjectCode = expenseDto.getFirstSubject();
        List<String> needCheckFirstSubjectCodes = Arrays.asList(
                ExpenseFirstSubjectEnum.CL_HOTEL.getSubjectCode(),
                ExpenseFirstSubjectEnum.CL_LONG_TRIP.getSubjectCode(),
                ExpenseFirstSubjectEnum.CL_BUSINESS_MEAL.getSubjectCode(),
                // ExpenseFirstSubjectEnum.CL_TRAIN_TICKET.getSubjectCode(),
                // ExpenseFirstSubjectEnum.CL_AIR_TICKET.getSubjectCode(),
                ExpenseFirstSubjectEnum.CL_TAXI_TICKET.getSubjectCode());
        if (StringUtils.isEmpty(firstSubjectCode) || !needCheckFirstSubjectCodes.contains(firstSubjectCode)) {
            return;
        }

        // 查询费用中的城市
        List<String> expenseCities = getTravelExpenseCities(expenseDto);
        if (CollectionUtils.isEmpty(expenseCities)) {
            return;
        }

        // 行程中所有地点
        Set<String> scheduleCities = getScheduleCities(travelApplications);

        // 费用中城市
        List<String> invalidCities = new ArrayList<>();
        for (String city : expenseCities) {
            if (!scheduleCities.contains(city)) {
                invalidCities.add(city);
            }
        }

        if (!CollectionUtils.isEmpty(invalidCities)) {
            log.info("buildItineraryCheckResultCity scheduleCities:{},expenseCities:{}", scheduleCities, expenseCities);
            String validCitiesName = invalidCities.stream().distinct().collect(Collectors.joining(","));
            CheckResultItemDto checkResultItemDto = ExpenseCheckItemEnum.getCheckResultItemDto(ExpenseCheckItemEnum.EXPENSE_LOCATION_CHECK);
            checkResultItemDto.setDescription(String.format(checkResultItemDto.getDescription(), validCitiesName));
            blockedCheckResults.add(checkResultItemDto);
        }
    }


    private List<String> getTravelExpenseCities(ExpenseDto expenseDto) {
        List<String> expenseCities = new ArrayList<>();
        String firstSubjectCode = expenseDto.getFirstSubject();

        List<String> cityCodes = Arrays.asList(ExpenseFirstSubjectEnum.CL_HOTEL.getSubjectCode(), ExpenseFirstSubjectEnum.CL_LONG_TRIP.getSubjectCode(), ExpenseFirstSubjectEnum.CL_BUSINESS_MEAL.getSubjectCode());
        if (cityCodes.contains(firstSubjectCode)) {
            if (ExpenseFirstSubjectEnum.CL_BUSINESS_MEAL.getSubjectCode().equals(expenseDto.getFirstSubject())) {
                if (ExpenseSecondSubjectEnum.JNGZC.getSubjectCode().equals(expenseDto.getSecondSubject())) {
                    Object defaultValueObject = dynamicFormFieldService.getDefaultValueObject(expenseDto.getDynamicFormFieldDtoList(), CommonConstant.EXPENSE_FIELD_ID_CODE_TRIP_CITY_MULTIPLE_CODE);
                    if (dynamicFormFieldService.isValueEmpty(defaultValueObject)) {
                        return Collections.emptyList();
                    }
                    List<TravelCityDto> travelCityDtoList = JSON.parseArray(defaultValueObject.toString(), TravelCityDto.class);
                    if (CollectionUtils.isNotEmpty(travelCityDtoList)) {
                        expenseCities.addAll(travelCityDtoList.stream().map(TravelCityDto::getLabel).collect(Collectors.toList()));
                    }
                }
                if (ExpenseSecondSubjectEnum.JWGZC.getSubjectCode().equals(expenseDto.getSecondSubject())) {
                    Object defaultValueObject = dynamicFormFieldService.getDefaultValueObject(expenseDto.getDynamicFormFieldDtoList(), CommonConstant.EXPENSE_FIELD_ID_CODE_CITY_CODE);
                    if (dynamicFormFieldService.isValueEmpty(defaultValueObject)) {
                        return Collections.emptyList();
                    }
                    TravelCityDto city = JSON.parseObject(defaultValueObject.toString(), TravelCityDto.class);
                    expenseCities.add(city.getLabel());
                }
            }
        }

        List<String> airTrainCityCodes = Arrays.asList(ExpenseFirstSubjectEnum.CL_TRAIN_TICKET.getSubjectCode(), ExpenseFirstSubjectEnum.CL_AIR_TICKET.getSubjectCode());
        if (airTrainCityCodes.contains(firstSubjectCode)) {
            Object defaultValueObject = dynamicFormFieldService.getDefaultValueObject(expenseDto.getDynamicFormFieldDtoList(), CommonConstant.EXPENSE_FIELD_ID_CODE_TRIP_ROUTE_CITY);
            if (dynamicFormFieldService.isValueEmpty(defaultValueObject)) {
                return Collections.emptyList();
            }
            List<TripRouteCityDto> tripRouteCityList = JSON.parseArray(JSON.toJSONString(defaultValueObject), TripRouteCityDto.class);
            for (TripRouteCityDto tripRouteCity : tripRouteCityList) {
                String startCity = tripRouteCity.getStartCity();
                // 火车票将站点转为城市名称
                if (ExpenseFirstSubjectEnum.CL_TRAIN_TICKET.getSubjectCode().equals(firstSubjectCode)) {
                    startCity = getTrainStationPlaceName(startCity);
                }
                expenseCities.add(startCity);
            }
        }

        List<String> taxiCityCodes = Arrays.asList(ExpenseFirstSubjectEnum.CL_TAXI_TICKET.getSubjectCode());
        if (taxiCityCodes.contains(firstSubjectCode)) {
            Object defaultValueObject = dynamicFormFieldService.getDefaultValueObject(expenseDto.getDynamicFormFieldDtoList(), CommonConstant.EXPENSE_FIELD_ID_CODE_CITY_MULTIPLE);
            if (dynamicFormFieldService.isValueEmpty(defaultValueObject)) {
                return Collections.emptyList();
            }
            List<String> tripRouteCityList = JSON.parseArray(JSON.toJSONString(defaultValueObject), String.class);
            expenseCities.addAll(tripRouteCityList);
        }
        return expenseCities;
    }

    public String getTrainStationPlaceName(String stationName) {
        if (StringUtils.isEmpty(stationName)) {
            return stationName;
        }

        // 通过火车站名称查询对应的城市 code
        List<TravelStation> travelStations = travelPlaceMapper.selectPlaceListByTrainStationName(stationName);
        if (!CollectionUtils.isEmpty(travelStations)) {
            String placeCode = travelStations.get(0).getPlaceCode();
            List<TravelPlace> placeList = travelPlaceMapper.selectByPlaceCode("train", placeCode);
            if (!CollectionUtils.isEmpty(placeList)) {
                return placeList.get(0).getPlaceName();
            }
        }
        return stationName;
    }

    public Set<String> getScheduleCities(List<TravelApplyFormInfoDto> travelApplications) {
        if (CollectionUtils.isEmpty(travelApplications)) {
            return Collections.emptySet();
        }

        return travelApplications.stream()
                .filter(Objects::nonNull)
                .flatMap(apply -> apply.getTravelScheduleDetails().stream())
                .filter(Objects::nonNull)
                .flatMap(schedule -> Stream.concat(
                        Optional.ofNullable(schedule.getDepTripPlaces()).orElse(Collections.emptyList()).stream(),
                        Optional.ofNullable(schedule.getArrTripPlaces()).orElse(Collections.emptyList()).stream()
                ))
                .filter(Objects::nonNull)
                .map(TravelPlaceDto::getPlaceName)
                .filter(place -> !StringUtils.isEmpty(place))
                .collect(Collectors.toSet());
    }

    public void buildItineraryCheckResultDays(List<CheckResultItemDto> blockList, List<TravelApplyFormInfoDto> travelApplyList, ExpenseDto expenseDto) {
        // 差旅工作餐需要校验
        String firstSubject = expenseDto.getFirstSubject();
        List<String> needCheckSubjectCodes = Arrays.asList(
                ExpenseFirstSubjectEnum.CL_HOTEL.getSubjectCode(),
                ExpenseFirstSubjectEnum.CL_LONG_TRIP.getSubjectCode(),
                ExpenseFirstSubjectEnum.CL_BUSINESS_MEAL.getSubjectCode()
        );

        if (StringUtils.isEmpty(firstSubject) || !needCheckSubjectCodes.contains(firstSubject)) {
            return;
        }

        List<TravelDateDto> dateScopeList = getHotelTravelScheduleDays(travelApplyList, expenseDto);

        Object stayDaysObject = dynamicFormFieldService.getDefaultValueObject(expenseDto.getDynamicFormFieldDtoList(), CommonConstant.EXPENSE_FIELD_ID_CODE_STAY_DAYS);
        if (dynamicFormFieldService.isValueEmpty(stayDaysObject)) {
            return;
        }
        Integer stayDays = Integer.valueOf(stayDaysObject.toString());

        List<String> dates = new ArrayList<>();

        Boolean isHotel = ExpenseFirstSubjectEnum.CL_HOTEL.getSubjectCode().equals(expenseDto.getFirstSubject())
                || ExpenseFirstSubjectEnum.CL_LONG_TRIP.getSubjectCode().equals(expenseDto.getFirstSubject());

        for (TravelDateDto travelDateDto : dateScopeList) {
            dates.addAll(travelDateDto.findDates(isHotel));
        }
        dates = dates.stream().distinct().collect(Collectors.toList());
        Integer totalDays = dates.size();


        if (stayDays > totalDays) {
            log.info("buildItineraryCheckResultDays stayDays:{},totalDays{}", stayDays, totalDays);
            blockList.add(ExpenseCheckItemEnum.getCheckResultItemDto(ExpenseCheckItemEnum.EXPENSE_DURATION_CHECK));
        }
    }


    public void checkInvoiceData(List<CheckResultItemDto> list, List<TravelDateDto> travelDateDtoList, List<InvoiceDto> invoiceDtoList, CheckResultItemDto checkResultItemDto) {
        if (CollectionUtils.isEmpty(invoiceDtoList)) {
            return;
        }

        for (InvoiceDto invoiceDto : invoiceDtoList) {
            if (invoiceDto == null || StringUtils.isEmpty(invoiceDto.getInvoiceDate())) {
                continue;
            }
            if (CollectionUtils.isEmpty(travelDateDtoList) || !isDateWithinTravelSchedule(invoiceDto.getInvoiceDate(), travelDateDtoList)) {
                list.add(checkResultItemDto);
                return;
            }
        }
    }

    public boolean isDateWithinTravelSchedule(String invoiceDate, List<TravelDateDto> travelDateDtoList) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date targetDate;
        try {
            targetDate = sdf.parse(invoiceDate);
        } catch (ParseException e) {
            log.warn("isDateWithinTravelSchedule invoiceDate illegal invoiceDate:{}", invoiceDate);
            return false;
        }

        for (TravelDateDto travelDateDto : travelDateDtoList) {
            if (!targetDate.before(travelDateDto.getStartDate()) && !targetDate.after(travelDateDto.getEndDate())) {
                return true;
            }
        }
        log.info("isDateWithinTravelSchedule invoiceDate:{},travelDateDtoList:{}", invoiceDate, travelDateDtoList);
        return false;
    }

    private List<TravelDateDto> getHotelTravelScheduleDays(List<TravelApplyFormInfoDto> travelApplyList, ExpenseDto expenseDto) {
        // 差旅-酒店
        if (ExpenseFirstSubjectEnum.CL_HOTEL.getSubjectCode().equals(expenseDto.getFirstSubject())) {
            Object cityObject = dynamicFormFieldService.getDefaultValueObject(expenseDto.getDynamicFormFieldDtoList(), CommonConstant.EXPENSE_FIELD_ID_CODE_CITY_CODE);
            if (dynamicFormFieldService.isValueEmpty(cityObject)) {
                return Collections.emptyList();
            }

            TravelCityDto city = JSON.parseObject(cityObject.toString(), TravelCityDto.class);
            List<TravelApplyFormInfoDto> validTravelApplyList = travelApplyList.stream()
                    .filter(item -> {
                        return isValidHotelTravel(item, city);
                    }).collect(Collectors.toList());

            return getTravelScheduleDateScope(validTravelApplyList);
        }

        return getTravelScheduleDateScope(travelApplyList);
    }

    private Boolean isValidHotelTravel(TravelApplyFormInfoDto travelApplyFormInfoDto, TravelCityDto city) {
        Set<String> scheduleCitiesSet = getScheduleCities(Arrays.asList(travelApplyFormInfoDto));
        return scheduleCitiesSet.contains(city.getLabel());
    }

    private List<TravelDateDto> getTravelScheduleDateScope(List<TravelApplyFormInfoDto> travelApplyList) {
        List<TravelDateDto> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(travelApplyList)) {
            return result;
        }

        // 过滤出包含 travelScheduleDetails 的 TravelApplyFormInfoDto
        List<TravelApplyFormInfoDto> validTravelApplyList = travelApplyList.stream()
                .filter(item -> item != null && !CollectionUtils.isEmpty(item.getTravelScheduleDetails()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(validTravelApplyList)) {
            return result;
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        for (TravelApplyFormInfoDto travelApplyFormInfoDto : validTravelApplyList) {
            List<TravelScheduleDetailDto> travelScheduleDetails = travelApplyFormInfoDto.getTravelScheduleDetails();
            if (CollectionUtils.isEmpty(travelScheduleDetails)) {
                continue;
            }

            List<TravelerDto> travelers = travelApplyFormInfoDto.getTravelers();
            Integer travelersNum = CollectionUtils.isEmpty(travelers) ? 0 : travelers.size();

            for (TravelScheduleDetailDto travelScheduleDetailDto : travelScheduleDetails) {
                if (travelScheduleDetailDto == null || StringUtils.isEmpty(travelScheduleDetailDto.getStartDate()) || StringUtils.isEmpty(travelScheduleDetailDto.getEndDate())) {
                    continue;
                }
                try {
                    TravelDateDto travelDateDto = new TravelDateDto();
                    travelDateDto.setStartD(travelScheduleDetailDto.getStartDate());
                    travelDateDto.setEndD(travelScheduleDetailDto.getEndDate());
                    travelDateDto.setStartDate(sdf.parse(travelScheduleDetailDto.getStartDate()));
                    travelDateDto.setEndDate(sdf.parse(travelScheduleDetailDto.getEndDate()));
                    travelDateDto.setScheduleType(travelScheduleDetailDto.getScheduleType());
                    travelDateDto.setTravelPeopleNumber(travelersNum);
                    travelDateDto.setStartDate(sdf.parse(travelScheduleDetailDto.getStartDate()));
                    travelDateDto.setEndDate(sdf.parse(travelScheduleDetailDto.getEndDate()));
                    result.add(travelDateDto);
                } catch (ParseException e) {
                    continue;
                }
            }
        }

        return result;
    }

    private void checkExistValidateFailInvoice(List<CheckResultItemDto> list, List<InvoiceDto> relationInvoiceList, CheckResultItemDto checkResultItemDto) {
        if (CollectionUtils.isEmpty(relationInvoiceList)) {
            return;
        }
        for (InvoiceDto invoiceDto : relationInvoiceList) {
            Integer invoiceValidateResult = invoiceDto.getInvoiceValidateResult();
            if (Objects.nonNull(invoiceValidateResult) && InvoiceValidateEnum.VALIDATE_FAIL.getCode().equals(invoiceValidateResult)) {
                list.add(checkResultItemDto);
                return;
            }
        }
    }

    private void checkExistCompanyNoMatchInvoice(List<CheckResultItemDto> list, List<InvoiceDto> relationInvoiceList, String targetCompanyName) {
        if (CollectionUtils.isEmpty(relationInvoiceList)) {
            return;
        }
        for (InvoiceDto invoiceDto : relationInvoiceList) {
            if (!invoiceDto.purchaserNameCheck(targetCompanyName)) {
                log.info("checkExistCompanyNoMatchInvoice invoiceDto:{},targetCompanyName:{}", invoiceDto, targetCompanyName);
                list.add(ExpenseCheckItemEnum.getCheckResultItemDto(ExpenseCheckItemEnum.EXPENSE_COMPANY_CHECK));
                return;
            }
        }
    }

    @Override
    public void checkReimbursementFormInExpense(ReimbursementFormCheckResultDto result, List<ExpenseDto> expenseList) {
        List<ExpenseCheckItemEnum> checkItemList = new ArrayList<>();

        // 拿到所有的校验项
        List<String> checkCodeList = expenseList.stream()
                .map(ExpenseDto::getItineraryCheckResultDto)
                .filter(Objects::nonNull)
                .flatMap(checkResult -> Stream.of(
                        checkResult.getBlockSubmissionCheckResultList(),
                        checkResult.getAllowSubmissionCheckResultList()
                ))
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(List::stream)
                .map(CheckResultItemDto::getCode)
                .distinct()
                .collect(Collectors.toList());

        // 获取code-enum的map
        Map<String, ExpenseCheckItemEnum> codeEnumMap = Arrays.stream(ExpenseCheckItemEnum.values()).collect(Collectors.toMap(ExpenseCheckItemEnum::getCode, Function.identity()));

        // 将包含的枚举项加入 checkItemList
        checkCodeList.stream()
                .map(codeEnumMap::get)
                .filter(Objects::nonNull)
                .forEach(checkItemList::add);

        // 费用完整性校验
        expenseIntegrityCheck(result, expenseList);

        // 统一处理checkItemList
        checkItemList.stream()
                .map(checkItem -> CheckResultDetailDto.builder()
                        .checkItemCode(checkItem.getCode())
                        .checkItemName(checkItem.getName())
                        .blockSubmit(checkItem.getBlockSubmit())
                        .build())
                .forEach(result.getCheckResultDetailList()::add);
    }

    /**
     * 费用完整性校验
     * @param result 校验结果
     * @param expenseList 费用列表
     */
    public void expenseIntegrityCheck(ReimbursementFormCheckResultDto result, List<ExpenseDto> expenseList) {
        List<ExpenseIntegrityFieldEnum> fieldList = new ArrayList<>();
        List<BlockItemDto> blockItemList = new ArrayList<>();
        ExpenseCheckItemEnum checkItem = ExpenseCheckItemEnum.EXPENSE_INTEGRITY_CHECK;

        for (ExpenseDto expenseDto : expenseList) {
            // 校验所有阻碍项
            if (StringUtils.isBlank(expenseDto.getFormType())) {
                fieldList.add(ExpenseIntegrityFieldEnum.FORM_TYPE);
            }
            if (StringUtils.isBlank(expenseDto.getFirstSubject())) {
                fieldList.add(ExpenseIntegrityFieldEnum.FIRST_SUBJECT);
            }

            // 确保上述两个字段非空
            if (StringUtils.isNotBlank(expenseDto.getFormType()) && StringUtils.isNotBlank(expenseDto.getFirstSubject())) {
                // 拿到动态表单
                String realKey = getRealKey(expenseDto.getFormType(), expenseDto.getFirstSubject(), expenseDto.getSecondSubject());
                List<DynamicFormFieldDto> templateList = dynamicFormFieldService.getExpenseDynamicFormFieldList(realKey);
                sortedDynamicFormFieldList(templateList, expenseDto.getDynamicFormFieldDtoList());
                expenseDto.setDynamicFormFieldDtoList(templateList);

                // 校验动态表单
                for (DynamicFormFieldDto field : templateList) {
                    // 如果是必填项且默认值为空，则添加到阻碍项列表
                    if (field.isMust() && dynamicFormFieldService.isValueEmpty(field.getDefaultValue())) {
                        blockItemList.add(BlockItemDto.builder()
                                .fieldCode(field.getFieldCode())
                                .fieldName(field.getFieldName())
                                .build());
                    }
                    // 行程-tripRouteCity 是object 特殊校验
                    if ("tripRouteCity".equals(field.getFieldCode())) {
                        List<TripRouteCityDto> TripRouteCityList = JSONArray.parseArray(field.getDefaultValue().toString(), TripRouteCityDto.class);
                        for (TripRouteCityDto tripRouteCityDto : TripRouteCityList) {
                            if (StringUtils.isEmpty(tripRouteCityDto.getStartCity()) || StringUtils.isEmpty(tripRouteCityDto.getEndCity()) || StringUtils.isEmpty(tripRouteCityDto.getSeatType())) {
                                fieldList.add(ExpenseIntegrityFieldEnum.TRIP_ROUTE_CITY);
                            }
                        }
                    }
                }

                // 业务招待费-超标说明
                if (expenseDto.getFirstSubject().equals(ExpenseFirstSubjectEnum.YBFY_RECEPTION.getSubjectCode()) &&
                        expenseDto.getSecondSubject().equals(ExpenseSecondSubjectEnum.YWZDCY.getSubjectCode()) &&
                        dynamicFormFieldService.validateRequiredFields(templateList)) {
                    BigDecimal perCapitaCost = new BigDecimal((String) dynamicFormFieldService.getDefaultValueObject(templateList, CommonConstant.EXPENSE_FIELD_ID_CODE_PER_CAPITA_COST));
                    String city = (String) dynamicFormFieldService.getDefaultValueObject(templateList, CommonConstant.EXPENSE_FIELD_ID_CODE_CITY);
                    if (expenseCityService.overWaterLevel(city, perCapitaCost) && StringUtils.isEmpty(expenseDto.getBeyondStandardInfo())) {
                        fieldList.add(ExpenseIntegrityFieldEnum.BEYOND_STANDARD_INFO);
                    }
                }

                // 差旅工作餐-超标原因
                if (expenseDto.getFirstSubject().equals(ExpenseFirstSubjectEnum.CL_BUSINESS_MEAL.getSubjectCode()) && dynamicFormFieldService.validateRequiredFields(templateList)) {
                    // 校验是否超标
                    ExpenseBudgetCheckRequest request = checkOverBudgetService.buildBusinessMealRequest(expenseDto);
                    Boolean isOverBudget = checkOverBudgetService.checkBusinessMealOverBudget(request);

                    TravelMealOverLimitReasonDto travelMealOverLimitReasonDto = expenseDto.getTravelMealOverLimitReasonDto();
                    if (isOverBudget && (travelMealOverLimitReasonDto == null || !travelMealOverLimitReasonDto.isComplete())) {
                        fieldList.add(ExpenseIntegrityFieldEnum.TRAVEL_MEAL_OVER_LIMIT_REASON);
                    }
                }
            }

            // 为发现的阻碍项构造 BlockItemDto
            fieldList.stream().distinct()
                    .map(field -> BlockItemDto.builder()
                            .fieldCode(field.getFieldCode())
                            .fieldName(field.getFieldName())
                            .build())
                    .forEach(blockItemList::add);

            // 有阻碍项
            if (CollectionUtils.isNotEmpty(blockItemList)) {
                CheckResultDetailDto detail = CheckResultDetailDto.builder()
                        .checkItemCode(checkItem.getCode())
                        .checkItemName(checkItem.getName())
                        .blockSubmit(checkItem.getBlockSubmit())
                        .blockItemList(blockItemList)
                        .build();
                result.getCheckResultDetailList().add(detail);
            }
        }
    }

    @Override
    public String formRelateExpenseCheck(List<ExpenseDto> expenseList) {
        //是否填充费用
        if (CollectionUtils.isEmpty(expenseList)) {
            return "单据未关联费用";
        }

        //费用是否阻碍提交
        for (ExpenseDto expense : expenseList) {
            String checkResult = expense.expenseBlockSubmitAlert();
            if (StringUtils.isNotBlank(checkResult)) {
                return checkResult;
            }
        }

        // 校验通过
        return "";
    }

    @Override
    public String formSingleSubmitCheck(List<ExpenseDto> expenseList) {
        List<String> secondSubjects = expenseList.stream().map(ExpenseDto::getSecondSubject).distinct().collect(Collectors.toList());

        if (secondSubjects.contains(ExpenseSecondSubjectEnum.STHD.getSubjectCode()) && secondSubjects.size() > 1) {
            return "报销明细选取不符合规定，社团活动不能与其他活动一并提交";
        }

        if (secondSubjects.contains(ExpenseSecondSubjectEnum.YWZDCY.getSubjectCode()) && secondSubjects.size() > 1) {
            return "业务招待费-餐饮不可与其他费用合并报销，请分单据报销";
        }

        return "";
    }
}
