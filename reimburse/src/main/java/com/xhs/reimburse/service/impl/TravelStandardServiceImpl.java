package com.xhs.reimburse.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.xhs.reimburse.mapper.TravelStandardMapper;
import com.xhs.reimburse.modal.entity.travel.TravelStandard;
import com.xhs.reimburse.modal.entity.travel.TravelStandardExample;
import com.xhs.reimburse.modal.vo.travel.CTripCountryVO;
import com.xhs.reimburse.service.TravelStandardService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class TravelStandardServiceImpl implements TravelStandardService {

    @Resource
    private TravelStandardMapper travelStandardMapper;

    @Override
    public List<TravelStandard> selectTravelBudgetStandardByCity(String subject, String cityId) {
        TravelStandardExample example = new TravelStandardExample();
        example.createCriteria()
                .andIsValidEqualTo(Byte.valueOf("1"))
                .andSubjectEqualTo(subject)
                .andCityIdEqualTo(cityId);
        return travelStandardMapper.selectByExample(example);
    }

    @Override
    public List<TravelStandard> selectBudgetStandardByCountryId(String subject, String countryId) {
        TravelStandardExample example = new TravelStandardExample();
        example.createCriteria()
                .andIsValidEqualTo(Byte.valueOf("1"))
                .andSubjectEqualTo(subject)
                .andCountryIdEqualTo(countryId)
                .andCityIdIsNull();
        return travelStandardMapper.selectByExample(example);
    }

    @Override
    public CTripCountryVO selectCountryInfoByCountryName(String countryName) {

        TravelStandardExample example = new TravelStandardExample();
        example.createCriteria()
                .andIsValidEqualTo(Byte.valueOf("1"))
                .andCountryNameEqualTo(countryName);

        List<TravelStandard> travelStandards = travelStandardMapper.selectByExample(example);
        if (CollUtil.isNotEmpty(travelStandards)) {
            TravelStandard travelStandard = travelStandards.get(0);
            return CTripCountryVO.builder()
                    .countryName(travelStandard.getCountryName())
                    .countryId(travelStandard.getCountryId())
                    .build();
        }

        return null;
    }

}
