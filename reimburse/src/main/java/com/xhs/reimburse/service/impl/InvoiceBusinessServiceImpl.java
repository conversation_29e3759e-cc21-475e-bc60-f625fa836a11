package com.xhs.reimburse.service.impl;

import com.xhs.oa.office.utils.AssertHelper;
import com.xhs.reimburse.constant.CommonConstant;
import com.xhs.reimburse.constant.ErrorConstant;
import com.xhs.reimburse.modal.dto.InvoiceDto;
import com.xhs.reimburse.modal.entity.InvoiceEntity;
import com.xhs.reimburse.modal.entity.ReimbursementFormEntity;
import com.xhs.reimburse.service.*;
import com.xhs.reimburse.service.external.redflow.RedFlowRpcService;
import com.xhs.reimburse.utils.StreamUtil;
import com.xiaohongshu.erp.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> jiangjixiang
 * @version : 1.0
 * @Description : Description
 * @date ：Created in 2025/5/20 15:26
 */
@Slf4j
@Service
public class InvoiceBusinessServiceImpl implements InvoiceBusinessService {

    @Resource
    private RelationExpenseInvoiceService relationExpenseInvoiceService;
    @Resource
    private RelationReimbursementFormExpenseService relationReimbursementFormExpenseService;
    @Resource
    private ReimbursementFormService reimbursementFormService;
    @Resource
    private InvoiceService invoiceService;
    @Resource
    private ReimburseCommonService reimburseCommonService;
    @Resource
    private RedFlowRpcService redFlowRpcService;

    @Override
    public ReimbursementFormEntity getReimbursementFormNumByInvoiceUuid(String invoiceUuid) {
        String expenseUuid = relationExpenseInvoiceService.queryInvoiceBelongExpense(invoiceUuid);
        if (StringUtils.isBlank(expenseUuid)) {
            return null;
        }
        String reimbursementFormUuid = relationReimbursementFormExpenseService.queryExpenseBelongReimbursementForm(expenseUuid);
        if (StringUtils.isBlank(reimbursementFormUuid)) {
            return null;
        }
        List<ReimbursementFormEntity> fromList = reimbursementFormService.queryReimbursementFormEntity(Collections.singletonList(reimbursementFormUuid));
        fromList = StreamUtil.filterToList(fromList, o -> CommonConstant.VALID_INTER.equals(o.getIsValid()));
        if (CollectionUtils.isEmpty(fromList)) {
            return null;
        }
        return fromList.get(0);
    }

    @Override
    public InvoiceDto getInvoiceDtoByUuidCheckAuth(String uuId, boolean needExistMsg) {
        if(needExistMsg){
            // 前置校验是否存在 不存在返回null
            List<InvoiceEntity> invoiceEntities = invoiceService.getValidInvoiceEntities(uuId);
            if(CollectionUtils.isEmpty(invoiceEntities)){
                return null;
            }
        }
        // 鉴权
        checkInvoiceAuth(uuId);
        return invoiceService.getInvoiceDtoByUuid(uuId);
    }

    @Override
    public InvoiceDto getInvoiceForBotByUuidCheckAuth(String uuId, boolean needExistMsg) {
        if(needExistMsg){
            // 前置校验是否存在 不存在返回null
            List<InvoiceEntity> invoiceEntities = invoiceService.getValidInvoiceEntities(uuId);
            if(CollectionUtils.isEmpty(invoiceEntities)){
                return null;
            }
        }
        // 鉴权
        checkInvoiceAuth(uuId);
        return invoiceService.getInvoiceForBotByUuid(uuId);
    }

    @Override
    public InvoiceDto getInvoiceByIdWithDeleted(String uuId) {
        checkInvoiceAuthSelf(uuId);
        return invoiceService.getInvoiceDtoByUuid(uuId, true);
    }


    /**
     * @description 发票鉴权-仅发票创建者本人可操作
     * <AUTHOR>
     * @param uuId 发票uuId
     * @date 2025/5/21 11:47
     */
    private void checkInvoiceAuthSelf(String uuId) {
        AssertHelper.notBlank(uuId, "参数异常");
        InvoiceEntity invoiceEntity = invoiceService.getInvoiceEntityByUuid(uuId, true);
        String loginUserId = reimburseCommonService.getUserId();
        if (!loginUserId.equals(invoiceEntity.getCreatorUserId())) {
            throw new BusinessException(ErrorConstant.NOT_AUTH_CODE, ErrorConstant.NOT_AUTH_MSG);
        }
    }

    /**
     * @description 发票鉴权
     * 关联报销单：follow 流程redFlow的权限
     * 未关联报销单：仅本人可查看
     * <AUTHOR>
     * @param uuId 发票uuid
     * @date 2025/5/20 16:32
     */
    private void checkInvoiceAuth(String uuId) {
        AssertHelper.notBlank(uuId,"参数异常");
        InvoiceEntity invoiceEntity = invoiceService.getInvoiceEntityByUuid(uuId, false);
        String loginUserId = reimburseCommonService.getUserId();
        if (loginUserId.equals(invoiceEntity.getCreatorUserId())) {
            // 发票创建者 有权限直接返回
            return;
        }
        // 非发票创建者 鉴权
        ReimbursementFormEntity form = this.getReimbursementFormNumByInvoiceUuid(uuId);
        if (Objects.nonNull(form) && StringUtils.isNotBlank(form.getFormNum())) {
            //关联报销单：follow 流程redFlow的权限
            boolean flowAuth = redFlowRpcService.checkUserFormPermission(loginUserId, form.getFormNum());
            if (!flowAuth) {
                throw new BusinessException(ErrorConstant.NOT_AUTH_CODE, ErrorConstant.NOT_AUTH_MSG);
            }
        } else {
            //未关联报销单：仅本人可查看
            throw new BusinessException(ErrorConstant.NOT_AUTH_CODE, ErrorConstant.NOT_AUTH_MSG);
        }
    }

}
