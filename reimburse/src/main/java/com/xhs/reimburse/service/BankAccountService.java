package com.xhs.reimburse.service;

import com.xhs.reimburse.modal.dto.BankAccountDto;
import com.xhs.reimburse.modal.request.BankAccountRequest;

import java.util.List;

/**
 * <AUTHOR>
 * @date :2025/02/07 - 下午8:40
 * @description :
 */
public interface BankAccountService {

    /**
     * 查询员工的报销的银行卡号列表
     * 首次进入为空，并从人事系统同步落库
     *
     * @param userId 核心人事用户编号
     * @return List<BankAccountDto>
     */
    List<BankAccountDto> getBankAccountList(String userId);

    /**
     * 新增或者编辑银行账户信息
     */
    void addOrUpdate(BankAccountRequest bankAccountRequest);
}