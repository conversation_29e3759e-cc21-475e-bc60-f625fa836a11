package com.xhs.reimburse.service;

import com.xhs.reimburse.modal.entity.travel.RelationTravelReimbursementForm;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface TravelApplyExpenseFormRelationService {

    /**
     * 构建差旅申请单与报销单的关系
     *
     * @param formUuid            差旅报销单FId
     * @param travelApplyFormNums 差旅申请单号
     */
    void buildTravelFormRelation(String formUuid, List<String> travelApplyFormNums, String userId);

    /**
     * 根据差旅报销单FId查询关联的差旅申请单号
     *
     * @param formUuid 差旅报销单FId
     * @return 差旅申请单号
     */
    List<String> queryRelationTravelApplyNums(String formUuid, boolean save);

    /**
     * 根据差旅报销单UUID查询差旅申请关联关系
     *
     * @param formUuids 差旅报销单ids
     */
    List<RelationTravelReimbursementForm> queryRelationTravelApplyRelation(List<String> formUuids);

    /**
     * 根据差旅申请单号查询关联的报销单
     *
     * @param travelApplyFormNum 差旅申请单号
     * @return 报销单ids
     */
    List<String> queryRelationReimbursementFormIds(String travelApplyFormNum);

    /**
     * 撤回、拒绝情况下清除差旅申请单与报销单的关系
     *
     * @param formId 报销单
     */
    void clearTravelApplyRelation(String formId);

    /**
     * 差旅申请单变更时 更新差旅申请单的关联关系
     *
     * @param travelApplyFormNum    当前的差旅申请单
     * @param preTravelApplyFormNum 变更前的差旅申请单
     */
    void travelApplyChangeHandle(String travelApplyFormNum, String preTravelApplyFormNum);

    /**
     * 创建差旅申请单与报销单的关系
     *
     * @param reimburseFormId    报销单
     * @param travelApplyFormNum 差旅申请单
     * @return 所有被关联的差旅申请单号
     */
    List<String> createRelationship(String reimburseFormId, String travelApplyFormNum);
}
