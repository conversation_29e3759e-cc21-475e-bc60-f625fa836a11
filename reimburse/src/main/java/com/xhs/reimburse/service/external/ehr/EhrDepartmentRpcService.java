package com.xhs.reimburse.service.external.ehr;

import com.google.common.collect.Lists;
import com.xhs.ehr.rpc.request.QueryDepartmentAuditRequest;
import com.xhs.ehr.rpc.request.QueryDepartmentRequest;
import com.xhs.ehr.rpc.response.AuditInfo;
import com.xhs.ehr.rpc.response.BatchQueryDepartmentAuditResponse;
import com.xhs.ehr.rpc.response.BatchQueryDepartmentResponse;
import com.xhs.ehr.rpc.service.EhrDepartmentService;
import com.xhs.finance.exception.BusinessException;
import com.xhs.reimburse.assembler.mapper.EhrDepartmentMapper;
import com.xhs.reimburse.modal.entity.EhrDepartmentEntity;
import com.xiaohongshu.infra.rpc.base.Context;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class EhrDepartmentRpcService {

    @Resource
    private EhrDepartmentService.Iface ehrDepartmentService;

    @Resource
    private EhrDepartmentMapper ehrDepartmentMapper;

    private static final Long ENTERPRISE_ID = 10000L;

    /**
     * 根据部门ID查询部门信息
     *
     * @param departmentId 部门ID
     * @param withInvalid  是否查询无效部门
     */
    public EhrDepartmentEntity queryDepartmentByDepartmentId(Long departmentId, Boolean withInvalid) {

        try {
            QueryDepartmentRequest request = new QueryDepartmentRequest();
            request.setWihtInvalid(withInvalid);
            request.setEnterpriseId(ENTERPRISE_ID);
            request.setDepartmentIdList(Lists.newArrayList(departmentId));
            BatchQueryDepartmentResponse response = ehrDepartmentService.query_department_by_ids(new Context(), request);
            if (response.success) {
                return ehrDepartmentMapper.baseToEntity(response.getDepartmentInfoList().get(0));
            } else {
                throw new BusinessException(response.error_message);
            }
        } catch (TException e) {
            log.error("EhrDepartmentRpcService queryDepartmentByDepartmentId error:{}", e.getMessage(), e);
        }
        return null;
    }

    /**
     * 根据部门获取链路审批人
     * @param departmentId 部门
     */
    public Map<Long, String> queryDepartmentAuditor(Long departmentId) {
        try {
            QueryDepartmentAuditRequest queryDepartmentAuditRequest = new QueryDepartmentAuditRequest();
            queryDepartmentAuditRequest.setDepartmentIdList(Lists.newArrayList(departmentId));
            queryDepartmentAuditRequest.setEnterpriseId(ENTERPRISE_ID);
            BatchQueryDepartmentAuditResponse response = ehrDepartmentService.query_department_audit_chain_by_ids(new Context(), queryDepartmentAuditRequest);
            log.info("EhrDepartmentRpcService queryDepartmentAuditor response:{}", response);
            if (Objects.nonNull(response) && response.isSuccess() && CollectionUtils.isNotEmpty(response.getDepartmentInfoList())) {
                Map<Long, String> auditorMap = new HashMap<>();
                for (AuditInfo auditInfo : response.getDepartmentInfoList().get(0).getAuditInfoList()) {
                    if (!"-9999".equals(auditInfo.getAuditUserId())) {
                        auditorMap.put(auditInfo.getDepartmentId(), auditInfo.getAuditUserId());
                    }
                }
                return auditorMap;
            }
        } catch (Exception e) {
            log.error("EhrDepartmentRpcService queryDepartmentAuditor error:{}", e.getMessage(), e);
        }
        return null;
    }
}
