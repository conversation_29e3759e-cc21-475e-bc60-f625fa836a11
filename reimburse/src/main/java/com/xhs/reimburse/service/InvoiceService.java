package com.xhs.reimburse.service;

import com.xhs.reimburse.enums.InvoiceExpenseStatusEnum;
import com.xhs.reimburse.enums.InvoiceFormStatusEnum;
import com.xhs.reimburse.enums.InvoiceStatusEnum;
import com.xhs.reimburse.modal.dto.*;
import com.xhs.reimburse.modal.entity.InvoiceEntity;
import com.xhs.reimburse.modal.entity.ReimbursementFormEntity;
import com.xhs.reimburse.modal.request.BatchOcrParseRequest;
import com.xhs.reimburse.modal.request.BatchSaveInvoiceRequest;
import com.xhs.reimburse.modal.request.PageQueryInvoiceRequest;
import com.xhs.reimburse.modal.response.BatchOcrParseResponse;
import com.xhs.reimburse.modal.response.LabelValueResponse;
import com.xiaohongshu.erp.common.framework.page.PageResult;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date :2025/02/12 - 下午2:43
 * @description :
 */
public interface InvoiceService {
    Integer getPendingCount();

    void logicDeleteInvoice(String uuId);

    List<DynamicFormFieldDto> getInvoiceDynamicFormFields(String invoiceType);

    List<LabelValueResponse> getInvoiceTypes();

    PageResult<InvoiceDto> pageQueryInvoice(PageQueryInvoiceRequest request);

    BatchOcrParseResponse batchOcrParse(BatchOcrParseRequest request, boolean fileCheck);

    List<String> batchSaveInvoices(BatchSaveInvoiceRequest request);

    InvoiceDto getInvoiceDtoByUuid(String uuid);

    InvoiceDto getInvoiceForBotByUuid(String uuid);

    InvoiceDto getInvoiceDtoByUuid(String uuid, boolean withDeleted);

    void updateInvoice(InvoiceDto invoiceDto);

    List<InvoiceDto> queryInvoice(List<String> invoiceUuidList);

    BigDecimal calculateInvoicesAmount(List<String> invoiceUuids);

    List<String> queryInvoiceByRule(InvoiceQueryRuleDto invoiceQueryRuleDto);

    List<InvoiceEntity> queryInvoiceEntity(List<String> invoiceUuidList);

    List<String> matchInvoice(InvoiceMatchRuleDto invoiceMatchRuleDto);

    void existCheck(String userId, List<String> invoiceUuIdList);

    void usedCheck(List<String> invoiceUuIdList);

    BatchOcrParseResponse ocrBatchParseInvoice(FileInfoDto fileInfoDto);

    void updateInvoiceListStatus(List<String> invoiceUuids, InvoiceStatusEnum invoiceStatus, InvoiceExpenseStatusEnum invoiceExpenseStatus, InvoiceFormStatusEnum invoiceFormStatusEnum);

    void invoiceDateConvertToDotSeparatedDate(List<InvoiceDto> invoiceDtoList);


    /**
     * @description
     * <AUTHOR>
     * @param uuid 发票uuid
     * @param withDeleted 是否包含删除数据 true包含 false不包含
     * @return InvoiceEntity
     * @date 2025/5/20 16:17
     */
    InvoiceEntity getInvoiceEntityByUuid(String uuid, boolean withDeleted);

    /**
     * @description 获取有效发票-获取不到返回null
     * <AUTHOR>
     * @param uuid
     * @return List<InvoiceEntity>
     * @date 2025/5/23 15:39
     */
    List<InvoiceEntity> getValidInvoiceEntities(String uuid);

    /**
     * 发票维度的校验
     * @param result 校验结果
     * @param invoiceList 发票列表
     */
    void checkReimbursementFormInInvoice(ReimbursementFormCheckResultDto result, List<InvoiceDto> invoiceList);
}