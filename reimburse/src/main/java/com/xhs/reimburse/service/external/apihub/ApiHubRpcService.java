package com.xhs.reimburse.service.external.apihub;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.xhs.oa.office.exception.BusinessException;
import com.xhs.reimburse.modal.request.ApiHubRequest;
import com.xiaohongshu.fls.rpc.enterprise.apihub.request.ApiCallRequest;
import com.xiaohongshu.fls.rpc.enterprise.apihub.response.ApiCallResponse;
import com.xiaohongshu.fls.rpc.enterprise.apihub.service.ApiCallService;
import com.xiaohongshu.infra.rpc.base.Context;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class ApiHubRpcService {

    @Resource
    private ApiCallService.Iface apiCallService;

    public JSONObject callApiGetObject(ApiHubRequest apiHubRequest) {
        String content = callApi(apiHubRequest);
        if (StrUtil.isNotBlank(content)) {
            return JSONObject.parseObject(content);
        }
        return null;
    }

    public JSONArray callApiGetArray(ApiHubRequest apiHubRequest) {
        String content = callApi(apiHubRequest);
        if (StrUtil.isNotBlank(content)) {
            return JSONArray.parseArray(content);
        }
        return null;
    }

    public String callApi(ApiHubRequest apiHubRequest) {
        ApiCallRequest callRequest = new ApiCallRequest();
        try {

            callRequest.setApiCode(apiHubRequest.getApiCode());
            callRequest.setContent(apiHubRequest.getContent());
            callRequest.setBusinessType(apiHubRequest.getBusinessType());
            callRequest.setBusinessSubType(apiHubRequest.getBusinessSubType());
            callRequest.setBusinessElement(apiHubRequest.getBusinessElement());
            ApiCallResponse response = apiCallService.callApi(new Context(), callRequest);
            if (response.getBaseResp().success) {
                return response.getContent();
            } else {
                throw new BusinessException(response.getBaseResp().getMessage());
            }
        } catch (Exception e) {
            log.error("ApiHubRpcService callApi error: {}", e.getMessage(), e);
        }
        return null;
    }

}
