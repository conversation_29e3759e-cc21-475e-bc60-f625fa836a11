package com.xhs.reimburse.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.xhs.finance.sso.UserInfoBag;
import com.xhs.reimburse.assembler.TravelApplyAssembler;
import com.xhs.reimburse.constant.ApiHubConstant;
import com.xhs.reimburse.modal.dto.travel.ApiHubTravelApplyInfoDto;
import com.xhs.reimburse.modal.dto.travel.ApiHubTravelScheduleResponse;
import com.xhs.reimburse.modal.dto.travel.ApiHubTravelTravelPlaceConvertResponse;
import com.xhs.reimburse.modal.dto.travel.TravelApplyFormInfoDto;
import com.xhs.reimburse.modal.dto.travel.TravelPlaceDto;
import com.xhs.reimburse.modal.request.ApiHubRequest;
import com.xhs.reimburse.modal.request.PageQueryTravelApplyRequest;
import com.xhs.reimburse.modal.request.QueryTravelScheduleRequest;
import com.xhs.reimburse.modal.response.TravelApplySummaryInfo;
import com.xhs.reimburse.service.TravelApplyService;
import com.xhs.reimburse.service.external.apihub.ApiHubRpcService;
import com.xiaohongshu.erp.common.framework.page.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> 陈执
 * @createDate : 2025-03-08 14:52:33
 * @Description :
 **/
@Service
@Slf4j
public class TravelApplyServiceImpl implements TravelApplyService {

    @Resource
    private ApiHubRpcService apiHubRpcService;

    @Resource
    private TravelApplyAssembler travelApplyAssembler;

    @Override
    public PageResult<TravelApplySummaryInfo> pageQueryTravelApplyFormSummary(PageQueryTravelApplyRequest request) {
        PageResult<TravelApplySummaryInfo> pageResult = new PageResult();
        if (StringUtils.isBlank(request.getUserId())) {
            request.setUserId(UserInfoBag.get().getUserId());
        }
        ApiHubRequest apiHubRequest = ApiHubRequest.builder().apiCode(ApiHubConstant.GET_TRAVLEPAGEINFO_BY_PARAM)
                .businessType(ApiHubConstant.OA_BUSINESS_TYPE).businessSubType(ApiHubConstant.OA_BUSINESS_SUB_TYPE)
                .businessElement(ApiHubConstant.OA_ELEMENT_TRAVEL).content(JSON.toJSONString(request))
                .build();

        JSONObject resp = apiHubRpcService.callApiGetObject(apiHubRequest);
        if (Objects.isNull(resp)) {
            return pageResult;
        }
        ApiHubTravelApplyInfoDto dto = resp.toJavaObject(ApiHubTravelApplyInfoDto.class);
        if (Objects.isNull(dto)) {
            return pageResult;
        }
        pageResult.setTotalPage(dto.getTotalPage());
        pageResult.setTotal(dto.getTotal());
        pageResult.setPageSize(dto.getPageSize());
        pageResult.setPageNum(dto.getPageNum());
        if (Objects.nonNull(dto.getTravelBasicInfos())) {
            List<TravelApplySummaryInfo> infos = travelApplyAssembler.setTravelTravelerName(
                    dto.getTravelBasicInfos().toJavaList(TravelApplySummaryInfo.class)
            );
            infos = infos.subList(Math.min(infos.size(), (dto.getPageNum() - 1) * dto.getPageSize()),
                    Math.min(infos.size(), dto.getPageNum() * dto.getPageSize()));
            pageResult.setList(infos);
        }
        return pageResult;
    }

    @Override
    public List<TravelApplySummaryInfo> queryTravelApplyFormSummary(PageQueryTravelApplyRequest request) {
        if (StringUtils.isBlank(request.getUserId())) {
            request.setUserId(UserInfoBag.get().getUserId());
        }
        ApiHubRequest apiHubRequest = ApiHubRequest.builder().apiCode(ApiHubConstant.GET_TRAVLEINFOS_BY_PARAM)
                .businessType(ApiHubConstant.OA_BUSINESS_TYPE).businessSubType(ApiHubConstant.OA_BUSINESS_SUB_TYPE)
                .businessElement(ApiHubConstant.OA_ELEMENT_TRAVEL).content(JSON.toJSONString(request))
                .build();

        JSONArray array = apiHubRpcService.callApiGetArray(apiHubRequest);
        if (Objects.isNull(array)) {
            return null;
        }

        return travelApplyAssembler.setTravelTravelerName(array.toJavaList(TravelApplySummaryInfo.class));
    }

    /**
     * 查询差旅申请单行程详情
     *
     * @param formNums 差旅申请单号
     * @return 差旅申请单行程详情
     */
    @Override
    public List<TravelApplyFormInfoDto> queryTravelApplyScheduleDetail(List<String> formNums) {
        if (CollUtil.isEmpty(formNums)) {
            return null;
        }
        ApiHubRequest apiHubRequest = ApiHubRequest.builder().apiCode(ApiHubConstant.GET_TRAVEL_SCHEDULE_DETAIL)
                .businessType(ApiHubConstant.OA_BUSINESS_TYPE).businessSubType(ApiHubConstant.OA_BUSINESS_SUB_TYPE)
                .businessElement(ApiHubConstant.OA_ELEMENT_TRAVEL)
                .content(JSON.toJSONString(new QueryTravelScheduleRequest(formNums)))
                .build();

        JSONObject callApiGetObject = apiHubRpcService.callApiGetObject(apiHubRequest);
        log.info("queryTravelApplyScheduleDetail formNums:{},callApiGetObject:{}", formNums, callApiGetObject);
        if (Objects.isNull(callApiGetObject)) {
            return null;
        }
        ApiHubTravelScheduleResponse response = callApiGetObject.toJavaObject(ApiHubTravelScheduleResponse.class);
        if (BooleanUtil.isTrue(response.isSuccess()) && CollUtil.isNotEmpty(response.getTravelApplyFormInfos())) {
            return response.getTravelApplyFormInfos();
        }

        return null;
    }

    /**
     * 差旅机场三字码code转换成城市ID
     *
     * @param travelPlaceDto 差旅申请单号
     */
    @Override
    public void convertPlaceCode(TravelPlaceDto travelPlaceDto) {
        if (Objects.isNull(travelPlaceDto)) {
            return;
        }
        Map<Object, Object> content = new HashMap<>();
        content.put("airPortCode", travelPlaceDto.getPlaceCode());
        content.put("cityShowName", travelPlaceDto.getShowPlaceName());
        ApiHubRequest apiHubRequest = ApiHubRequest.builder().apiCode(ApiHubConstant.CONVERTER_TRAVEL_PLACE_CODE4AIR)
                .businessType(ApiHubConstant.OA_BUSINESS_TYPE).businessSubType(ApiHubConstant.OA_BUSINESS_SUB_TYPE)
                .businessElement(ApiHubConstant.OA_ELEMENT_TRAVEL)
                .content(JSON.toJSONString(content))
                .build();

        JSONObject callApiGetObject = apiHubRpcService.callApiGetObject(apiHubRequest);
        if (Objects.isNull(callApiGetObject)) {
            return;
        }
        ApiHubTravelTravelPlaceConvertResponse response
                = callApiGetObject.toJavaObject(ApiHubTravelTravelPlaceConvertResponse.class);
        if (BooleanUtil.isTrue(response.isSuccess()) && StrUtil.isNotBlank(response.getPlaceCode())) {
            travelPlaceDto.setPlaceCode(response.getPlaceCode());
        }
    }
}