package com.xhs.reimburse.service.external.company;

import com.alibaba.fastjson2.JSON;
import com.xhs.finance.exception.BusinessException;
import com.xhs.oa.office.rpc.CompanyRpcService;
import com.xhs.reimburse.modal.dto.CheckNeedPayDto;
import com.xhs.reimburse.modal.dto.PaymentInfoDto;
import com.xiaohongshu.fls.rpc.oacommon.company.enums.AccountTypeEnum;
import com.xiaohongshu.fls.rpc.oacommon.company.request.NeedPaymentCheckRequest;
import com.xiaohongshu.fls.rpc.oacommon.company.response.ExpenseInfo;
import com.xiaohongshu.fls.rpc.oacommon.company.response.NeedPaymentCheckResponse;
import com.xiaohongshu.fls.rpc.oacommon.company.response.NeedPaymentResultDTO;
import com.xiaohongshu.infra.rpc.base.Context;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.UUID;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class OaCompanyRpcService {

    @Autowired
    private CompanyRpcService companyRpcService;


    public PaymentInfoDto checkNeedPayment(CheckNeedPayDto checkParam) {

        NeedPaymentCheckResponse checkResponse = null;
        try {

            NeedPaymentCheckRequest checkRequest = convertRequest(checkParam);
            log.info("OaCompanyRpcService checkNeedPayment checkRequest:{}", JSON.toJSONString(checkRequest));
            checkResponse = companyRpcService.getCompanyService().needPaymentCheck(new Context(), checkRequest);
            log.info("OaCompanyRpcService checkNeedPayment checkResponse:{}", JSON.toJSONString(checkResponse));

            if (!checkResponse.success) {
                throw new BusinessException("判断是否需要银企直连超时，请联系OA薯");
            }
            return convertResult(checkResponse.getNeedPaymentResultDTO());
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.info("OaCompanyRpcService checkNeedPayment error :{}", e.getMessage());
        }

        return null;
    }

    private NeedPaymentCheckRequest convertRequest(CheckNeedPayDto checkParam) {
        NeedPaymentCheckRequest request = new NeedPaymentCheckRequest();
        request.setReqId(UUID.randomUUID().toString().replaceAll("-", ""));
        request.setCompanyNameList(checkParam.getCompanyNameList());
        request.setFormType(checkParam.getFormType());
        request.setPaymentWayList(checkParam.getPaymentTypeList());
        AccountTypeEnum accountType = AccountTypeEnum.valueOf(checkParam.getAccountType());
        request.setAccountType(accountType);
        return request;
    }

    private PaymentInfoDto convertResult(NeedPaymentResultDTO needPaymentResultDTO) {
        PaymentInfoDto result = new PaymentInfoDto();
        result.setCanBankEnterprise(needPaymentResultDTO.canBankEnterprise);
        result.setMsg(needPaymentResultDTO.getError_msg());
        result.setSubjectCode(needPaymentResultDTO.getSubjectCode());
        result.setPaymentAbility(needPaymentResultDTO.getPaymentAbility().getValue());
        result.setPaymentType(needPaymentResultDTO.getPaymentType());
        return result;
    }

    public ExpenseInfo queryExpenseInfos(String userId) {
        return companyRpcService.queryExpenseInfos(userId);
    }

}
