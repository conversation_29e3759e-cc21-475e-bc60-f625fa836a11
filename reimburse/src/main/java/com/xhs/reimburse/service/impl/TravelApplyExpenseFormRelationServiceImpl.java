package com.xhs.reimburse.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.xhs.oa.office.utils.AssertHelper;
import com.xhs.reimburse.assembler.TravelReimbursementFormRelationConverter;
import com.xhs.reimburse.constant.CommonConstant;
import com.xhs.reimburse.mapper.TReimbursementFormMapper;
import com.xhs.reimburse.mapper.TRelationTravelReimbursementFormMapper;
import com.xhs.reimburse.modal.entity.ReimbursementFormEntity;
import com.xhs.reimburse.modal.entity.travel.RelationTravelReimbursementForm;
import com.xhs.reimburse.modal.entity.travel.RelationTravelReimbursementFormExample;
import com.xhs.reimburse.modal.request.ReimburseFormRequest;
import com.xhs.reimburse.service.ReimbursementFormService;
import com.xhs.reimburse.service.TravelApplyExpenseFormRelationService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class TravelApplyExpenseFormRelationServiceImpl implements TravelApplyExpenseFormRelationService {

    @Resource
    private ReimbursementFormService reimbursementFormService;

    @Resource
    private TRelationTravelReimbursementFormMapper relationTravelReimbursementFormMapper;

    @Resource
    private TReimbursementFormMapper tReimbursementFormMapper;

    @Resource
    private TravelReimbursementFormRelationConverter converter;

    /**
     * 构建差旅申请单与报销单的关系
     *
     * @param formUuid            差旅报销单FId
     * @param travelApplyFormNums 差旅申请单号
     * @param userId              创建人ID
     */
    @Override
    public void buildTravelFormRelation(String formUuid, List<String> travelApplyFormNums, String userId) {

        AssertHelper.notBlank(formUuid, "构建差旅关系失败：未知报销单");

        if (CollUtil.isEmpty(travelApplyFormNums)) {
            return;
        }

        //新的关联关系
        relationTravelReimbursementFormMapper.batchInsert(converter.convert2Entity(formUuid, travelApplyFormNums, userId));
    }

    /**
     * 根据差旅报销单FId查询关联的差旅申请单号
     *
     * @param formUuid 差旅报销单FId
     * @return 差旅申请单号
     */
    @Override
    public List<String> queryRelationTravelApplyNums(String formUuid, boolean save) {
        if (StrUtil.isBlank(formUuid)) {
            return null;
        }

        List<String> travelApplyNums = relationTravelReimbursementFormMapper.selectTravelApplyNumsByFId(formUuid);
        if (!save) {
            return travelApplyNums;
        }

        List<ReimbursementFormEntity> entities = reimbursementFormService.queryReimbursementFormEntity(Arrays.asList(formUuid));
        if (CollUtil.isNotEmpty(entities)) {
            ReimbursementFormEntity entity = entities.get(0);
            ReimburseFormRequest request = JSONObject.parseObject(entity.getFormContent(), ReimburseFormRequest.class);
            return request.getTravelApplyFormNums();
        } else {
            return travelApplyNums;
        }
    }

    /**
     * 根据差旅报销单UUID查询差旅申请关联关系
     *
     * @param formUuids 差旅报销单ids
     */
    @Override
    public List<RelationTravelReimbursementForm> queryRelationTravelApplyRelation(List<String> formUuids) {
        if (CollUtil.isEmpty(formUuids)) {
            return null;
        }

        RelationTravelReimbursementFormExample example = new RelationTravelReimbursementFormExample();
        example.createCriteria()
                .andIsValidEqualTo(CommonConstant.VALID)
                .andReimbursementIdIn(formUuids);

        return relationTravelReimbursementFormMapper.selectByExample(example);
    }

    @Override
    public List<String> queryRelationReimbursementFormIds(String travelApplyFormNum) {

        List<RelationTravelReimbursementForm> relations = queryRelationsByTravelApplyFormNum(travelApplyFormNum);

        if (CollectionUtils.isNotEmpty(relations)) {
            return relations.stream().map(RelationTravelReimbursementForm::getReimbursementId).collect(Collectors.toList());
        }

        return null;
    }

    /**
     * 撤回、拒绝情况下清除差旅申请单与报销单的关系
     *
     * @param formId 报销单
     */
    @Override
    public void clearTravelApplyRelation(String formId) {

        if (StrUtil.isNotBlank(formId)) {

            RelationTravelReimbursementFormExample example = new RelationTravelReimbursementFormExample();
            example.createCriteria()
                    .andIsValidEqualTo(CommonConstant.VALID)
                    .andReimbursementIdEqualTo(formId);

            RelationTravelReimbursementForm invalid = new RelationTravelReimbursementForm(CommonConstant.INVALID);

            relationTravelReimbursementFormMapper.updateByExampleSelective(invalid, example);
        }
    }

    /**
     * 差旅申请单变更时 更新差旅申请单的关联关系
     *
     * @param newTravelApplyFormNum 当前的差旅申请单
     * @param preTravelApplyFormNum 变更前的差旅申请单
     */
    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "oaOfficeTransactionManager")
    public void travelApplyChangeHandle(String newTravelApplyFormNum, String preTravelApplyFormNum) {

        List<RelationTravelReimbursementForm> relations = queryRelationsByTravelApplyFormNum(preTravelApplyFormNum);
        if (CollectionUtils.isEmpty(relations)) {
            return;
        }
        List<Long> ids = new ArrayList<>();
        List<String> fids = new ArrayList<>();

        relations.forEach(relation -> {
            ids.add(relation.getId());
            fids.add(relation.getReimbursementId());
            relation.setTravelFormNum(newTravelApplyFormNum);
        });
        //将原有关系置为无效
        relationTravelReimbursementFormMapper.batchInvalid(ids);
        //插入新的关联数据
        relationTravelReimbursementFormMapper.batchInsert(relations);

        List<ReimbursementFormEntity> entities = reimbursementFormService.queryReimbursementFormEntity(fids);
        if (CollUtil.isEmpty(entities)) {
            return;
        }

        for (ReimbursementFormEntity entity : entities) {

            JSONObject data = JSON.parseObject(entity.getFormContent());
            JSONArray travelNumArray = data.getJSONArray("travelApplyFormNums");
            if (CollUtil.isEmpty(travelNumArray)) {
                continue;
            }

            List<String> travelApplyFormNums = travelNumArray.toJavaList(String.class);
            travelApplyFormNums.add(newTravelApplyFormNum);
            travelApplyFormNums.remove(preTravelApplyFormNum);
            data.put("travelApplyFormNums", travelApplyFormNums);
            entity.setFormContent(data.toJSONString());

            tReimbursementFormMapper.updateByPrimaryKeyWithBLOBs(entity);
        }
    }

    @Override
    public List<String> createRelationship(String reimburseFormId, String travelApplyFormNum) {

        List<String> travelApplyFormNums = null;
        List<RelationTravelReimbursementForm> relations = this.queryRelationTravelApplyRelation(Arrays.asList(reimburseFormId));
        if (CollUtil.isNotEmpty(relations)) {
            travelApplyFormNums = relations.stream()
                    .map(RelationTravelReimbursementForm::getTravelFormNum).collect(Collectors.toList());

            if (!travelApplyFormNums.contains(travelApplyFormNum)) {
                RelationTravelReimbursementForm relation = new RelationTravelReimbursementForm(reimburseFormId, travelApplyFormNum);
                relationTravelReimbursementFormMapper.insertSelective(relation);
                travelApplyFormNums.add(travelApplyFormNum);
            }

            return travelApplyFormNums;
        } else {
            List<ReimbursementFormEntity> entities
                    = reimbursementFormService.queryReimbursementFormEntity(Arrays.asList(reimburseFormId));
            if (CollUtil.isEmpty(entities)) {
                return travelApplyFormNums;
            }

            ReimbursementFormEntity entity = entities.get(0);
            JSONObject data = JSON.parseObject(entity.getFormContent());
            JSONArray travelNumArray = data.getJSONArray("travelApplyFormNums");
            travelApplyFormNums = CollUtil.isEmpty(travelNumArray) ? Arrays.asList(travelApplyFormNum) : travelNumArray.toJavaList(String.class);

            if (!travelApplyFormNums.contains(travelApplyFormNum)) {
                travelApplyFormNums.add(travelApplyFormNum);
            }
            data.put("travelApplyFormNums", travelApplyFormNums);
            entity.setFormContent(data.toJSONString());
            tReimbursementFormMapper.updateByPrimaryKeyWithBLOBs(entity);
        }

        return travelApplyFormNums;
    }

    private List<RelationTravelReimbursementForm> queryRelationsByTravelApplyFormNum(String travelApplyFormNum) {

        if (StrUtil.isBlank(travelApplyFormNum)) {
            return null;
        }

        RelationTravelReimbursementFormExample example = new RelationTravelReimbursementFormExample();
        example.createCriteria()
                .andIsValidEqualTo(CommonConstant.VALID)
                .andTravelFormNumEqualTo(travelApplyFormNum);

        return relationTravelReimbursementFormMapper.selectByExample(example);
    }
}
