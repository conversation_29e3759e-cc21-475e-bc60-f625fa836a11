package com.xhs.reimburse.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.xhs.reimburse.constant.CommonConstant;
import com.xhs.reimburse.enums.OaFormAuditStatusEnum;
import com.xhs.reimburse.enums.travel.ExpenseCheckItemEnum;
import com.xhs.reimburse.modal.dto.ExpenseDto;
import com.xhs.reimburse.modal.dto.travel.CheckResultItemDto;
import com.xhs.reimburse.modal.dto.travel.ItineraryCheckResultDto;
import com.xhs.reimburse.modal.entity.ReimbursementFormEntity;
import com.xhs.reimburse.modal.request.PageQueryTravelApplyRequest;
import com.xhs.reimburse.modal.request.RedFlowProcessSaveOrStartRequest;
import com.xhs.reimburse.modal.request.ReimburseFormRequest;
import com.xhs.reimburse.modal.response.*;
import com.xhs.reimburse.service.ReimbursementFormService;
import com.xhs.reimburse.service.TravelApplyExpenseFormRelationService;
import com.xhs.reimburse.service.TravelApplyService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @desc 差旅报销单
 */
@Service(value = "CLBXD" + CommonConstant.FORM_COMPONENT_SUFFIX)
public class TravelFormService extends ReimburseFormFlowAdapter {

    @Resource
    private TravelApplyService travelApplyService;

    @Resource
    private ReimbursementFormService reimbursementFormService;

    @Resource
    private TravelApplyExpenseFormRelationService travelApplyExpenseFormRelationService;

    @Override
    public ReimbursementFormResponse buildReimbursementForm(ReimbursementFormEntity reimburseForm, ReimburseFormRequest formRequest) {
        TravelFormResponse response = (TravelFormResponse) super.buildReimbursementForm(reimburseForm, formRequest);

        List<String> travelApplyFormNums
                = travelApplyExpenseFormRelationService.queryRelationTravelApplyNums(reimburseForm.getUuid(), false);
        //如果DB没有 有效关联关系（撤回、拒绝的情况），取表单里的关联单据
        travelApplyFormNums = CollUtil.isEmpty(travelApplyFormNums) ? formRequest.getTravelApplyFormNums() : travelApplyFormNums;

        if (CollUtil.isNotEmpty(travelApplyFormNums)) {

            PageQueryTravelApplyRequest travelApplyRequest = new PageQueryTravelApplyRequest(travelApplyFormNums
                    , reimburseForm.getCreatorNo(), OaFormAuditStatusEnum.getValidStatusCode());
            List<TravelApplySummaryInfo> travelInfos
                    = travelApplyService.queryTravelApplyFormSummary(travelApplyRequest);

            response.setTravelApplyFormNums(travelApplyFormNums);
            response.setTravelScheduleSummaryInfos(travelInfos);
        }

        return response;
    }

    @Override
    public ReimbursementFormPrintResponse buildFormPrintResponse(ReimbursementFormEntity reimbursementForm) {

        TravelFormPrintResponse formPrintResponse = (TravelFormPrintResponse) super.buildFormPrintResponse(reimbursementForm);
        if (Objects.isNull(formPrintResponse)) {
            return null;
        }

        List<String> travelApplyNums
                = travelApplyExpenseFormRelationService.queryRelationTravelApplyNums(reimbursementForm.getUuid(), false);
        if (CollUtil.isEmpty(travelApplyNums)) {

            //如果DB没有 有效关联关系（撤回、拒绝的情况），取表单里的关联单据
            ReimburseFormRequest reimburseFormRequest
                    = JSONObject.parseObject(reimbursementForm.getFormContent(), ReimburseFormRequest.class);
            travelApplyNums = reimburseFormRequest.getTravelApplyFormNums();
        }

        if (CollUtil.isNotEmpty(travelApplyNums)) {

            PageQueryTravelApplyRequest travelApplyRequest = new PageQueryTravelApplyRequest(travelApplyNums
                    , reimbursementForm.getCreatorNo(), OaFormAuditStatusEnum.getValidStatusCode());
            List<TravelApplySummaryInfo> travelInfos
                    = travelApplyService.queryTravelApplyFormSummary(travelApplyRequest);

            formPrintResponse.setTravelScheduleSummaryInfos(travelInfos);
            formPrintResponse.setTravelApplyFormNums(travelApplyNums);
        }

        return formPrintResponse;
    }

    @Override
    public String checkFormData(ReimburseFormRequest formRequest) {
        if (CollUtil.isEmpty(formRequest.getTravelApplyFormNums())) {
            return "请关联差旅申请单";
        }
        return "";
    }

    @Override
    public RedFlowProcessSaveOrStartRequest buildVariableMap(ReimburseFormRequest formRequest, boolean save) {
        RedFlowProcessSaveOrStartRequest request = super.buildVariableMap(formRequest, save);
        if (save) {
            return request;
        }

        Map<String, Object> variableMap = request.getVariableMap();
        for (ExpenseDto expense : formRequest.getExpenses()) {

            ItineraryCheckResultDto checkResultDto = expense.getItineraryCheckResultDto();
            if (Objects.nonNull(checkResultDto)) {

                List<CheckResultItemDto> allowSubmissionCheckResults = checkResultDto.getAllowSubmissionCheckResultList();
                if (CollUtil.isEmpty(allowSubmissionCheckResults)) {
                    continue;
                }
                if (allowSubmissionCheckResults.stream().anyMatch(item -> ExpenseCheckItemEnum.seatOverStandard(item.getCode()))) {
                    //是否座位超预算
                    variableMap.put("travelSeatStandardExceed", String.valueOf(true));
                    //差旅标准是否超标
                    variableMap.put("travelStandardExceed", String.valueOf(true));
                    break;
                } else if (allowSubmissionCheckResults.stream().anyMatch(item -> ExpenseCheckItemEnum.overStandard(item.getCode()))) {
                    //差旅标准是否超标
                    variableMap.put("travelStandardExceed", String.valueOf(true));
                }
            }
        }

        return request;
    }

    /**
     * 获取表单校验状态
     *
     * @param request 请求信息
     * @return 是否可提报 true 可提报
     */
    @Override
    public boolean getFormCheckedStatus(ReimburseFormRequest request) {

        if (!super.getFormCheckedStatus(request)) {
            return false;
        }

        return StrUtil.isBlank(checkFormData(request));
    }

    @Override
    public void submitAfterAction(ReimbursementFormEntity reimburseForm, ReimburseFormRequest formRequest, boolean save) {

        super.submitAfterAction(reimburseForm, formRequest, save);

        if (!save) {
            travelApplyExpenseFormRelationService.buildTravelFormRelation(reimburseForm.getUuid(),
                    formRequest.getTravelApplyFormNums(), formRequest.getCreatorNo());
        }
    }

    /**
     * 审批人「驳回」至「发起人提交」节点
     *
     * @param formNum 单据号
     */
    @Override
    public void formRefuse(String formNum) {

        super.formRefuse(formNum);

        clearTravelApplyRelationByFormNum(formNum);
    }

    /**
     * 发起人「撤回」单据
     *
     * @param formNum 单据号
     */
    @Override
    public void formWithdrawal(String formNum) {

        super.formWithdrawal(formNum);

        clearTravelApplyRelationByFormNum(formNum);
    }

    /**
     * 撤回、拒绝时根据单据号作废关联关系
     *
     * @param formNum 单据号
     */
    private void clearTravelApplyRelationByFormNum(String formNum) {
        ReimbursementFormEntity formEntity
                = reimbursementFormService.getReimbursementFormByFormNum(formNum, false);

        travelApplyExpenseFormRelationService.clearTravelApplyRelation(formEntity.getUuid());
    }
}
