package com.xhs.reimburse.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xhs.reimburse.enums.*;
import com.xhs.reimburse.mapper.RelationExpenseInvoiceMapper;
import com.xhs.reimburse.modal.dto.ExpenseDto;
import com.xhs.reimburse.modal.dto.InvoiceDto;
import com.xhs.reimburse.modal.entity.ExpenseEntity;
import com.xhs.reimburse.modal.entity.RelationExpenseInvoiceEntity;
import com.xhs.reimburse.service.ExpenseService;
import com.xhs.reimburse.service.InvoiceService;
import com.xhs.reimburse.service.RelationExpenseInvoiceService;
import com.xhs.reimburse.utils.StreamUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName RelationExpenseInvoiceServiceImpl.java
 * @createTime 2025年02月12日 19:02:00
 */
@Slf4j
@Service
public class RelationExpenseInvoiceServiceImpl extends ServiceImpl<RelationExpenseInvoiceMapper, RelationExpenseInvoiceEntity> implements RelationExpenseInvoiceService {
    // service
    @Resource
    private InvoiceService invoiceService;
    @Resource
    private ExpenseService expenseService;

    // mapper
    @Resource
    private RelationExpenseInvoiceMapper relationExpenseInvoiceMapper;

    /**
     * 费用新增发票
     *
     * @param userId          用户ID
     * @param expenseUuid     费用ID
     * @param invoiceUuidList 发票ID列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "oaOfficeTransactionManager")
    public void updateExpenseInvoiceRelation(String userId, String expenseUuid, List<String> invoiceUuidList) {
        // 费用校验
        expenseService.existCheck(userId, Collections.singletonList(expenseUuid));

        // 清空原有关联发票
        clearRelationByExpenseUuid(expenseUuid);

        // 发票校验
        if (CollectionUtils.isEmpty(invoiceUuidList)) {
            return;
        }
        invoiceService.existCheck(userId, invoiceUuidList);

        // 批量插入
        invoiceUuidList.stream()
                .map(invoiceUuId -> {
                    RelationExpenseInvoiceEntity entity = new RelationExpenseInvoiceEntity();
                    entity.setExpenseUuid(expenseUuid);
                    entity.setInvoiceUuid(invoiceUuId);
                    return entity;
                })
                .forEach(relationExpenseInvoiceMapper::insert);

        // 费用是否关联了报销单，依次修改新发票的invoiceFormStatus
        List<ExpenseEntity> expenseEntityList = expenseService.queryExpenseEntity(Collections.singletonList(expenseUuid));
        ExpenseEntity expenseEntity = expenseEntityList.stream().findFirst().orElse(new ExpenseEntity());
        InvoiceFormStatusEnum invoiceFormStatusEnum = Objects.equals(ExpenseFormStatusEnum.YGL.getCode(), expenseEntity.getExpenseFormStatus()) ? InvoiceFormStatusEnum.YGL : InvoiceFormStatusEnum.WGL;

        // 修改现有关联发票
        invoiceService.updateInvoiceListStatus(invoiceUuidList, null, InvoiceExpenseStatusEnum.YGL, invoiceFormStatusEnum);

        // 修改费用动态表单中冗余的relationInvoice字段
        expenseService.updateExpenseRelationFild(expenseUuid, invoiceUuidList);
    }

    @Override
    public void clearRelationByExpenseUuid(String expenseUuid) {
        LambdaQueryWrapper<RelationExpenseInvoiceEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(RelationExpenseInvoiceEntity::getExpenseUuid, expenseUuid);
        List<RelationExpenseInvoiceEntity> relationList = relationExpenseInvoiceMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(relationList)) {
            return;
        }

        log.info("clearExpenseInvoiceRelation expenseUuid={}, relationList={}", expenseUuid, relationList);
        List<Long> ids = StreamUtil.toList(relationList, RelationExpenseInvoiceEntity::getId);
        relationExpenseInvoiceMapper.deleteBatchIds(ids);

        // 修改发票状态 - 由于可能之前发票绑定了报销单，所以解绑之后需要修改invoiceFormStatus
        List<String> invoiceUuidList = relationList.stream().map(RelationExpenseInvoiceEntity::getInvoiceUuid).collect(Collectors.toList());
        invoiceService.updateInvoiceListStatus(invoiceUuidList, null, InvoiceExpenseStatusEnum.WGL, InvoiceFormStatusEnum.WGL);
    }

    @Override
    public boolean confirmNeedSubmitInvoice(List<ExpenseDto> expenses) {

        if (CollectionUtils.isEmpty(expenses)) {
            return false;
        }

        List<String> overseasFirstSubjects = ExpenseFirstSubjectEnum.overseasFirstSubject();
        List<String> overseasSecondSubjects = ExpenseSecondSubjectEnum.overseasSecondSubject();
        List<InvoiceDto> invoices = new ArrayList<>();

        expenses.forEach(expense -> {
            if (ReimburseTypeEnum.CLBXD.getType().equals(expense.getFormType())) {
                //海外科目电、纸质发票不需要交票校验
                if (overseasFirstSubjects.contains(expense.getFirstSubject())
                        || overseasSecondSubjects.contains(expense.getSecondSubject())) {
                    return;
                }
            }
            if (CollectionUtils.isNotEmpty(expense.getRelationInvoiceList())) {
                invoices.addAll(expense.getRelationInvoiceList());
            }
        });

        if (CollectionUtils.isEmpty(invoices)) {
            return false;
        }
        return invoices.stream().anyMatch(e -> InvoiceTypeEnum.paperTickets().contains(e.getTicketType()));
    }

    @Override
    public List<String> getExpenseListAllInvoice(List<String> expenseUuIdList) {
        if (CollectionUtils.isEmpty(expenseUuIdList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<RelationExpenseInvoiceEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(RelationExpenseInvoiceEntity::getExpenseUuid, expenseUuIdList);
        List<RelationExpenseInvoiceEntity> expenseInvoiceRelationList = relationExpenseInvoiceMapper.selectList(queryWrapper);
        return expenseInvoiceRelationList.stream()
                .map(RelationExpenseInvoiceEntity::getInvoiceUuid)
                .collect(Collectors.toList());
    }

    @Override
    public List<InvoiceDto> getExpenseAllInvoice(String expenseUuId) {
        if (null == expenseUuId) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<RelationExpenseInvoiceEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(RelationExpenseInvoiceEntity::getExpenseUuid, expenseUuId);
        List<RelationExpenseInvoiceEntity> expenseInvoiceRelationList = relationExpenseInvoiceMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(expenseInvoiceRelationList)) {
            return new ArrayList<>();
        }

        List<String> invoiceUuidList = expenseInvoiceRelationList.stream().map(RelationExpenseInvoiceEntity::getInvoiceUuid).collect(Collectors.toList());
        return invoiceService.queryInvoice(invoiceUuidList);
    }

    @Override
    public String queryInvoiceBelongExpense(String invoiceUuid) {
        if (null == invoiceUuid) {
            return "";
        }
        LambdaQueryWrapper<RelationExpenseInvoiceEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(RelationExpenseInvoiceEntity::getInvoiceUuid, invoiceUuid);
        List<RelationExpenseInvoiceEntity> expenseInvoiceRelationList = relationExpenseInvoiceMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(expenseInvoiceRelationList) || expenseInvoiceRelationList.size() != 1) {
            return "";
        }
        return expenseInvoiceRelationList.get(0).getExpenseUuid();
    }

    @Override
    public List<String> queryRelationExpenseUuids(String invoiceUuid) {
        LambdaQueryWrapper<RelationExpenseInvoiceEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(RelationExpenseInvoiceEntity::getInvoiceUuid, invoiceUuid);
        return relationExpenseInvoiceMapper.selectList(queryWrapper)
                .stream().map(RelationExpenseInvoiceEntity::getExpenseUuid).collect(Collectors.toList());
    }
}
