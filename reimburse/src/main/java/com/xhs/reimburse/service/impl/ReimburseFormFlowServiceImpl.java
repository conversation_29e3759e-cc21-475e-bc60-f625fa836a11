package com.xhs.reimburse.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.xhs.finance.sso.UserInfoBag;
import com.xhs.finance.sso.model.UserInfo;
import com.xhs.finance.utils.AssertUtils;
import com.xhs.oa.office.exception.BusinessException;
import com.xhs.oa.office.threadPool.ThreadPoolManager;
import com.xhs.oa.office.utils.AssertHelper;
import com.xhs.reimburse.assembler.CompanyDTOConverter;
import com.xhs.reimburse.enums.ReimbursementFormStatusEnum;
import com.xhs.reimburse.modal.dto.*;
import com.xhs.reimburse.assembler.ReimbursementFormAssembler;
import com.xhs.reimburse.constant.ErrorConstant;
import com.xhs.reimburse.enums.ReimbursementFormStatusEnum;
import com.xhs.reimburse.modal.dto.ExpenseDto;
import com.xhs.reimburse.modal.dto.PaymentInfoDto;
import com.xhs.reimburse.modal.dto.ReimbursementFormSimpleDto;
import com.xhs.reimburse.modal.entity.ReimbursementFormEntity;
import com.xhs.reimburse.modal.request.ReimburseFormRequest;
import com.xhs.reimburse.modal.response.ReimbursementFormPrintResponse;
import com.xhs.reimburse.modal.response.ReimbursementFormResponse;
import com.xhs.reimburse.service.*;
import com.xhs.reimburse.service.external.company.OaCompanyRpcService;
import com.xhs.reimburse.service.external.redflow.RedFlowRpcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class ReimburseFormFlowServiceImpl implements ReimburseFormFlowService {

    @Resource
    private ExpenseService expenseService;

    @Resource
    private RedFlowRpcService redFlowRpcService;

    @Resource
    private OaCompanyRpcService oaCompanyRpcService;

    @Resource
    private CompanyDTOConverter companyDtoConverter;

    @Resource
    private ReimbursementFormAssembler reimburseFormConverter;

    @Resource
    private ReimbursementFormService reimbursementFormService;

    @Resource
    private ReimburseFormFlowComponentServiceFactory componentServiceFactory;

    /**
     * 保存或提交报销单
     *
     * @param formRequest 提交请求
     * @param save        是否是保存操作 true:保存 false:提交
     * @return 单据表单详情快照
     */
    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "oaOfficeTransactionManager")
    public ReimbursementFormResponse saveOrSubmitReimbursementForm(ReimburseFormRequest formRequest, boolean save) {

        String formNum = formRequest.getFormNum();
        //0 单据是否可以提交校验
        checkFormStatus(formNum);

        ReimburseFormFlowComponentService formComponentService
                = componentServiceFactory.getComponentServiceByFormType(formRequest.getFormType());
        //0.1 保存时自动填充下基础信息
        formComponentService.fillBaseInfo(formRequest, save);

        //1 保存
        boolean checked;
        if (save) {
            //1.1 提交的必填字段校验
            checked = formComponentService.getFormCheckedStatus(formRequest);
            //1.2  单据号为空时，提交到redFlow
            formNum = StringUtils.isNotBlank(formNum) ? formNum
                    : redFlowRpcService.saveProcess(formComponentService.buildVariableMap(formRequest, true));
        } else {
            //2.1 提交的必填字段校验
            formRequest.fieldsCommonCheck4Submit();
            //2.2 单据自己的业务校验
            String alertMsg = formComponentService.checkFormData(formRequest);
            AssertHelper.isBlank(alertMsg, alertMsg);
            //2.3 获取是否需要线上付款
            confirmFillPaymentInfo(formRequest);
            //2.4 所有报销单的通用基础
            formComponentService.checkFormCommonData(formRequest);
            checked = true;
            //2.5 提交到redFlow启动流程
            formNum = redFlowRpcService.submitProcess(formComponentService.buildVariableMap(formRequest, false));
        }
        AssertUtils.notBlank(formNum, "保存或提交报销单失败: 系统异常");
        ReimbursementFormEntity reimburseForm = reimburseFormConverter.req2Do(formRequest, checked, formNum, save);

        //3 保存或提交后的动作
        formComponentService.submitAfterAction(reimburseForm, formRequest, save);

        //4 返回表单详情快照
        return formComponentService.buildReimbursementForm(reimburseForm, formRequest);
    }

    /**
     * 查询报销单单据详情
     *
     * @param formNum redFlow单据号
     * @return 单据表单详情快照
     */
    @Override
    public ReimbursementFormResponse queryReimburseFormDetail(String formNum) {
        return this.queryReimburseFormDetail(formNum, false);
    }

    /**
     * 查询报销单单据详情 包含软删除的单据
     *
     * @param formNum     单据编号
     * @param withDeleted 是否包含软删除的单据
     * @return 单据表单详情快照
     */
    @Override
    public ReimbursementFormResponse queryReimburseFormDetail(String formNum, boolean withDeleted) {
        AssertHelper.notBlank(formNum, "系统异常: 未知报销单 " + formNum);

        ReimbursementFormEntity reimbursementForm = reimbursementFormService.getReimbursementFormByFormNum(formNum, withDeleted);
        AssertHelper.notNull(reimbursementForm, "系统异常: 未查询到报销单据 " + formNum);

        //redFlow鉴权
        checkReimburseFormPermission(reimbursementForm, UserInfoBag.get().getUserId());

        ReimburseFormFlowComponentService formComponentService
                = componentServiceFactory.getComponentServiceByFormType(reimbursementForm.getFormType());

        ReimburseFormRequest content =
                JSONObject.parseObject(reimbursementForm.getFormContent(), ReimburseFormRequest.class);
        //草稿状态下重新走一遍校验
        if (reimbursementForm.needBaseCheck()) {
            formComponentService.fillCheckedInfo(content, true);
            reimbursementForm.setCheckStatus(formComponentService.getFormCheckedStatus(content) ? 1 : 0);
        }
        ReimbursementFormResponse response =  formComponentService.buildReimbursementForm(reimbursementForm, content);

        // 修改日期格式
        if(CollectionUtils.isNotEmpty(response.getExpenses())) {
            for (ExpenseDto expense : response.getExpenses()){
                expenseService.expenseDateConvertToDotSeparatedDate(expense);
            }
        }
        return response;
    }

    /**
     * 填充打印单据的表单
     *
     * @param formNum 单据编号
     * @return 单据表单打印详情快照
     */
    @Override
    public ReimbursementFormPrintResponse queryReimburseFormDetail4Print(String formNum) {
        AssertHelper.notBlank(formNum, "系统异常: 未知报销单 " + formNum);

        ReimbursementFormEntity reimbursementForm = reimbursementFormService.getReimbursementFormByFormNum(formNum, false);
        AssertHelper.notNull(reimbursementForm, "系统异常: 未查询到报销单据 " + formNum);

        //redFlow鉴权
        checkReimburseFormPermission(reimbursementForm, UserInfoBag.get().getUserId());

        ReimburseFormFlowComponentService formComponentService
                = componentServiceFactory.getComponentServiceByFormType(reimbursementForm.getFormType());

        return formComponentService.buildFormPrintResponse(reimbursementForm);
    }

    /**
     * 检查当前用户是否有查看报销单
     *
     * @param reimbursementForm 单据
     * @param userId            用户编号
     */
    @Override
    public void checkReimburseFormPermission(ReimbursementFormEntity reimbursementForm, String userId) {
        //不是创建者
        if (!reimbursementForm.getCreatorNo().equals(userId)
                // redFlow 鉴权
                && !redFlowRpcService.checkUserFormPermission(userId, reimbursementForm.getFormNum())) {
            throw new BusinessException(ErrorConstant.NOT_AUTH_CODE, ErrorConstant.NOT_AUTH_MSG);
        }
    }

    /**
     * 删除报销单
     *
     * @param formNum 单据编号
     */
    @Override
    public void deleteReimburseForm(String formNum) {
        //鉴权
        String userId = UserInfoBag.get().getUserId();
        ReimbursementFormEntity reimbursementForm = reimbursementFormService.findSelfReimburseForm(formNum, userId);
        if(Objects.isNull(reimbursementForm)){
            throw new BusinessException(ErrorConstant.NOT_AUTH_CODE, ErrorConstant.NOT_AUTH_MSG);
        }
        ReimburseFormFlowComponentService componentService
                = componentServiceFactory.getComponentServiceByFormType(reimbursementForm.getFormType());

        //删除redFlow单据
        redFlowRpcService.deleteForm(reimbursementForm.getFormType(), reimbursementForm.getUuid());

        //删除关联关系
        componentService.formDelete(formNum);

        //本地库单据
        reimbursementFormService.invalidReimbursementFormByUuid(reimbursementForm.getUuid());
    }

    /**
     * 批量保存报销单，单据类型不一致时，根据单据类型分组提交保存
     *
     * @param eIds   费用单号
     * @param userId 用户编号
     * @return 报销单
     */
    @Override
    public List<ReimbursementFormSimpleDto> batchSaveReimbursementForm(List<String> eIds, String userId) {

        List<ExpenseDto> expenses = expenseService.queryExpense(eIds);
        AssertHelper.notEmpty(expenses, "未查询到费用");
        List<ReimbursementFormSimpleDto> forms = new ArrayList<>();
        Map<String, List<ExpenseDto>> expenseMap = expenses.stream().collect(Collectors.groupingBy(ExpenseDto::getFormType));

        List<Future<?>> saveFormFutures = new ArrayList<>();
        for (Map.Entry<String, List<ExpenseDto>> expenseEntry : expenseMap.entrySet()) {
            Future<?> submit = ThreadPoolManager.commonExecutorService.submit(() -> {
                ReimburseFormRequest formRequest = new ReimburseFormRequest();
                List<String> uuids = expenseEntry.getValue().stream().map(ExpenseDto::getUuid).filter(StrUtil::isNotBlank).collect(Collectors.toList());
                formRequest.setExpenseNos(uuids);
                formRequest.setCreatorNo(userId);
                formRequest.setFormType(expenseEntry.getKey());
                ReimbursementFormResponse response = saveOrSubmitReimbursementForm(formRequest, true);
                forms.add(reimburseFormConverter.respToDto(response));
            });
            saveFormFutures.add(submit);
        }

        saveFormFutures.forEach(future -> {
            try {
                future.get();
            } catch (Exception e) {
                log.error("多线程保存报销单失败:{}", e.getMessage(), e);
            }
        });
        return forms;
    }

    /**
     * 单据是否可以提交校验
     * <br>1. 只要草稿、驳回、撤回状态的单据才能保存或提交
     * <br>2. 判断当前人是否有修改权限 = 当前属于可修改状态 + 可修改的人（提交人、）
     *
     * @param formNum 单据号
     */
    private void checkFormStatus(String formNum) {
        if (StrUtil.isBlank(formNum)) {
            return;
        }

        ReimbursementFormEntity reimbursementForm = reimbursementFormService.getReimbursementFormByFormNum(formNum, false);
        //判断当前人是否有修改权限 = 当前属于可修改状态 + 可修改的人（提交人、）
        UserInfo userInfo = UserInfoBag.get();
        //目前只有C端页面提交有用户信息，去校验当前人是不是提交人
        if (Objects.nonNull(userInfo)) {
            AssertHelper.check(reimbursementForm.getCreatorNo().equals(userInfo.getUserId()), "当前用户无权限操作");
        }
        AssertHelper.check(ReimbursementFormStatusEnum.canEditStatus(reimbursementForm.getReimburseStatus()), "当前单据状态不可编辑");
    }

    /**
     * 获取是否需要线上付款
     */
    private void confirmFillPaymentInfo(ReimburseFormRequest formRequest) {
        PaymentInfoDto paymentInfo = oaCompanyRpcService.checkNeedPayment(companyDtoConverter.toCheckParam(formRequest));
        formRequest.setPaymentInfo(paymentInfo);
    }

}
