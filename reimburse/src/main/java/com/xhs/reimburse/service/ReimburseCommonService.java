package com.xhs.reimburse.service;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ReimburseCommonService.java
 * @createTime 2025年02月18日 11:31:00
 */

public interface ReimburseCommonService {
    String getUserId();
    void setUserId(String userId);
    void clean();
    String getUuid();
    boolean amountFormatIsError(String amount);
    boolean dateFormatIsError(String date);
    boolean timeRangeInTimeRange(String startTime, String endTime, String queryStartTime, String queryEndTime);
    boolean timeInTimeRange(String matchTime, String startTime, String endTime);
}
