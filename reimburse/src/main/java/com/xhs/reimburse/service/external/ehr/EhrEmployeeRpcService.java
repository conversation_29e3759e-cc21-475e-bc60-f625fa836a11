package com.xhs.reimburse.service.external.ehr;

import com.google.common.collect.Lists;
import com.xhs.ehr.rpc.request.QueryEmployeeRequest;
import com.xhs.ehr.rpc.response.BatchQueryEmployeeResponse;
import com.xhs.ehr.rpc.service.EhrEmployeeService;
import com.xhs.finance.exception.BusinessException;
import com.xhs.reimburse.assembler.mapper.EhrEmployeeMapper;
import com.xhs.reimburse.modal.entity.EmployeeEntity;
import com.xiaohongshu.infra.rpc.base.Context;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class EhrEmployeeRpcService {

    @Resource
    private EhrEmployeeService.Iface ehrEmployeeService;

    @Resource
    private EhrEmployeeMapper ehrEmployeeMapper;

    private static final Long ENTERPRISE_ID = 10000L;

    /**
     * 根据员工编号查询员工信息
     *
     * @param userId      员工编号
     * @param withInvalid 是否查询无效员工
     */
    public EmployeeEntity queryEhrEmployeeEntity(String userId, Boolean withInvalid) {

        QueryEmployeeRequest request = new QueryEmployeeRequest();
        try {
            request.setEnterpriseId(ENTERPRISE_ID);
            request.setWithInvalid(withInvalid);
            request.setEmployeeIdList(Lists.newArrayList(Long.parseLong(userId)));
            BatchQueryEmployeeResponse response = ehrEmployeeService.query_employee_by_ids(new Context(), request);
            if (response.success) {
                return ehrEmployeeMapper.baseToEntity(response.getEmployeeInfoList().get(0));
            } else {
                log.error("EhrDepartmentRpcService queryDepartmentByDepartmentId fail:{}", response.error_message);
            }
        } catch (TException e) {
            log.error("EhrDepartmentRpcService queryDepartmentByDepartmentId error:{}", e.getMessage(), e);
        }
        return null;
    }


    public List<EmployeeEntity> batchQueryEmployee(List<String> userIdList, Boolean withInvalid) {
        QueryEmployeeRequest request = new QueryEmployeeRequest();
        try {

            List<Long> userIds = userIdList.stream()
                    .filter(userId -> {
                        try {
                            Long.valueOf(userId);
                            return true;
                        } catch (NumberFormatException e) {
                            return false;
                        }
                    })
                    .map(Long::valueOf)
                    .collect(Collectors.toList());

            request.setEnterpriseId(ENTERPRISE_ID);
            request.setWithInvalid(withInvalid);
            request.setEmployeeIdList(userIds);
            BatchQueryEmployeeResponse response = ehrEmployeeService.query_employee_by_ids(new Context(), request);
            if (response.success) {
                return ehrEmployeeMapper.baseToEntityList(response.getEmployeeInfoList());
            } else {
                throw new BusinessException(response.error_message);
            }
        } catch (TException e) {
            log.error("EhrDepartmentRpcService query_employee_by_ids userIdList:{},error:{}", userIdList, e.getMessage(), e);
        }
        return Collections.emptyList();
    }
}
