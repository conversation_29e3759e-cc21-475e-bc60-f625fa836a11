package com.xhs.reimburse.service;

import com.xhs.reimburse.modal.dto.ExpenseDto;
import com.xhs.reimburse.modal.dto.ReimbursementFormDto;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> jiangjixiang
 * @version : 1.0
 * @Description : Description
 * @date ：Created in 2025/5/21 20:15
 */
public interface ExpenseBusinessService {

    /**
     * @description 保存费用-加鉴权
     * <AUTHOR>
     * @param expenseDto
     * @return String
     * @date 2025/5/21 20:17
     */
    String saveExpenseCheckAuth(ExpenseDto expenseDto);

    /**
     * @description 更新费用-加鉴权
     * <AUTHOR>
     * @param expenseDto
     * @return String
     * @date 2025/5/21 20:17
     */
    void updateExpenseCheckAuth(ExpenseDto expenseDto);

    /**
     * @description 删除费用-加鉴权
     * <AUTHOR>
     * @param uuid 发票uuid
     * @return void
     * @date 2025/5/21 20:17
     */
    void logicDeleteExpenseCheckAuth(String uuid);

    /**
     * @description 查询费用所属报销单-加鉴权
     * <AUTHOR>
     * @param expenseUuidList 费用uuid
     * @return com.xhs.reimburse.modal.dto.ReimbursementFormDto
     * @date 2025/5/21 20:17
     */
    Map<String, ReimbursementFormDto> batchQueryExpenseBelongReimbursementFormCheckAuth(List<String> expenseUuidList);

    /**
     * @description 根据uuid查询费用信息-加鉴权
     * 关联报销单：follow 流程redFlow的权限
     * 未关联报销单：仅本人可查看
     * <AUTHOR>
     * @param uuId 费用uuid
     * @return com.xhs.reimburse.modal.dto.ExpenseDto
     * @date 2025/5/21 20:17
     */
    ExpenseDto getExpenseDtoByUuidCheckAuth(String uuId);

    /**
     * @description 根据uuid查询费用信息（包含已删除）-加鉴权
     * <AUTHOR>
     * @param uuId 费用uuid
     * @return com.xhs.reimburse.modal.dto.ExpenseDto
     * @date 2025/5/21 20:17
     */
    ExpenseDto getExpenseByUuIdWithDeletedCheckAuth(String uuId);
}
