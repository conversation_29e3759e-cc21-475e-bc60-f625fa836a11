package com.xhs.reimburse.service.external.ros;

import cn.hutool.core.util.StrUtil;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.xhs.oa.office.utils.AssertHelper;
import com.xhs.reimburse.modal.dto.FileInfoDto;
import com.xiaohongshu.ros.ROSClient;
import com.xiaohongshu.ros.model.ROSPutObjectResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.UUID;


/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class ReimburseRosService {

    /**
     * token 是ros sdk的接入凭证, 在融合CDN平台申请
     */
    @ApolloJsonValue("${ep-oa-attachments-ros-token:}")
    private String token;

    /**
     * business scene 业务场景绑定了一份存储配置，包含双云桶等信息。
     */
    @ApolloJsonValue("${ep-oa-attachments-ros-business:}")
    private String business;

    @ApolloJsonValue("${ep-oa-attachments-ros-scene:}")
    private String scene;

    private ROSClient buildClient() throws Exception {
        return new ROSClient.Builder(token).
                BusinessScene(business, scene).
                build();
    }


    /**
     * 上传文件后返回fileId
     *
     * @param fileName    文件名
     * @param inputStream 流
     * @return fileId（文件ID）
     */
    public FileInfoDto putObject(String fileName, InputStream inputStream) {

        if (StrUtil.isBlank(fileName) || inputStream == null) {
            return null;
        }

        try {
            //文件后缀-类型，例如： jpg
            String fileType = fileName.substring(fileName.lastIndexOf("."));
            AssertHelper.notBlank(fileType, "文件类型不能为空");
            //生成 objKey（不能包含中文）
            String objKey = business + UUID.randomUUID().toString().replaceAll("-", "") + fileType;

            ROSPutObjectResult putObjectResult = buildClient().putObject(objKey, inputStream);
            return new FileInfoDto(putObjectResult.getFileName(), business, scene, fileName);
        } catch (Exception e) {
            log.error("RosService putObject 上传文件失败:{}", e.getMessage(), e);
        }

        return null;
    }

}
