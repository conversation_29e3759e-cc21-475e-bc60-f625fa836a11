package com.xhs.reimburse.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xhs.reimburse.assembler.ExpenseAssembler;
import com.xhs.reimburse.constant.CommonConstant;
import com.xhs.reimburse.enums.ExpenseFormStatusEnum;
import com.xhs.reimburse.enums.InvoiceFormStatusEnum;
import com.xhs.reimburse.mapper.RelationReimbursementFormExpenseMapper;
import com.xhs.reimburse.modal.dto.ExpenseDto;
import com.xhs.reimburse.modal.dto.ReimbursementFormDto;
import com.xhs.reimburse.modal.entity.ExpenseEntity;
import com.xhs.reimburse.modal.entity.ReimbursementFormEntity;
import com.xhs.reimburse.modal.entity.RelationReimbursementFormExpenseEntity;
import com.xhs.reimburse.service.*;
import com.xhs.reimburse.utils.StreamUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date :2025/02/12 - 下午8:58
 * @description :
 */
@Slf4j
@Service
public class RelationReimbursementFormExpenseServiceImpl extends ServiceImpl<RelationReimbursementFormExpenseMapper, RelationReimbursementFormExpenseEntity> implements RelationReimbursementFormExpenseService {
    // service
    @Resource
    private InvoiceService invoiceService;
    @Resource
    private ExpenseService expenseService;
    @Resource
    private ExpenseAssembler expenseAssembler;
    @Resource
    private ReimbursementFormService reimbursementFormService;
    @Resource
    private RelationExpenseInvoiceService relationExpenseInvoiceService;

    // mapper
    @Resource
    private RelationReimbursementFormExpenseMapper reimbursementFormExpenseRelationMapper;

    /**
     * @param userId                用户ID
     * @param reimbursementFormUuid 报销单ID
     * @param expenseUuidList       费用列表
     */
    @Override
    public void updateReimbursementFormExpenseRelation(String userId, String reimbursementFormUuid, List<String> expenseUuidList) {
        // 报销单校验
        reimbursementFormService.existCheck(userId, Collections.singletonList(reimbursementFormUuid));

        // 清空原有关联费用
        clearRelationByReimbursementFormUuid(reimbursementFormUuid);

        // 费用校验
        if (CollectionUtils.isEmpty(expenseUuidList)) {
            return;
        }
        expenseService.existCheck(userId, expenseUuidList);

        // 批量插入
        expenseUuidList.stream()
                .map(expenseUuid -> {
                    RelationReimbursementFormExpenseEntity entity = new RelationReimbursementFormExpenseEntity();
                    entity.setReimbursementFormUuid(reimbursementFormUuid);
                    entity.setExpenseUuid(expenseUuid);
                    return entity;
                })
                .forEach(reimbursementFormExpenseRelationMapper::insert);

        // 修改现有关联费用
        expenseService.updateExpenseListStatus(expenseUuidList, null, null, ExpenseFormStatusEnum.YGL);

        // 修改费用下所有发票的状态
        List<String> invoiceUuidList = relationExpenseInvoiceService.getExpenseListAllInvoice(expenseUuidList);
        invoiceService.updateInvoiceListStatus(invoiceUuidList, null, null, InvoiceFormStatusEnum.YGL);
    }

    private void clearRelationByReimbursementFormUuid(String reimbursementFormUuid) {
        LambdaQueryWrapper<RelationReimbursementFormExpenseEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(RelationReimbursementFormExpenseEntity::getReimbursementFormUuid, reimbursementFormUuid);
        List<RelationReimbursementFormExpenseEntity> relationList = reimbursementFormExpenseRelationMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(relationList)) {
            return;
        }

        log.info("clearRelationByReimbursementFormUuid reimbursementFormUuid={}, relationList={}", reimbursementFormUuid, relationList);
        List<Long> ids = StreamUtil.toList(relationList, RelationReimbursementFormExpenseEntity::getId);
        reimbursementFormExpenseRelationMapper.deleteBatchIds(ids);

        // 修改费用状态
        List<String> expenseUuidList = relationList.stream().map(RelationReimbursementFormExpenseEntity::getExpenseUuid).collect(Collectors.toList());
        expenseService.updateExpenseListStatus(expenseUuidList, null, null, ExpenseFormStatusEnum.WGL);

        // 修改费用下所有发票的状态
        List<String> invoiceUuidList = relationExpenseInvoiceService.getExpenseListAllInvoice(expenseUuidList);
        invoiceService.updateInvoiceListStatus(invoiceUuidList, null, null, InvoiceFormStatusEnum.WGL);
    }

    @Override
    public List<String> queryEIdsByUuid(String uuid) {
        if (StringUtils.isBlank(uuid)) {
            return null;
        }
        List<RelationReimbursementFormExpenseEntity> formExpenseRelationEntities = getFormExpenseRelationEntities(uuid);
        if (CollectionUtils.isNotEmpty(formExpenseRelationEntities)) {
            return formExpenseRelationEntities.stream()
                    .map(RelationReimbursementFormExpenseEntity::getExpenseUuid).collect(Collectors.toList());
        }
        return null;
    }

    @Override
    public List<ExpenseDto> getReimbursementFormAllExpense(String reimbursementFormUuid) {
        List<RelationReimbursementFormExpenseEntity> relationReimbursementFormExpenseEntityList = getFormExpenseRelationEntities(reimbursementFormUuid);
        if (CollectionUtils.isEmpty(relationReimbursementFormExpenseEntityList)) {
            return new ArrayList<>();
        }

        List<String> expenseUuidList = relationReimbursementFormExpenseEntityList.stream().map(RelationReimbursementFormExpenseEntity::getExpenseUuid).collect(Collectors.toList());
        return expenseService.queryExpense(expenseUuidList);
    }

    @Override
    public List<String> queryFromUuidsByExpenseUuid(String expenseUuid) {
        if(StringUtils.isBlank(expenseUuid)){
            return Collections.emptyList();
        }
        LambdaQueryWrapper<RelationReimbursementFormExpenseEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(RelationReimbursementFormExpenseEntity::getExpenseUuid, expenseUuid);
        return reimbursementFormExpenseRelationMapper.selectList(queryWrapper).stream()
                .map(RelationReimbursementFormExpenseEntity::getReimbursementFormUuid).collect(Collectors.toList());
    }

    private List<RelationReimbursementFormExpenseEntity> getFormExpenseRelationEntities(String reimbursementFormUuid) {
        if (StrUtil.isNotBlank(reimbursementFormUuid)) {
            LambdaQueryWrapper<RelationReimbursementFormExpenseEntity> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(RelationReimbursementFormExpenseEntity::getReimbursementFormUuid, reimbursementFormUuid);
            return reimbursementFormExpenseRelationMapper.selectList(queryWrapper);
        }
        return null;
    }

    public Map<String, ReimbursementFormDto> batchQueryExpenseBelongReimbursementForm(List<String> expenseUuidList) {
        Map<String, ReimbursementFormDto> result = new HashMap<>();
        if (CollectionUtils.isEmpty(expenseUuidList)) {
            return result;
        }
        LambdaQueryWrapper<RelationReimbursementFormExpenseEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(RelationReimbursementFormExpenseEntity::getExpenseUuid, expenseUuidList);
        List<RelationReimbursementFormExpenseEntity> reimbursementFormExpenseRelationList = reimbursementFormExpenseRelationMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(reimbursementFormExpenseRelationList)) {
            return result;
        }
        // 费用uuid和报销单uuid的映射关系
        Map<String, String> expenseReimbursementFormMap = reimbursementFormExpenseRelationList.stream().collect(Collectors.toMap(RelationReimbursementFormExpenseEntity::getExpenseUuid, RelationReimbursementFormExpenseEntity::getReimbursementFormUuid));
        List<String> reimbursementFormUuidList = reimbursementFormExpenseRelationList.stream().map(RelationReimbursementFormExpenseEntity::getReimbursementFormUuid).collect(Collectors.toList());

        // 报销单uuid与报销单的映射关系
        List<ReimbursementFormDto> reimbursementFormList = reimbursementFormService.queryReimbursementForm(reimbursementFormUuidList);
        Map<String, ReimbursementFormDto> reimbursementFormMap = reimbursementFormList.stream().collect(Collectors.toMap(ReimbursementFormDto::getUuid, Function.identity()));

        // 费用uuid与报销单的映射关系
        expenseReimbursementFormMap.forEach((expenseUuid, reimbursementFormUuid) -> {
            ReimbursementFormDto reimbursementFormDto = reimbursementFormMap.get(reimbursementFormUuid);
            result.put(expenseUuid, reimbursementFormDto);
        });
        return result;
    }

    public String queryExpenseBelongReimbursementForm(String expenseUuid) {
        LambdaQueryWrapper<RelationReimbursementFormExpenseEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(RelationReimbursementFormExpenseEntity::getExpenseUuid, expenseUuid);
        List<RelationReimbursementFormExpenseEntity> reimbursementFormExpenseRelationList = reimbursementFormExpenseRelationMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(reimbursementFormExpenseRelationList)) {
            return "";
        }
        // 过滤有效报销单
        List<ReimbursementFormEntity> forms = reimbursementFormService.queryReimbursementFormEntity(StreamUtil.toList(reimbursementFormExpenseRelationList, RelationReimbursementFormExpenseEntity::getReimbursementFormUuid));
        List<String> formUuIds = StreamUtil.filterToList(forms, o -> CommonConstant.VALID_INTER.equals(o.getIsValid()), ReimbursementFormEntity::getUuid);
        reimbursementFormExpenseRelationList=StreamUtil.filterToList(reimbursementFormExpenseRelationList, o -> formUuIds.contains(o.getReimbursementFormUuid()));
        if (CollectionUtils.isEmpty(reimbursementFormExpenseRelationList)) {
            return "";
        }
        return reimbursementFormExpenseRelationList.get(0).getReimbursementFormUuid();
    }

    public void clearRelationByExpenseUuid(String uuId) {
        if (StrUtil.isBlank(uuId)) {
            return;
        }
        reimbursementFormExpenseRelationMapper.deleteByExpenseUuid(uuId);
    }

    @Override
    public Map<String, BigDecimal> batchQueryRelationFormAmount(List<String> formUuids) {
        Map<String, BigDecimal> result = new HashMap<>();
        if (CollUtil.isEmpty(formUuids)) {
            return result;
        }

        //查询关联关系
        LambdaQueryWrapper<RelationReimbursementFormExpenseEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(RelationReimbursementFormExpenseEntity::getReimbursementFormUuid, formUuids);
        List<RelationReimbursementFormExpenseEntity> entities = reimbursementFormExpenseRelationMapper.selectList(queryWrapper);

        //查询费用 ExpenseEntity
        List<String> eIds = entities.stream().map(RelationReimbursementFormExpenseEntity::getExpenseUuid)
                .collect(Collectors.toList());
        List<ExpenseEntity> expenseEntities = expenseService.queryExpenseEntity(eIds);

        if (CollUtil.isNotEmpty(expenseEntities)) {

            //构建费用uuid和金额的关系
            Map<String, BigDecimal> expenseMap = expenseEntities.stream()
                    .collect(Collectors.toMap(ExpenseEntity::getUuid, e -> expenseAssembler.parseExpenseAmount(e)));

            for (RelationReimbursementFormExpenseEntity entity : entities) {
                //叠加金额
                BigDecimal formAmount = result.getOrDefault(entity.getReimbursementFormUuid(), BigDecimal.ZERO);
                formAmount = expenseMap.getOrDefault(entity.getExpenseUuid(), BigDecimal.ZERO).add(formAmount);
                result.put(entity.getReimbursementFormUuid(), formAmount);
            }
        }

        return result;
    }
}
