package com.xhs.reimburse.service;

import com.xhs.finance.utils.AssertUtils;
import com.xhs.reimburse.constant.CommonConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class ReimburseFormFlowComponentServiceFactory {

    @Resource
    private Map<String, ReimburseFormFlowComponentService> reimburseFormFlowComponentServiceMap;

    public ReimburseFormFlowComponentService getComponentServiceByFormType(String formType){
        AssertUtils.notBlank(formType, "未知单据类型");
        return reimburseFormFlowComponentServiceMap.get(formType + CommonConstant.FORM_COMPONENT_SUFFIX);
    }

}
