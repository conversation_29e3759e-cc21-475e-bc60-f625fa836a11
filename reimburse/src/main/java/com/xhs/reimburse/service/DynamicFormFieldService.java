package com.xhs.reimburse.service;

import com.hankcs.hanlp.collection.sequence.SString;
import com.xhs.reimburse.enums.InvoiceTypeEnum;
import com.xhs.reimburse.modal.dto.DynamicFormFieldDto;
import com.xhs.reimburse.modal.dto.DynamicFormFieldListQueryDto;
import com.xhs.reimburse.rpc.consumer.ocr.OcrInvoiceDataResponse;
import com.xhs.reimburse.rpc.consumer.ocr.bean.ImageInvoicesRecogcollectMediaInvoice;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DynamicFormFieldServiceImpl.java
 * @createTime 2025年02月20日 11:41:00
 */
public interface DynamicFormFieldService {
    List<DynamicFormFieldDto> getInvoiceDynamicFormFieldList(String invoiceType);

    List<DynamicFormFieldDto> getExpenseDynamicFormFieldList(String expenseType);

    List<DynamicFormFieldDto> queryDynamicFormFieldList(DynamicFormFieldListQueryDto dto);

    boolean validateRequiredFields(List<DynamicFormFieldDto> list);

    List<DynamicFormFieldDto> getDynamicFormFields(OcrInvoiceDataResponse ocrData);

    /**
     * 根据OCR解析发票内容,渲染动态表单
     * @param ocrData
     * @param invoiceTypeEnum
     * @return
     */
    List<DynamicFormFieldDto> getDynamicFormFields(ImageInvoicesRecogcollectMediaInvoice ocrData, InvoiceTypeEnum ticketType, String ocrParseInvoiceType);

    void validateAndCorrectHasDefault(List<DynamicFormFieldDto> list);

    Object getDefaultValueObject(List<DynamicFormFieldDto> list, String fieldCode);

    void buildDynamicFormDefaultValueMap(List<DynamicFormFieldDto> list);

    void buildDynamicFormDefaultValue(String secondSubject, List<DynamicFormFieldDto> list);

    boolean isValueEmpty(Object value);

    boolean isAmountNotValid(String amount);

    void updateDynamicFromDefaultValue(List<DynamicFormFieldDto> list, String filedCode,Object value);
}