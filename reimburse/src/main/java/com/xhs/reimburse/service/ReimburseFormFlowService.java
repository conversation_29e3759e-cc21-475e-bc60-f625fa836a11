package com.xhs.reimburse.service;

import com.xhs.reimburse.modal.dto.ExpenseDto;
import com.xhs.reimburse.modal.dto.ReimburseBasicInfo;
import com.xhs.reimburse.modal.dto.ReimbursementFormSimpleDto;
import com.xhs.reimburse.modal.entity.ReimbursementFormEntity;
import com.xhs.reimburse.modal.request.ReimburseFormRequest;
import com.xhs.reimburse.modal.response.ReimbursementFormPrintResponse;
import com.xhs.reimburse.modal.response.ReimbursementFormResponse;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ReimburseFormFlowService {

    /**
     * 保存或提交报销单
     *
     * @param formRequest 提交请求
     * @param save        是否是保存操作 true:保存 false:提交
     * @return 单据表单详情快照
     */
    ReimbursementFormResponse saveOrSubmitReimbursementForm(ReimburseFormRequest formRequest, boolean save);

    /**
     * 查询报销单单据详情
     *
     * @param formNum redFlow单据号
     * @return 单据表单详情快照
     */
    ReimbursementFormResponse queryReimburseFormDetail(String formNum);

    /**
     * 查询报销单单据详情 包含软删除的单据
     *
     * @param formNum     单据编号
     * @param withDeleted 是否包含软删除的单据
     * @return 单据表单详情快照
     */
    ReimbursementFormResponse queryReimburseFormDetail(String formNum, boolean withDeleted);

    /**
     * 填充打印单据的表单
     *
     * @param formNum 单据编号
     * @return 单据表单打印详情快照
     */
    ReimbursementFormPrintResponse queryReimburseFormDetail4Print(String formNum);

    /**
     * 批量保存报销单
     *
     * @param eIds   费用编号
     * @param userId 提交人
     * @return 单据描述
     */
    List<ReimbursementFormSimpleDto> batchSaveReimbursementForm(List<String> eIds, String userId);

    /**
     * 检查当前用户是否有查看报销单
     *
     * @param reimbursementForm 单据
     * @param userId            用户编号
     */
    void checkReimburseFormPermission(ReimbursementFormEntity reimbursementForm, String userId);

    /**
     * 删除报销单
     *
     * @param formNum 单据编号
     */
    void deleteReimburseForm(String formNum);

}