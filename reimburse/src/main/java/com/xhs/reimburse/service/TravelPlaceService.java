package com.xhs.reimburse.service;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.google.common.collect.Lists;
import com.xhs.oa.office.utils.AssertHelper;
import com.xhs.reimburse.modal.dto.travel.SearchPlaceDto;
import com.xhs.reimburse.xhsoa.mapper.TravelPlaceMapper;
import com.xhs.reimburse.xhsoa.modal.TravelPlace;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TravelPlaceService.java
 * @createTime 2025年03月21日 15:08:00
 */
@Slf4j
@Service
public class TravelPlaceService {

    private static final String SYS = "1";

//	private static final int SEARCH_LIMIT = 20;

    @ApolloJsonValue("${travel_place_search_limit:20}")
    private Integer SEARCH_LIMIT;

    /**
     * 酒店一类城市
     *
     * @see com.xhs.oa.travel.domain.enums.TravelHotelCityType#DOMESTIC_LEVEL_ONE
     */
    private static final List<String> HOTEL_CITY_LEVEL_ONE = Lists.newArrayList("北京", "上海", "深圳", "广州");

    /**
     * 酒店三类城市
     *
     * @see com.xhs.oa.travel.domain.enums.TravelHotelCityType#INTERNATIONAL_ASIA
     */
    private static final List<String> HOTEL_CITY_LEVEL_THREE = Lists
            .newArrayList("香港", "澳门", "台北", "高雄", "垦丁", "新竹", "Pingtung", "台南", "台东", "台中", "嘉义", "马公", "花莲", "金门", "云林", "南投",
                    "桃园", "斗六", "宜兰", "新北市", "澎湖", "马祖", "苗栗", "基隆", "彰化", "恒春", "南竿", "东沙岛", "绿岛", "兰屿");

    /**
     * 酒店三类国家
     *
     * @see com.xhs.oa.travel.domain.enums.TravelHotelCityType#INTERNATIONAL_ASIA
     */
    private static final List<String> HOTEL_COUNTRY_LEVEL_THREE = Lists
            .newArrayList("蒙古", "朝鲜", "韩国", "日本", "菲律宾", "越南", "老挝", "柬埔寨", "缅甸", "泰国", "马来西亚", "文莱", "新加坡", "印度尼西亚", "东帝汶",
                    "尼泊尔", "不丹", "孟加拉国", "印度", "巴基斯坦", "斯里兰卡", "马尔代夫", "哈萨克斯坦", "吉尔吉斯斯坦", "塔吉克斯坦", "乌兹别克斯坦", "土库曼斯坦", "阿富汗",
                    "伊拉克", "伊朗", "叙利亚", "约旦", "黎巴嫩", "以色列", "巴勒斯坦", "沙特阿拉伯", "巴林", "卡塔尔", "科威特", "阿联酋", "阿曼", "也门", "格鲁吉亚",
                    "亚美尼亚", "阿塞拜疆", "土耳其", "塞浦路斯");

    /**
     * 机票特殊城市，国内城市走国际航班
     */
    private static final List<String> FLIGHT_SPECIAL_DOMESTIC_CITY = Lists
            .newArrayList("澳门", "香港", "台北", "高雄", "新竹", "台南", "台东", "台中", "嘉义市", "屏东", "花莲", "云林", "南投", "桃园", "宜兰", "新北", "澎湖", "苗栗", "基隆", "彰化", "嘉义县", "新竹县");

    /**
     * 中国id
     */
    private static final String CHINA = "1";

    @Resource
    private TravelPlaceMapper travelPlaceMapper;

    public List<TravelPlace> queryTravelPlaceList(SearchPlaceDto searchPlaceParam) {
        return travelPlaceMapper.queryTravelPlaceListByParam(searchPlaceParam);
    }

    /**
     * 地点code转换 例如：国家为1，code SHA 转换成 code 2
     *
     * @param toType
     * @param cityName
     * @param countryId
     * @return
     */
    public String convertPlaceCode(String toType, String cityName, String countryId) {

        AssertHelper.notBlank(cityName, "参数缺失: cityName");
        AssertHelper.notBlank(countryId, "参数缺失: countryId");

        SearchPlaceDto searchPlaceParam = new SearchPlaceDto(toType, cityName, countryId);
        List<TravelPlace> travelPlaceList = queryTravelPlaceList(searchPlaceParam);
        if (CollectionUtils.isNotEmpty(travelPlaceList)) {
            return travelPlaceList.get(0).getPlaceCode();
        }

        return null;
    }
}
