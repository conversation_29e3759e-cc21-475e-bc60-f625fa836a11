package com.xhs.reimburse.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Maps;
import com.xhs.oa.office.exception.BusinessException;
import com.xhs.oa.office.utils.AssertHelper;
import com.xhs.reimburse.constant.CommonConstant;
import com.xhs.reimburse.constant.ErrorConstant;
import com.xhs.reimburse.mapper.ExpenseMapper;
import com.xhs.reimburse.modal.dto.ExpenseDto;
import com.xhs.reimburse.modal.dto.ReimbursementFormDto;
import com.xhs.reimburse.modal.entity.ExpenseEntity;
import com.xhs.reimburse.modal.entity.ReimbursementFormEntity;
import com.xhs.reimburse.service.ExpenseBusinessService;
import com.xhs.reimburse.service.ReimburseCommonService;
import com.xhs.reimburse.service.ReimbursementFormService;
import com.xhs.reimburse.service.RelationReimbursementFormExpenseService;
import com.xhs.reimburse.service.external.redflow.RedFlowRpcService;
import com.xhs.reimburse.utils.StreamUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> jiangjixiang
 * @version : 1.0
 * @Description : Description
 * @date ：Created in 2025/5/21 20:16
 */
@Slf4j
@Service
public class ExpenseBusinessServiceImpl implements ExpenseBusinessService {

    @Resource
    private ExpenseServiceImpl expenseService;
    @Resource
    private ReimburseCommonService reimburseCommonService;
    @Resource
    private RelationReimbursementFormExpenseService relationReimbursementFormExpenseService;
    @Resource
    private ExpenseMapper expenseMapper;
    @Resource
    private ReimbursementFormService reimbursementFormService;
    @Resource
    private RedFlowRpcService redFlowRpcService;

    @Override
    public String saveExpenseCheckAuth(ExpenseDto expenseDto) {
        String creatorUserId = expenseDto.getCreatorUserId();
        String loginUserId = reimburseCommonService.getUserId();
        // 检验本人
        if(StringUtils.isNotBlank(creatorUserId) && !loginUserId.equals(creatorUserId)){
            throw new BusinessException(ErrorConstant.NOT_AUTH_CODE, ErrorConstant.NOT_AUTH_MSG);
        }
        return expenseService.saveExpense(expenseDto);
    }

    @Override
    public void updateExpenseCheckAuth(ExpenseDto expenseDto) {
        String uuid = expenseDto.getUuid();
        //鉴权是否是本人操作
        checkExpenseAuthSelf(uuid, false);
        expenseService.updateExpense(expenseDto);
    }

    //鉴权是否是本人操作
    private void checkExpenseAuthSelf(String uuid, boolean withDeleted) {
        ExpenseEntity entity = expenseService.getExpenseEntityByUuid(uuid, withDeleted);
        String loginUserId = reimburseCommonService.getUserId();
        AssertHelper.notNull(entity, "费用不存在");
        if(!loginUserId.equals(entity.getCreatorUserId())){
            throw new BusinessException(ErrorConstant.NOT_AUTH_CODE, ErrorConstant.NOT_AUTH_MSG);
        }
    }

    @Override
    public void logicDeleteExpenseCheckAuth(String uuid) {
        //鉴权是否是本人操作
        checkExpenseAuthSelf(uuid, false);
        expenseService.logicDeleteExpense(uuid);
    }

    @Override
    public Map<String, ReimbursementFormDto> batchQueryExpenseBelongReimbursementFormCheckAuth(List<String> expenseUuidList) {
        if (CollectionUtils.isEmpty(expenseUuidList)) {
            return Maps.newHashMap();
        }
        // 校验Uuid均为本人费用
        LambdaQueryWrapper<ExpenseEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(ExpenseEntity::getUuid, expenseUuidList);
        List<ExpenseEntity> expenseList = expenseMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(expenseList)) {
            return Maps.newHashMap();
        }
        List<String> createUserIds = StreamUtil.toList(expenseList, ExpenseEntity::getCreatorUserId);
        if (!createUserIds.contains(reimburseCommonService.getUserId())) {
            throw new BusinessException(ErrorConstant.NOT_AUTH_CODE, ErrorConstant.NOT_AUTH_MSG);
        }
        return relationReimbursementFormExpenseService.batchQueryExpenseBelongReimbursementForm(expenseUuidList);
    }

    @Override
    public ExpenseDto getExpenseDtoByUuidCheckAuth(String uuId) {
        checkExpenseAuth(uuId);
        return expenseService.getExpenseDtoByUuid(uuId);
    }

    @Override
    public ExpenseDto getExpenseByUuIdWithDeletedCheckAuth(String uuId) {
        //鉴权是否是本人操作
        checkExpenseAuthSelf(uuId, true);
        return expenseService.getExpenseByUuIdWithDeleted(uuId);
    }

    private void checkExpenseAuth(String uuId) {
        AssertHelper.notBlank(uuId,"参数异常");
        ExpenseEntity expenseEntity = expenseService.getExpenseEntityByUuid(uuId, false);
        String loginUserId = reimburseCommonService.getUserId();
        if (loginUserId.equals(expenseEntity.getCreatorUserId())) {
            // 费用创建者 有权限直接返回
            return;
        }
        // 非费用创建者 鉴权
        ReimbursementFormEntity form = this.getReimbursementFormNumByExpenseUuid(uuId);
        if (ObjectUtil.isNotNull(form) && StringUtils.isNotBlank(form.getFormNum())) {
            //关联报销单：follow 流程redFlow的权限
            boolean flowAuth = redFlowRpcService.checkUserFormPermission(loginUserId, form.getFormNum());
            if (!flowAuth) {
                throw new BusinessException(ErrorConstant.NOT_AUTH_CODE, ErrorConstant.NOT_AUTH_MSG);
            }
        } else {
            //未关联报销单：仅本人可查看
            throw new BusinessException(ErrorConstant.NOT_AUTH_CODE, ErrorConstant.NOT_AUTH_MSG);
        }
    }

    private ReimbursementFormEntity getReimbursementFormNumByExpenseUuid(String expenseUuid) {
        String formUuid = relationReimbursementFormExpenseService.queryExpenseBelongReimbursementForm(expenseUuid);
        if (StringUtils.isBlank(formUuid)) {
            return null;
        }
        List<ReimbursementFormEntity> fromList = reimbursementFormService.queryReimbursementFormEntity(Collections.singletonList(formUuid));
        fromList = StreamUtil.filterToList(fromList, o -> CommonConstant.VALID_INTER.equals(o.getIsValid()));
        return CollectionUtils.isEmpty(fromList) ? null : fromList.get(0);
    }
}
