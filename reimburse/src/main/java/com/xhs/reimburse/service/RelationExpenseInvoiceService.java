package com.xhs.reimburse.service;

import com.xhs.reimburse.modal.dto.ExpenseDto;
import com.xhs.reimburse.modal.dto.InvoiceDto;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName RelationExpenseInvoiceService.java
 * @createTime 2025年02月12日 19:02:00
 */
public interface RelationExpenseInvoiceService {

    void updateExpenseInvoiceRelation(String userId, String expenseUuId, List<String> invoiceUuIdList);

    void clearRelationByExpenseUuid(String expenseUuid);

    boolean confirmNeedSubmitInvoice(List<ExpenseDto> eIds);

    List<String> getExpenseListAllInvoice(List<String> expenseUuId);

    List<InvoiceDto> getExpenseAllInvoice(String expenseUuId);

    String queryInvoiceBelongExpense(String invoiceUuid);

    List<String> queryRelationExpenseUuids(String invoiceUuid);
}
