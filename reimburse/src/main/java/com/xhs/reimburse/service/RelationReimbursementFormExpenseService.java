package com.xhs.reimburse.service;

import com.xhs.reimburse.modal.dto.ExpenseDto;
import com.xhs.reimburse.modal.dto.ReimbursementFormDto;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date :2025/02/12 - 下午8:57
 * @description :
 */
public interface RelationReimbursementFormExpenseService {
    void updateReimbursementFormExpenseRelation(String userId, String reimbursementFormUuid, List<String> expenseIdList);

    List<String> queryEIdsByUuid(String uuid);

    List<ExpenseDto> getReimbursementFormAllExpense(String reimbursementFormUuid);

    List<String> queryFromUuidsByExpenseUuid(String expenseUuid);

    Map<String, ReimbursementFormDto> batchQueryExpenseBelongReimbursementForm(List<String> expenseUuidList);

    String queryExpenseBelongReimbursementForm(String expenseUuid);

    void clearRelationByExpenseUuid(String uuId);

    Map<String, BigDecimal> batchQueryRelationFormAmount(List<String> formUuids);
}