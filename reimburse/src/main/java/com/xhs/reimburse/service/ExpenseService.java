package com.xhs.reimburse.service;

import com.xhs.reimburse.enums.ExpenseFormStatusEnum;
import com.xhs.reimburse.enums.ExpenseStatusEnum;
import com.xhs.reimburse.enums.InvoiceExpenseStatusEnum;
import com.xhs.reimburse.modal.dto.*;
import com.xhs.reimburse.modal.dto.travel.TravelApplyFormInfoDto;
import com.xhs.reimburse.modal.entity.ExpenseEntity;
import com.xhs.reimburse.modal.request.PageQueryExpenseRequest;
import com.xhs.reimburse.modal.response.LabelValueExtendResponse;
import com.xhs.reimburse.modal.response.LabelValueResponse;
import com.xiaohongshu.erp.common.framework.page.PageResult;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date :2025/02/12 - 下午2:43
 * @description :
 */
public interface ExpenseService {
    Integer getPendingCount();

    List<LabelValueResponse> getExpenseFormType();

    /**
     * @description
     * <AUTHOR>
     * @param formType ReimburseTypeEnum 报销单类型
     * @return List<LabelValueExtendResponse>
     * @date 2025/5/14 20:40
     */
    List<LabelValueExtendResponse> getExpenseSubject(String formType);

    List<String> getAllowedInvoiceTypes(String formType, String firstSubject, String secondSubject);

    PageResult<ExpenseDto> pageQueryExpense(PageQueryExpenseRequest request);

    ExpenseDto getExpenseDtoByUuid(String uuid);

    ExpenseDto getExpenseByUuIdWithDeleted(String uuid);

    void expenseDateConvertToDotSeparatedDate(ExpenseDto expenseDto);

    void sortedDynamicFormFieldList(List<DynamicFormFieldDto> sortList, List<DynamicFormFieldDto> list);

    ExpenseDto getExpenseDtoByUuid(String uuid, boolean withDeleted);

    void logicDeleteExpense(String uuId);

    List<DynamicFormFieldDto> getExpenseDynamicFormFields(String formType, String firstSubjectCode, String secondSubjectCode);

    List<ExpenseDto> queryExpense(List<String> expenseUuidList);

    List<ExpenseEntity> queryExpenseEntity(List<String> expenseUuidList);

    String updateExpense(ExpenseDto expenseDto);

    String saveExpense(ExpenseDto expenseDto);

    List<String> matchExpense(ExpenseMatchRuleDto expenseMatchRuleDto);

    List<String> queryExpenseByRule(ExpenseQueryRuleDto expenseQueryRuleDto);

    void existCheck(String userId, List<String> expenseUuidList);

    /**
     * 计算所有费用金额总和
     *
     * @param expenses 费用编号
     * @return 费用金额总和，默认值为0.00
     */
    BigDecimal expenseSumAmount(List<ExpenseDto> expenses);

    void updateExpenseListStatus(List<String> expenseUuidList, InvoiceExpenseStatusEnum invoiceExpenseStatus, ExpenseStatusEnum expenseStatus, ExpenseFormStatusEnum expenseFormStatus);

    void updateExpenseRelationFild(String expenseUuid, List<String> invoiceUuidList);

    void setDynamicFormFieldList(String userId, String secondSubject, List<DynamicFormFieldDto> dynamicFormFieldDtoList);

    List<LabelValueResponse> getSeats(String seatType);

    List<LabelValueResponse> getTravelMealOverLimitReasonTypes();

    BigDecimal queryTravelMealLimit(String userId);

    void validateInvoiceItinerary(InvoiceDto invoiceDto, ExpenseDto expenseDto);

    List<ExpenseDto> getExpenseCheckInfos(List<String> expenseUuidList, List<TravelApplyFormInfoDto> travelApplyList, String formPaymentCompanyName);

    /**
     * @description 获取费用信息
     * <AUTHOR>
     * @param uuid 费用uuid
     * @param withDeleted 是否包含删除数据
     * @return ExpenseEntity
     * @date 2025/5/21 20:22
     */
    ExpenseEntity getExpenseEntityByUuid(String uuid, boolean withDeleted);

    /**
     * 提交报销单时，费用维度的校验
     * @param result 校验结果
     * @param expenseList 费用列表
     */
    void checkReimbursementFormInExpense(ReimbursementFormCheckResultDto result, List<ExpenseDto> expenseList);

    String formRelateExpenseCheck(List<ExpenseDto> expenseList);

    String formSingleSubmitCheck(List<ExpenseDto> expenseList);
}