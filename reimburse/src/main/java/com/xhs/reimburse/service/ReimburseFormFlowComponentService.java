package com.xhs.reimburse.service;

import com.xhs.reimburse.modal.entity.ReimbursementFormEntity;
import com.xhs.reimburse.modal.request.RedFlowProcessSaveOrStartRequest;
import com.xhs.reimburse.modal.request.ReimburseFormRequest;
import com.xhs.reimburse.modal.response.ReimbursementFormPrintResponse;
import com.xhs.reimburse.modal.response.ReimbursementFormResponse;

/**
 * <AUTHOR>
 */
public interface ReimburseFormFlowComponentService {

    /**
     * 每个单据构建自己的 response
     *
     * @param reimburseForm 报销单表单
     * @param formRequest   报销请求
     * @return response
     */
    ReimbursementFormResponse buildReimbursementForm(ReimbursementFormEntity reimburseForm, ReimburseFormRequest formRequest);

    /**
     * <br>2.2.1 预算部门是否存在 <br/>
     * <br>2.2.2 财务爱马仕系统付款信息基础校验 <br/>
     */
    void checkFormCommonData(ReimburseFormRequest formRequest);

    /**
     * 单据自己的业务校验
     *
     * @param formRequest 表单请求
     * @return 业务异常msg
     */
    String checkFormData(ReimburseFormRequest formRequest);

    /**
     * 构建表单自己的请求redFlow的变量
     *
     * @param formRequest 表单请求
     * @param save        是否是保存
     * @return 请求redFlow的变量
     */
    RedFlowProcessSaveOrStartRequest buildVariableMap(ReimburseFormRequest formRequest, boolean save);

    /**
     * 单据提交时单据类型自己的业务逻辑
     *
     * @param formNum 单据号
     * @param save    是否保存是保存
     */
    void formSubmit(String formNum, boolean save);

    /**
     * 单据审批拒绝时单据类型自己的业务逻辑
     *
     * @param formNum 单据号
     */
    void formRefuse(String formNum);

    /**
     * 单据审批撤回时单据类型自己的业务逻辑
     *
     * @param formNum 单据号
     */
    void formWithdrawal(String formNum);

    /**
     * 单据审批终止时单据类型自己的业务逻辑
     *
     * @param formNum 单据号
     */
    void formTerminate(String formNum);

    /**
     * 单据审批删除时单据类型自己的业务逻辑
     *
     * @param formNum 单据号
     */
    void formDelete(String formNum);

    /**
     * 单据流程结束时单据类型自己的业务逻辑
     *
     * @param formNum 单据号
     */
    void formComplete(String formNum);

    /**
     * 单据提交后的动作
     */
    void submitAfterAction(ReimbursementFormEntity reimburseForm, ReimburseFormRequest formRequest, boolean save);

    /**
     * 填充基础信息
     */
    void fillBaseInfo(ReimburseFormRequest formRequest, boolean save);

    /**
     * 校验时填充基础信息
     */
    void fillCheckedInfo(ReimburseFormRequest content, boolean save);

    /**
     * 获取表单校验状态
     *
     * @param request 请求信息
     * @return 是否可提报 true 可提报
     */
    boolean getFormCheckedStatus(ReimburseFormRequest request);

    /**
     * 构建打印表单返回信息
     *
     * @param reimbursementForm 报销单实体
     * @return 打印表单
     */
    ReimbursementFormPrintResponse buildFormPrintResponse(ReimbursementFormEntity reimbursementForm);
}
