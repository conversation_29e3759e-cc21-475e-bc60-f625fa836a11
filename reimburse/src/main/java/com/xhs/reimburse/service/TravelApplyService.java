package com.xhs.reimburse.service;

import com.xhs.reimburse.modal.dto.travel.TravelApplyFormInfoDto;
import com.xhs.reimburse.modal.dto.travel.TravelPlaceDto;
import com.xhs.reimburse.modal.request.PageQueryTravelApplyRequest;
import com.xhs.reimburse.modal.response.TravelApplySummaryInfo;
import com.xiaohongshu.erp.common.framework.page.PageResult;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface TravelApplyService {
    /**
     * 分页查询差旅申请单信息
     *
     * @param request
     * @return
     */
    PageResult<TravelApplySummaryInfo> pageQueryTravelApplyFormSummary(PageQueryTravelApplyRequest request);

    List<TravelApplySummaryInfo> queryTravelApplyFormSummary(PageQueryTravelApplyRequest request);

    /**
     * 查询差旅申请单行程详情
     *
     * @param formNums 差旅申请单号
     * @return 差旅申请单行程详情
     */
    List<TravelApplyFormInfoDto> queryTravelApplyScheduleDetail(List<String> formNums);

    /**
     * 差旅机场三字码code转换成城市ID
     *
     * @param travelPlaceDto 差旅申请单号
     */
    void convertPlaceCode(TravelPlaceDto travelPlaceDto);
}
