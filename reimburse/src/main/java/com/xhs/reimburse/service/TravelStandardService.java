package com.xhs.reimburse.service;

import com.xhs.reimburse.modal.entity.travel.TravelStandard;
import com.xhs.reimburse.modal.vo.travel.CTripCountryVO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface TravelStandardService {

    /**
     * 根据科目和城市ID查询差标
     *
     * @param subject 科目
     * @param cityId  城市ID
     * @return 差标
     */
    List<TravelStandard> selectTravelBudgetStandardByCity(String subject, String cityId);

    /**
     * 根据科目和国家ID查询差标
     *
     * @param subject   科目
     * @param countryId 国家ID
     * @return 差标
     */
    List<TravelStandard> selectBudgetStandardByCountryId(String subject, String countryId);

    /**
     * 根据国家名称查询国家信息
     *
     * @param countryName 国家名称
     * @return 国家信息
     */
    CTripCountryVO selectCountryInfoByCountryName(String countryName);

}
