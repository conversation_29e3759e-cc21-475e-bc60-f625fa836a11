package com.xhs.reimburse.assembler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.github.pagehelper.Page;
import com.xhs.finance.sso.UserInfoBag;
import com.xhs.finance.sso.model.UserInfo;
import com.xhs.oa.office.utils.AssertHelper;
import com.xhs.oa.office.utils.DateUtils;
import com.xhs.reimburse.assembler.mapper.ReimbursementFormDtoMapper;
import com.xhs.reimburse.enums.InvoiceTypeEnum;
import com.xhs.reimburse.enums.ReimbursementCheckStatusEnum;
import com.xhs.reimburse.enums.ReimbursementFormStatusEnum;
import com.xhs.reimburse.enums.ReimburseTypeEnum;
import com.xhs.reimburse.modal.dto.*;
import com.xhs.reimburse.modal.dto.travel.TravelApplyFormInfoDto;
import com.xhs.reimburse.modal.entity.EmployeeEntity;
import com.xhs.reimburse.modal.entity.ReimbursementFormEntity;
import com.xhs.reimburse.modal.factory.ReimbursementFormResponseFactory;
import com.xhs.reimburse.modal.request.ReimburseFormRequest;
import com.xhs.reimburse.modal.response.ReimbursementFormPrintResponse;
import com.xhs.reimburse.modal.response.ReimbursementFormResponse;
import com.xhs.reimburse.rpc.consumer.MultiCdnRpcService;
import com.xhs.reimburse.service.*;
import com.xhs.reimburse.service.external.company.OaCompanyRpcService;
import com.xhs.reimburse.service.external.ehr.EhrEmployeeRpcService;
import com.xiaohongshu.erp.common.framework.page.PageResult;
import com.xiaohongshu.erp.common.utils.DateUtil;
import com.xiaohongshu.fls.rpc.oa.office.reimburse.entity.*;
import com.xiaohongshu.fls.rpc.oacommon.company.response.ExpenseInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Validated
@Component
public class ReimbursementFormAssembler {

    @Resource
    private ReimbursementFormDtoMapper reimbursementFormDtoMapper;

    @Resource
    private ExpenseService expenseService;

    @Resource
    private OaCompanyRpcService oaCompanyRpcService;

    @Resource
    private TravelApplyService travelApplyService;

    @Resource
    private EhrEmployeeRpcService ehrEmployeeRpcService;

    @Resource
    private RelationExpenseInvoiceService expenseInvoiceService;

    @Resource
    private RelationReimbursementFormExpenseService formExpenseService;

    @Resource
    private ReimbursementFormService reimbursementFormService;

    @Resource
    private ExpenseAssembler expenseAssembler;

    @Resource
    private MultiCdnRpcService multiCdnRpcService;

    @Resource
    private BankAccountService bankAccountService;

    public ReimbursementFormDto toDto(ReimbursementFormEntity entity) {
        ReimbursementFormDto dto = reimbursementFormDtoMapper.toDto(entity);
        // TODO
        return dto;
    }

    public List<ReimbursementFormDto> toDtoList(List<ReimbursementFormEntity> entityList) {
        List<ReimbursementFormDto> dtoList = reimbursementFormDtoMapper.toDtoList(entityList);
        // TODO
        return dtoList;
    }

    public ReimbursementFormEntity toEntity(ReimbursementFormDto dto) {
        ReimbursementFormEntity entity = reimbursementFormDtoMapper.toEntity(dto);
        // TODO
        return entity;
    }

    public List<ReimbursementFormEntity> toEntityList(List<ReimbursementFormDto> dtoList) {
        List<ReimbursementFormEntity> entityList = reimbursementFormDtoMapper.toEntityList(dtoList);
        // TODO
        return entityList;
    }

    public ReimbursementFormDto rpcToDto(ReimbursementForm reimbursementForm) {
        ReimbursementFormDto dto = new ReimbursementFormDto();
        String amount = Optional.ofNullable(reimbursementForm.getAmount()).orElse("0");
        String formContent = Optional.ofNullable(reimbursementForm.getFormContent()).orElse("");
        List<String> travelApplyFormNums = Optional.ofNullable(reimbursementForm.getTravelApplyFormNums()).orElse(new ArrayList<>());


        dto.setUuid(reimbursementForm.getUuid());
        dto.setFormNum(reimbursementForm.getFormNum());
        dto.setFormType(reimbursementForm.getFormType());
        dto.setAmount(new BigDecimal(amount));
        dto.setRemark(reimbursementForm.getRemark());
        dto.setCreatorNo(reimbursementForm.getCreatorNo());
        dto.setCreator(reimbursementForm.getCreator());
        dto.setUpdaterNo(reimbursementForm.getUpdaterNo());
        dto.setUpdater(reimbursementForm.getUpdater());
        dto.setFormContent(formContent);
        dto.setTravelApplyFormNums(travelApplyFormNums);
        return dto;
    }

    public List<ReimbursementForm> dtoListToRpc(List<ReimbursementFormDto> dtoList) {
        List<ReimbursementForm> reimbursementFormList = new ArrayList<>();
        for (ReimbursementFormDto dto : dtoList) {
            ReimbursementForm reimbursementForm = new ReimbursementForm();
            reimbursementForm.setUuid(dto.getUuid());
            reimbursementForm.setFormNum(dto.getFormNum());
            reimbursementForm.setFormType(dto.getFormType());
            reimbursementForm.setAmount(dto.getAmount().toString());
            reimbursementForm.setReimburseStatus(dto.getReimburseStatus());
            reimbursementForm.setRemark(dto.getRemark());
            reimbursementForm.setIsValid(dto.getIsValid());
            reimbursementForm.setCreatorNo(dto.getCreatorNo());
            reimbursementForm.setCreator(dto.getCreator());
            reimbursementForm.setUpdaterNo(dto.getUpdaterNo());
            reimbursementForm.setUpdater(dto.getUpdater());
            reimbursementForm.setFormContent(dto.getFormContent());
            List<ExpenseDto> expenseDtoList = dto.getRelationExpenseList();
            List<Expense> expenseList = expenseAssembler.dtoListToRpc(expenseDtoList);
            reimbursementForm.setExpenseList(expenseList);
            List<String> travelApplyFormNums = Optional.ofNullable(dto.getTravelApplyFormNums()).orElse(new ArrayList<>());
            reimbursementForm.setTravelApplyFormNums(travelApplyFormNums);

            reimbursementFormList.add(reimbursementForm);
        }
        return reimbursementFormList;
    }

    public ReimbursementFormEntity req2Do(ReimburseFormRequest formRequest, boolean checked, String formNum, boolean save) {
        if (Objects.isNull(formRequest)) {
            return null;
        }
        ReimbursementFormEntity reimburseForm = new ReimbursementFormEntity();
        reimburseForm.setIsValid(1);
        reimburseForm.setId(formRequest.getId());
        reimburseForm.setUuid(formRequest.getUuid());
        reimburseForm.setAmount(formRequest.getAmount());
        reimburseForm.setFormNum(formNum);
        reimburseForm.setCreatorNo(formRequest.getCreatorNo());
        reimburseForm.setRemark("");
        reimburseForm.setUpdaterNo(formRequest.getFormType());
        reimburseForm.setFormType(formRequest.getFormType());
        //数据库不用保存费用的相关信息
        formRequest.setExpenses(null);
        reimburseForm.setFormContent(JSON.toJSONString(formRequest));
        reimburseForm.setCheckStatus(checked ? ReimbursementCheckStatusEnum.PASS.getCode() : ReimbursementCheckStatusEnum.NOT_PASS.getCode());
        //提交->审核中、无id为首次提交->草稿、其他情况均为数据库当前状态（自动保存时不会进行覆盖）
        if (!save) {
            reimburseForm.setReimburseStatus(ReimbursementFormStatusEnum.SHZ.getCode());
        } else if (Objects.isNull(formRequest.getId())) {
            reimburseForm.setReimburseStatus(ReimbursementFormStatusEnum.CG.getCode());
        }

        if (!save) {
            Date submitTime = null;
            ReimbursementFormEntity formEntity = reimbursementFormService.findReimburseForm(formNum);
            if (Objects.nonNull(formEntity)) {
                submitTime = Objects.nonNull(formEntity.getSubmitTime()) ? formEntity.getSubmitTime() : new Date();
            }
            reimburseForm.setSubmitTime(submitTime);
        }

        UserInfo userInfo = UserInfoBag.get();
        String creator = Objects.nonNull(userInfo) && StrUtil.isNotBlank(userInfo.getName()) ? userInfo.getName() : "系统";
        reimburseForm.setCreator(creator);
        reimburseForm.setUpdater(creator);
        return reimburseForm;
    }

    public ReimbursementFormResponse toQueryResp(
            @NotNull(message = "表单构建失败：报销单不存在") ReimbursementFormEntity reimburseForm,
            @NotNull(message = "表单构建失败：表单内容缺失") ReimburseFormRequest formRequest) {

        ReimbursementFormResponse response = ReimbursementFormResponseFactory.createResponse(reimburseForm.getFormType());
        AssertHelper.notNull(response, "表单构建失败：报销单快照初始化失败");

        UserInfo userInfo = UserInfoBag.get();
        List<String> expenseUuids = formExpenseService.queryEIdsByUuid(reimburseForm.getUuid());

        response.setExpenseNos(expenseUuids);
        response.setId(reimburseForm.getId());
        response.setUuid(reimburseForm.getUuid());
        response.setFormNum(reimburseForm.getFormNum());
        response.setFormType(reimburseForm.getFormType());
        response.setCreatorNo(reimburseForm.getCreatorNo());
        response.setCheckStatus(reimburseForm.getCheckStatus());
        response.setReimburseStatus(reimburseForm.getReimburseStatus());
        ReimburseBasicInfo reimburseBasicInfoVo = formRequest.getReimburseBasicInfoVo();
        response.setReimburseBasicInfoVo(reimburseBasicInfoVo);
        boolean isDraft = ReimbursementFormStatusEnum.CG.getCode().equals(reimburseForm.getReimburseStatus());
        response.setIsDraft(isDraft);
        Date createTime = Objects.nonNull(reimburseForm.getCreateTime()) ? reimburseForm.getCreateTime() : new Date();
        response.setCreateTime(DateUtil.formatDate(createTime, DateUtils.LONG_PATTEN));

        Boolean canEditStatus = ReimbursementFormStatusEnum.canEditStatus(reimburseForm.getReimburseStatus());
        Boolean owner = Objects.nonNull(userInfo) && reimburseForm.getCreatorNo().equals(userInfo.getUserId());
        //判断当前人是否有修改权限 = 当前属于可修改状态 + 可修改的人（提交人、）。此状态的返回只对
        response.setCanEdit(canEditStatus && owner);

        //创建人信息
        EmployeeEntity creatorInfo = formRequest.getCreatorInfo();
        if (Objects.isNull(creatorInfo)) {
            creatorInfo = ehrEmployeeRpcService.queryEhrEmployeeEntity(reimburseForm.getCreatorNo(), true);
        }
        response.setCreatorInfo(creatorInfo);

        //报销单附件
        List<FileInfoDto> fileAttachments = formRequest.getFileAttachmentList();
        if (CollUtil.isNotEmpty(fileAttachments)) {
            multiCdnRpcService.batchGetAndSetFileUrl(fileAttachments);
            response.setFileAttachmentList(fileAttachments);
        }

        //报销单费用
        List<ExpenseDto> expenses = formRequest.getExpenses();
        if (CollectionUtils.isEmpty(expenses) && CollectionUtils.isNotEmpty(expenseUuids)) {

            String paymentCompanyName = Objects.nonNull(reimburseBasicInfoVo) ? reimburseBasicInfoVo.getPaymentCompanyName() : "";
            List<TravelApplyFormInfoDto> travelApplyFormInfos
                    = travelApplyService.queryTravelApplyScheduleDetail(formRequest.getTravelApplyFormNums());
            expenses = expenseService.getExpenseCheckInfos(expenseUuids, travelApplyFormInfos, paymentCompanyName);
        }
        response.setExpenses(expenses);
        BigDecimal expenseSumAmount = expenseService.expenseSumAmount(expenses);
        BigDecimal amount = Objects.nonNull(expenseSumAmount) ? expenseSumAmount : BigDecimal.ZERO;
        response.setAmount(amount.setScale(2, RoundingMode.HALF_UP));

        //是否展示需要打印: 需要交票并且不是草稿状态
        Boolean needPrint = formRequest.getNeedPrint();
        if (Objects.isNull(needPrint)) {
            boolean needSubmitInvoice = expenseInvoiceService.confirmNeedSubmitInvoice(expenses);
            needPrint = needSubmitInvoice && !isDraft;
        }
        response.setFormAuditType(BooleanUtils.isTrue(needPrint) ? "PRINT" : "WITHOUT_PRINT");

        return response;
    }

    public PageResult<ReimbursementFormSimpleDto> toPageResult(Page<ReimbursementFormEntity> pageInfo) {

        int total = (int) pageInfo.getTotal();
        int pageSize = pageInfo.getPageSize();
        List<ReimbursementFormEntity> result = pageInfo.getResult();

        List<ReimbursementFormSimpleDto> simpleForms = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(result)) {

            List<String> formUuids = result.stream().map(ReimbursementFormEntity::getUuid).collect(Collectors.toList());
            Map<String, BigDecimal> formAmountMap = formExpenseService.batchQueryRelationFormAmount(formUuids);

            result.forEach(reimbursementForm -> {
                ReimbursementFormSimpleDto simpleForm = new ReimbursementFormSimpleDto();
                simpleForm.setUuid(reimbursementForm.getUuid());
                simpleForm.setFormNum(reimbursementForm.getFormNum());
                simpleForm.setFormType(reimbursementForm.getFormType());
                simpleForm.setFormStatusCode(reimbursementForm.getReimburseStatus());
                simpleForm.setFormTypeName(ReimburseTypeEnum.getFormTypeName(reimbursementForm.getFormType()));
                BigDecimal formAmount = formAmountMap.getOrDefault(reimbursementForm.getUuid(), BigDecimal.ZERO);
                simpleForm.setAmount(formAmount.setScale(2, RoundingMode.HALF_UP));
                simpleForm.setCreateTime(DateUtil.formatDate(reimbursementForm.getCreateTime(), DateUtils.LONG_PATTEN));

                Date submitTime = reimbursementForm.getSubmitTime();
                if (Objects.nonNull(submitTime)) {
                    simpleForm.setSubmitTime(DateUtil.formatDate(submitTime, DateUtils.LONG_PATTEN));
                }
                simpleForm.setFormStatus(ReimbursementFormStatusEnum.getDescByStatus(reimbursementForm.getReimburseStatus()));
                simpleForms.add(simpleForm);
            });
        }

        return new PageResult<>(pageInfo.getPageNum(), pageSize, total, pageInfo.getPages(), simpleForms);
    }

    public ReimbursementFormPrintResponse toPrintResp(
            @NotNull(message = "表单构建失败：报销单不存在") ReimbursementFormEntity reimbursementForm) {

        ReimbursementFormPrintResponse response = ReimbursementFormResponseFactory.createPrintResponse(reimbursementForm.getFormType());
        AssertHelper.notNull(response, "表单构建失败：报销单打印快照初始化失败");

        EmployeeEntity employeeEntity
                = ehrEmployeeRpcService.queryEhrEmployeeEntity(reimbursementForm.getCreatorNo(), true);
        Objects.requireNonNull(response).setCreatorInfo(employeeEntity);
        JSONObject data = JSONObject.parse(reimbursementForm.getFormContent());
        response.setUuid(reimbursementForm.getUuid());
        response.setFormType(reimbursementForm.getFormType());
        response.setFormTypeName(ReimburseTypeEnum.getFormTypeName(reimbursementForm.getFormType()));
        response.setFormNum(reimbursementForm.getFormNum());
        ReimbursePrintBasicInfo reimburseBasicInfoVo
                = data.getJSONObject("reimburseBasicInfoVo").toJavaObject(ReimbursePrintBasicInfo.class);
        List<String> budgetDepartmentNamePath = reimburseBasicInfoVo.getBudgetDepartmentNamePath();
        if (CollectionUtils.isNotEmpty(budgetDepartmentNamePath)) {
            budgetDepartmentNamePath.remove("小红书");
        }
        //TODO 肖康 付款状态
        reimburseBasicInfoVo.setPayStatusDes("");
        response.setReimburseBasicInfoVo(reimburseBasicInfoVo);

        List<String> eIds = formExpenseService.queryEIdsByUuid(reimbursementForm.getUuid());
        if (CollectionUtils.isEmpty(eIds)) {
            return response;
        }
        List<ExpenseDto> expenses = expenseService.queryExpense(eIds);
        if (CollectionUtils.isNotEmpty(expenses)) {
            //填充费用和发票信息
            setExpenseInvoiceInfo(response, expenses);
            BigDecimal expenseSumAmount = expenseService.expenseSumAmount(expenses);
            BigDecimal amount = Objects.nonNull(expenseSumAmount) ? expenseSumAmount : BigDecimal.ZERO;
            response.setAmount(amount.setScale(2, RoundingMode.HALF_UP));
        }
        return response;
    }

    public ReimbursementFormSimpleDto respToDto(ReimbursementFormResponse response) {
        ReimbursementFormSimpleDto formSimpleDto = new ReimbursementFormSimpleDto();
        formSimpleDto.setAmount(response.getAmount());
        formSimpleDto.setFormNum(response.getFormNum());
        formSimpleDto.setFormType(response.getFormType());
        formSimpleDto.setCreateTime(response.getCreateTime());
        formSimpleDto.setFormStatusCode(response.getReimburseStatus());
        formSimpleDto.setFormTypeName(ReimburseTypeEnum.getFormTypeName(response.getFormType()));
        formSimpleDto.setFormStatus(ReimbursementFormStatusEnum.getDescByStatus(response.getReimburseStatus()));
        return formSimpleDto;
    }

    public ReimburseBasicInfo generateReimburseBasicInfo(EmployeeEntity creatorEntity) {
        if (Objects.isNull(creatorEntity)) {
            return null;
        }
        ReimburseBasicInfo reimburseBasicInfo = new ReimburseBasicInfo();
        reimburseBasicInfo.setBudgetDepartmentId(creatorEntity.getDepartmentId());
        reimburseBasicInfo.setBudgetDepartmentName(creatorEntity.getDepartmentName());
        reimburseBasicInfo.setDepartmentId(creatorEntity.getDepartmentId());
        reimburseBasicInfo.setDepartmentName(creatorEntity.getDepartmentName());
        reimburseBasicInfo.setDepartmentIdPath(creatorEntity.getDepartmentIdPath());
        reimburseBasicInfo.setDepartmentNamePath(creatorEntity.getDepartmentNamePath());
        reimburseBasicInfo.setBudgetDepartmentIdPath(creatorEntity.getDepartmentIdPath());
        reimburseBasicInfo.setBudgetDepartmentNamePath(creatorEntity.getDepartmentNamePath());

        ExpenseInfo companyExpenseInfo = oaCompanyRpcService.queryExpenseInfos(creatorEntity.getUserId());
        if (Objects.nonNull(companyExpenseInfo)) {
            reimburseBasicInfo.setPaymentCompanyName(companyExpenseInfo.getCompanyName());
            reimburseBasicInfo.setPaymentCompanyCode(companyExpenseInfo.getCompanyCode());
        }

        List<BankAccountDto> bankAccountList = bankAccountService.getBankAccountList(creatorEntity.getUserId());
        if (CollUtil.isNotEmpty(bankAccountList)) {
            bankAccountList = bankAccountList.stream()
                    .filter(bankAccount -> Objects.nonNull(bankAccount.getIsDefault()) && bankAccount.getIsDefault().equals(1))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(bankAccountList)) {
                BankAccountDto bankAccountDto = bankAccountList.get(0);
                reimburseBasicInfo.setGatheringName(bankAccountDto.getAccountName());
                reimburseBasicInfo.setGatheringBank(bankAccountDto.getBankName());
                reimburseBasicInfo.setGatheringAccount(bankAccountDto.getAccountNo());
                reimburseBasicInfo.setGatheringBankCode(bankAccountDto.getBankCode());
            }
        }

        return reimburseBasicInfo;
    }

    private void setExpenseInvoiceInfo(ReimbursementFormPrintResponse response, List<ExpenseDto> expenses) {
        //报销事由
        StringBuilder remark = new StringBuilder();
        BigDecimal ticketTotalAmount = BigDecimal.ZERO;
        List<ExpenseInvoicePrintVo> expenseVos = new ArrayList<>();
        Map<String, Integer> invoiceTypeCountMap = new HashMap<>();
        int index = 1;
        for (ExpenseDto expense : expenses) {
            ExpenseInvoicePrintVo expenseVo = new ExpenseInvoicePrintVo();
            List<InvoicePrintVo> invoiceVos = new ArrayList<>();
            expenseVo.setAmount(expense.getAmount());
            expenseVo.setUuid(expense.getUuid());
            expenseVo.setPeriod(expense.getDatePhase());
            expenseVo.setExpenseDetail(expense.getCostComment());
            expenseVo.setFirstSubject(expense.getFirstSubjectName());
            expenseVo.setSecondSubject(expense.getSecondSubjectName());
            List<InvoiceDto> invoices = expense.getRelationInvoiceList();
            remark.append(index++).append(".").append(remark).append(expense.getCostComment()).append("。");
            if (CollectionUtils.isNotEmpty(invoices)) {
                for (InvoiceDto invoice : invoices) {
                    InvoicePrintVo invoiceVo = new InvoicePrintVo();
                    BigDecimal invoiceAmount = invoice.getAmount();
                    invoiceVo.setTicketAmount(invoiceAmount);
                    if (Objects.nonNull(invoiceAmount)) {
                        ticketTotalAmount = ticketTotalAmount.add(invoiceAmount);
                    }
                    invoiceVo.setInvoiceNo(invoice.getInvoiceNo());
                    invoiceVo.setInvoiceDate(invoice.getInvoiceDate());
                    InvoiceTypeEnum invoiceTypeEnum = InvoiceTypeEnum.getByCode(invoice.getTicketType());
                    String invoiceDesc = invoiceTypeEnum.getShowDesc();
                    invoiceVo.setInvoiceType(invoiceTypeEnum.getName());
                    Integer count = invoiceTypeCountMap.getOrDefault(invoiceDesc, 0);
                    invoiceTypeCountMap.put(invoiceDesc, ++count);
                    invoiceVos.add(invoiceVo);
                }
            }
            expenseVo.setInvoiceVos(invoiceVos);
            expenseVos.add(expenseVo);
        }
        response.setExpenses(expenseVos);
        response.setRemark(remark.toString());
        response.setTicketTotalAmount(ticketTotalAmount);
        response.setInvoiceTypeCountMap(invoiceTypeCountMap);
    }

    public ReimbursementFormCheckResult reimbursementFormCheckResultDtoToRpc(ReimbursementFormCheckResultDto resultDto) {
        ReimbursementFormCheckResult result = new ReimbursementFormCheckResult();
        if (Objects.isNull(resultDto)) {
            return result;
        }

        List<CheckResultDetailDto> detailDtoList = Optional.ofNullable(resultDto.getCheckResultDetailList()).orElse(new ArrayList<>());
        List<CheckResultDetail> detailList = new ArrayList<>();
        for (CheckResultDetailDto detailDto : detailDtoList) {
            CheckResultDetail detail = new CheckResultDetail();
            detail.setCheckItemCode(detailDto.getCheckItemCode());
            detail.setCheckItemName(detailDto.getCheckItemName());
            detail.setBlockSubmit(detailDto.getBlockSubmit());
            
            List<BlockItemDto> blockItemDtoList = Optional.ofNullable(detailDto.getBlockItemList()).orElse(new ArrayList<>());
            List<BlockItem> blockItemList = new ArrayList<>();
            for (BlockItemDto blockItemDto : blockItemDtoList) {
                BlockItem blockItem = new BlockItem();
                blockItem.setFieldCode(blockItemDto.getFieldCode());
                blockItem.setFieldName(blockItemDto.getFieldName());
                blockItemList.add(blockItem);
            }
            detail.setBlockItemList(blockItemList);
            detailList.add(detail);
        }

        result.setCheckResultDetailList(detailList);
        result.setCheckPass(resultDto.getCheckPass());
        return result;
    }
}
