package com.xhs.reimburse.assembler.mapper;

import com.xhs.reimburse.modal.dto.ReimbursementFormDto;
import com.xhs.reimburse.modal.entity.ReimbursementFormEntity;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date :2025/02/07 - 下午9:37
 * @description :
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ReimbursementFormDtoMapper {
    ReimbursementFormDto toDto(ReimbursementFormEntity entity);
    List<ReimbursementFormDto> toDtoList(List<ReimbursementFormEntity> entityList);

    List<ReimbursementFormEntity> toEntityList(List<ReimbursementFormDto> dtoList);
    ReimbursementFormEntity toEntity(ReimbursementFormDto dto);
}
