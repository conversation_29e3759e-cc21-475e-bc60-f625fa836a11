package com.xhs.reimburse.assembler;

import com.alibaba.fastjson2.JSON;
import com.xhs.reimburse.modal.dto.*;
import com.xhs.reimburse.modal.dto.travel.TravelCityDto;
import com.xhs.reimburse.rpc.consumer.MultiCdnRpcService;
import com.xhs.reimburse.service.DynamicFormFieldService;
import com.xiaohongshu.fls.rpc.oa.office.reimburse.entity.DynamicFormField;
import com.xiaohongshu.fls.rpc.oa.office.reimburse.entity.FileInfo;
import com.xiaohongshu.fls.rpc.oa.office.reimburse.entity.InvoiceException;
import com.xiaohongshu.fls.rpc.oa.office.reimburse.request.QueryDynamicFormFieldListRequest;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date :2025/02/27 - 下午2:52
 * @description :
 */
@Component
public class CommonAssembler {
    @Resource
    private MultiCdnRpcService multiCdnRpcService;
    @Resource
    private DynamicFormFieldService dynamicFormFieldService;

    public List<DynamicFormField> dynamicFormFieldListToRpc(List<DynamicFormFieldDto> dynamicFormFieldDtoList) {
        List<DynamicFormField> dynamicFormFieldList = new ArrayList<>();
        if (CollectionUtils.isEmpty(dynamicFormFieldDtoList)) {
            return dynamicFormFieldList;
        }

        for (DynamicFormFieldDto dynamicFormFieldDto : dynamicFormFieldDtoList) {
            DynamicFormField dynamicFormField = new DynamicFormField();
            dynamicFormField.setFieldCode(dynamicFormFieldDto.getFieldCode());
            dynamicFormField.setFieldName(dynamicFormFieldDto.getFieldName());
            dynamicFormField.setMust(dynamicFormFieldDto.isMust());
            dynamicFormField.setComponent(dynamicFormFieldDto.getComponent());
            dynamicFormField.setDisplayed(dynamicFormFieldDto.isDisplayed());
            dynamicFormField.setHasDefault(dynamicFormFieldDto.isHasDefault());
            dynamicFormField.setDefaultValue(BooleanUtils.isTrue(dynamicFormFieldDto.isHasDefault()) ? dynamicFormFieldDto.getDefaultValue().toString() : "");
            dynamicFormField.setPlaceholder(dynamicFormFieldDto.getPlaceholder());
            dynamicFormFieldList.add(dynamicFormField);
        }
        return dynamicFormFieldList;
    }

    List<DynamicFormFieldDto> dynamicFormFieldListToDto(List<DynamicFormField> dynamicFormFieldList) {
        List<DynamicFormFieldDto> dynamicFormFieldDtoList = new ArrayList<>();
        if (CollectionUtils.isEmpty(dynamicFormFieldList)) {
            return dynamicFormFieldDtoList;
        }

        for (DynamicFormField dynamicFormField : dynamicFormFieldList) {
            DynamicFormFieldDto dynamicFormFieldDto = new DynamicFormFieldDto();
            String fieldCode = dynamicFormField.getFieldCode();
            String defaultValue = dynamicFormField.getDefaultValue();
            Object result;
            dynamicFormFieldDto.setFieldCode(fieldCode);
            dynamicFormFieldDto.setFieldName(dynamicFormField.getFieldName());
            dynamicFormFieldDto.setMust(dynamicFormField.isMust());
            dynamicFormFieldDto.setComponent(dynamicFormField.getComponent());
            dynamicFormFieldDto.setDisplayed(dynamicFormField.isDisplayed());
            dynamicFormFieldDto.setHasDefault(dynamicFormField.isHasDefault());
            dynamicFormFieldDto.setPlaceholder(dynamicFormField.getPlaceholder());

            // defaultValue非string就得还原成原始
            if ("datePhase".equals(fieldCode) && !dynamicFormFieldService.isValueEmpty(defaultValue)) {
                result = JSON.parseArray(defaultValue);
            } else if ("attachment".equals(fieldCode) && !dynamicFormFieldService.isValueEmpty(defaultValue)) {
                result = JSON.parseArray(defaultValue);
            } else if ("companyMembers".equals(fieldCode) && !dynamicFormFieldService.isValueEmpty(defaultValue)) {
                result = JSON.parseArray(defaultValue);
            } else if ("relationInvoice".equals(fieldCode) && !dynamicFormFieldService.isValueEmpty(defaultValue)) {
                result = JSON.parseArray(defaultValue);
            } else if ("tripRouteCity".equals(fieldCode) && !dynamicFormFieldService.isValueEmpty(defaultValue)) {
                result = JSON.parseArray(defaultValue);
            } else if ("cityCode".equals(fieldCode) && !dynamicFormFieldService.isValueEmpty(defaultValue)) {
                result = JSON.parseObject(defaultValue);
            } else if ("cityMultiple".equals(fieldCode) && !dynamicFormFieldService.isValueEmpty(defaultValue)) {
                result = JSON.parseArray(defaultValue);
            } else if ("tripCityMultiple".equals(fieldCode) && !dynamicFormFieldService.isValueEmpty(defaultValue)) {
                result = JSON.parseArray(defaultValue);
            } else {
                result = defaultValue;
            }
            dynamicFormFieldDto.setDefaultValue(result);
            dynamicFormFieldDtoList.add(dynamicFormFieldDto);
        }
        return dynamicFormFieldDtoList;
    }

    List<FileInfo> uploadFileInfoListToRpc(List<FileInfoDto> fileInfoDtoList) {
        List<FileInfo> fileInfoList = new ArrayList<>();
        if (CollectionUtils.isEmpty(fileInfoDtoList)) {
            return fileInfoList;
        }

        for (FileInfoDto fileInfoDto : fileInfoDtoList) {
            FileInfo fileInfo = new FileInfo();
            fileInfo.setFileId(fileInfoDto.getFileId());
            fileInfo.setBusiness(fileInfoDto.getBusiness());
            fileInfo.setScene(fileInfoDto.getScene());
            fileInfo.setName(fileInfoDto.getName());
            fileInfo.setMimeType(fileInfoDto.getMimeType());
            fileInfo.setUrl(fileInfoDto.getUrl());
            fileInfo.setDecryptedFileId(fileInfoDto.getDecryptedFileId());
            fileInfoList.add(fileInfo);
        }
        return fileInfoList;
    }

    List<FileInfoDto> fileInfoListToDto(List<FileInfo> fileInfoList) {
        List<FileInfoDto> fileInfoDtoList = new ArrayList<>();
        if (CollectionUtils.isEmpty(fileInfoList)) {
            return fileInfoDtoList;
        }

        for (FileInfo fileInfo : fileInfoList) {
            FileInfoDto fileInfoDto = new FileInfoDto();
            fileInfoDto.setFileId(fileInfo.getFileId());
            fileInfoDto.setBusiness(fileInfo.getBusiness());
            fileInfoDto.setScene(fileInfo.getScene());
            fileInfoDto.setName(fileInfo.getName());
            fileInfoDto.setMimeType(fileInfo.getMimeType());
            fileInfoDto.setUrl(fileInfo.getUrl());
            fileInfoDto.setDecryptedFileId(fileInfo.getDecryptedFileId());
            fileInfoDtoList.add(fileInfoDto);
        }
        multiCdnRpcService.batchGetAndSetFileUrl(fileInfoDtoList);
        return fileInfoDtoList;
    }

    List<InvoiceException> exceptionListToRpc(List<InvoiceExceptionDto> exceptionDtoList) {
        List<InvoiceException> exceptionList = new ArrayList<>();
        if (CollectionUtils.isEmpty(exceptionDtoList)) {
            return exceptionList;
        }

        for (InvoiceExceptionDto exceptionDto : exceptionDtoList) {
            InvoiceException invoiceException = new InvoiceException();
            invoiceException.setExceptionCode(exceptionDto.getExceptionCode());
            invoiceException.setExceptionName(exceptionDto.getExceptionName());
            invoiceException.setExceptionDes(exceptionDto.getExceptionDes());
            invoiceException.setExceptionIcon(exceptionDto.getExceptionIcon());
            invoiceException.setExceptionLevel(exceptionDto.getExceptionLevel());
            exceptionList.add(invoiceException);
        }
        return exceptionList;
    }

    public DynamicFormFieldListQueryDto rpcToDynamicFormFieldListQueryDto(QueryDynamicFormFieldListRequest request) {
        DynamicFormFieldListQueryDto dto = new DynamicFormFieldListQueryDto();
        dto.setDynamicFormType(request.getDynamicFormType());
        dto.setInvoiceType(request.getInvoiceType());
        dto.setReimburseType(request.getReimburseType());

        String firstSubject = request.getFirstSubject();
        dto.setFirstSubject(firstSubject);

        String secondSubject = request.getSecondSubject();
        dto.setSecondSubject(secondSubject);
        dto.setUserId(request.getUserId());
        return dto;
    }
}
