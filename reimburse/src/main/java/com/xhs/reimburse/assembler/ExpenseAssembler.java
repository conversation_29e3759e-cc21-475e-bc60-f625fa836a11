package com.xhs.reimburse.assembler;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.xhs.reimburse.assembler.mapper.ExpenseDtoMapper;
import com.xhs.reimburse.constant.CommonConstant;
import com.xhs.reimburse.enums.ExpenseFirstSubjectEnum;
import com.xhs.reimburse.enums.ExpenseSecondSubjectEnum;
import com.xhs.reimburse.enums.ExpenseStatusShowEnum;
import com.xhs.reimburse.modal.dto.DynamicFormFieldDto;
import com.xhs.reimburse.modal.dto.ExpenseDto;
import com.xhs.reimburse.modal.dto.FileInfoDto;
import com.xhs.reimburse.modal.dto.InvoiceDto;
import com.xhs.reimburse.modal.dto.TravelMealOverLimitReasonDto;
import com.xhs.reimburse.modal.entity.ExpenseEntity;
import com.xhs.reimburse.service.DynamicFormFieldService;
import com.xhs.reimburse.service.ExpenseCityService;
import com.xhs.reimburse.service.RelationReimbursementFormExpenseService;
import com.xiaohongshu.fls.rpc.oa.office.reimburse.entity.DynamicFormField;
import com.xiaohongshu.fls.rpc.oa.office.reimburse.entity.Expense;
import com.xiaohongshu.fls.rpc.oa.office.reimburse.entity.FileInfo;
import com.xiaohongshu.fls.rpc.oa.office.reimburse.entity.Invoice;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date :2025/02/20 - 下午3:03
 * @description :
 */
@Component
public class ExpenseAssembler {
    @Resource
    private ExpenseDtoMapper expenseDtoMapper;
    @Resource
    private DynamicFormFieldService dynamicFormFieldService;
    @Resource
    private CommonAssembler commonAssembler;
    @Resource
    private InvoiceAssembler invoiceAssembler;
    @Resource
    private ExpenseCityService expenseCityService;
    @Resource
    private RelationReimbursementFormExpenseService relationReimbursementFormExpenseService;

    public ExpenseDto toDto(ExpenseEntity entity) {
        ExpenseDto dto = expenseDtoMapper.toDto(entity);
        // 动态表单
        String details = entity.getDetails();
        List<DynamicFormFieldDto> dynamicFormFieldDtoList = JSON.parseArray(details, DynamicFormFieldDto.class);
        dynamicFormFieldService.buildDynamicFormDefaultValueMap(dynamicFormFieldDtoList);
        dto.setDynamicFormFieldDtoList(dynamicFormFieldDtoList);

        // 异常校验
        String exceptionDetails = entity.getExceptionDetails();
        Map<String, String> exceptionDetailsMap = StringUtils.isEmpty(exceptionDetails) || "{}".equals(exceptionDetails)
                ? new HashMap<>() :
                JSON.parseObject(exceptionDetails, new TypeReference<Map<String, String>>() {
                });
        dto.setExceptionDetailsMap(exceptionDetailsMap);

        // 差旅工作餐超标原因
        String travelMealOverLimitReason = entity.getTravelMealOverLimitReason();
        TravelMealOverLimitReasonDto travelMealOverLimitReasonDto = StringUtils.isEmpty(travelMealOverLimitReason) || "{}".equals(travelMealOverLimitReason)
                ? null
                : JSON.parseObject(travelMealOverLimitReason, TravelMealOverLimitReasonDto.class);
        dto.setTravelMealOverLimitReasonDto(travelMealOverLimitReasonDto);

        // 设置科目
        dto.setFirstSubjectName(ExpenseFirstSubjectEnum.getSubjectName(dto.getFirstSubject()));
        dto.setSecondSubjectName(ExpenseSecondSubjectEnum.getSubjectName(dto.getSecondSubject()));

        // 报销金额 有问题就给0
        // 校验规则：（1）是否为空 （2）是否能正常转为BigDecimal
        Object amountObject = dynamicFormFieldService.getDefaultValueObject(dynamicFormFieldDtoList, CommonConstant.EXPENSE_FIELD_ID_CODE_AMOUNT);
        BigDecimal amount = dynamicFormFieldService.isValueEmpty(amountObject) || dynamicFormFieldService.isAmountNotValid(String.valueOf(amountObject))
                ? BigDecimal.ZERO :
                new BigDecimal(String.valueOf(amountObject));
        dto.setAmount(amount.setScale(2, RoundingMode.HALF_UP));

        // 费用说明
        Object costCommentObject = dynamicFormFieldService.getDefaultValueObject(dynamicFormFieldDtoList, CommonConstant.EXPENSE_FIELD_ID_CODE_COST_COMMENT);
        if (dynamicFormFieldService.isValueEmpty(costCommentObject)) {
            dto.setCostComment("");
        } else {
            dto.setCostComment(String.valueOf(costCommentObject));
        }

        // 费用日期
        Object datePhaseObject = dynamicFormFieldService.getDefaultValueObject(dynamicFormFieldDtoList, CommonConstant.EXPENSE_FIELD_ID_CODE_DATE_PHASE);
        if(dynamicFormFieldService.isValueEmpty(datePhaseObject)){
            dto.setDatePhase("");
        }else {
            List<String> list = (List<String>) datePhaseObject;
            dto.setDatePhase(String.format("%s~%s", list.get(0), list.get(1)));
        }

        // 费用明细
        Object object = dynamicFormFieldService.getDefaultValueObject(dto.getDynamicFormFieldDtoList(), CommonConstant.EXPENSE_FIELD_ID_CODE_RELATION_INVOICE);
        dto.setRelationInvoiceNumber(dynamicFormFieldService.isValueEmpty(object) ? 0 : ((List<String>) object).size());

        // 附件
        Object uploadObject = dynamicFormFieldService.getDefaultValueObject(dto.getDynamicFormFieldDtoList(), CommonConstant.EXPENSE_FIELD_ID_CODE_UPLOADER);
        dto.setUploadFileNumber(dynamicFormFieldService.isValueEmpty(uploadObject) ? 0 : ((List<String>) uploadObject).size());

        // 费用状态
        ExpenseStatusShowEnum expenseStatusShowEnum = ExpenseStatusShowEnum.getExpenseStatusShowEnum(dto.getExpenseStatus(), dto.getExpenseInvoiceStatus(), dto.getExpenseFormStatus());
        if (expenseStatusShowEnum != null) {
            dto.setExpenseStatusShowNumber(expenseStatusShowEnum.getCode());
            dto.setExpenseStatusShowName(expenseStatusShowEnum.getName());
        } else {
            dto.setExpenseStatusShowNumber(0);
            dto.setExpenseStatusShowName("异常状态");
        }

        // 设置关联的票据uuId
        dto.setRelationFormUuidList(relationReimbursementFormExpenseService.queryFromUuidsByExpenseUuid(dto.getUuid()));

        return dto;
    }

    public List<ExpenseDto> toDtoList(List<ExpenseEntity> entityList) {
        if (CollectionUtils.isEmpty(entityList)) {
            return Collections.emptyList();
        }
        return entityList.stream().map(this::toDto).collect(Collectors.toList());
    }

    public ExpenseEntity toEntity(ExpenseDto dto) {
        ExpenseEntity entity = expenseDtoMapper.toEntity(dto);

        // 动态表单转String
        dynamicFormFieldService.validateAndCorrectHasDefault(dto.getDynamicFormFieldDtoList());
        String dynamicJsonString = CollectionUtils.isEmpty(dto.getDynamicFormFieldDtoList())
                ? "[]"
                : JSON.toJSONString(dto.getDynamicFormFieldDtoList());
        entity.setDetails(dynamicJsonString);

        // 差旅工作餐超标原因
        TravelMealOverLimitReasonDto travelMealOverLimitReasonDto = dto.getTravelMealOverLimitReasonDto();
        String travelMealOverLimitReason = travelMealOverLimitReasonDto == null
                ? "{}"
                :  JSON.toJSONString(travelMealOverLimitReasonDto);
        entity.setTravelMealOverLimitReason(travelMealOverLimitReason);

        // 超标状态
        entity.setExceptionDetails(CollectionUtils.isEmpty(dto.getExceptionDetailsMap()) ? "{}" : JSON.toJSONString(dto.getExceptionDetailsMap()));

        return entity;
    }

    public List<ExpenseEntity> toEntityList(List<ExpenseDto> dtoList) {
        List<ExpenseEntity> entityList = expenseDtoMapper.toEntityList(dtoList);
        // TODO
        return entityList;
    }

    public ExpenseDto rpcToDto(Expense expense) {
        ExpenseDto dto = new ExpenseDto();
        dto.setUuid(expense.getUuid());
        dto.setFormType(expense.getFormType());
        dto.setFirstSubject(expense.getFirstSubject());
        dto.setSecondSubject(expense.getSecondSubject());
        dto.setCreatorUserId(expense.getCreatorUserId());
        dto.setBeyondStandardInfo(expense.getBeyondStandardInfo());
        dto.setExceptionDetailsMap(expense.getExceptionDetailsMap());

        List<FileInfo> fileInfoList = expense.getFileInfoList();
        List<FileInfoDto> fileInfoDtoList = commonAssembler.fileInfoListToDto(fileInfoList);
        dto.setUploadFileInfoList(fileInfoDtoList);

        List<DynamicFormField> dynamicFieldList = expense.getDynamicFieldList();
        List<DynamicFormFieldDto> dynamicFormFieldDtoList = commonAssembler.dynamicFormFieldListToDto(dynamicFieldList);
        dto.setDynamicFormFieldDtoList(dynamicFormFieldDtoList);
        return dto;
    }

    public List<Expense> dtoListToRpc(List<ExpenseDto> dtoList) {
        List<Expense> expenseList = new ArrayList<>();
        if (CollectionUtils.isEmpty(dtoList)) {
            return expenseList;
        }

        for (ExpenseDto dto : dtoList) {
            Expense expense = new Expense();
            expense.setUuid(dto.getUuid());
            expense.setFormType(dto.getFormType());
            expense.setFirstSubject(dto.getFirstSubject());
            expense.setSecondSubject(dto.getSecondSubject());
            expense.setCreatorUserId(dto.getCreatorUserId());
            expense.setStatus(dto.getStatus());
            expense.setExpenseStatus(dto.getExpenseStatus());
            expense.setExpenseSource(dto.getExpenseSource());
            expense.setBeyondStandardInfo(dto.getBeyondStandardInfo());
            expense.setExceptionDetailsMap(dto.getExceptionDetailsMap());

            List<DynamicFormFieldDto> dynamicFormFieldDtoList = dto.getDynamicFormFieldDtoList();
            List<DynamicFormField> dynamicFormFieldList = commonAssembler.dynamicFormFieldListToRpc(dynamicFormFieldDtoList);
            expense.setDynamicFieldList(dynamicFormFieldList);

            List<FileInfoDto> uploadFileInfoList = dto.getUploadFileInfoList();
            List<FileInfo> fileInfoList = commonAssembler.uploadFileInfoListToRpc(uploadFileInfoList);
            expense.setFileInfoList(fileInfoList);

            List<InvoiceDto> invoiceDtoList = dto.getRelationInvoiceList();
            List<Invoice> invoiceList = invoiceAssembler.dtoListToRpc(invoiceDtoList);
            expense.setInvoiceList(invoiceList);

            //*****************报销入账查询使用 start****************
            expense.setAmount(dto.getAmount().toString());
            expense.setExpenseAbstract(dto.getCostComment());
            expense.setDateRange(dto.getDatePhase());
            //*****************报销入账查询使用 end****************

            expenseList.add(expense);
        }
        return expenseList;
    }

    //获取费用金额
    public BigDecimal parseExpenseAmount(ExpenseEntity entity) {

        if (Objects.isNull(entity)) {
            return BigDecimal.ZERO;
        }

        // 动态表单
        List<DynamicFormFieldDto> dynamicFormFieldDtoList = JSON.parseArray(entity.getDetails(), DynamicFormFieldDto.class);

        // 报销金额 有问题就给0
        // 校验规则：（1）是否为空 （2）是否能正常转为BigDecimal
        Object amountObject = dynamicFormFieldService.getDefaultValueObject(dynamicFormFieldDtoList, CommonConstant.EXPENSE_FIELD_ID_CODE_AMOUNT);

        return dynamicFormFieldService.isValueEmpty(amountObject) || dynamicFormFieldService.isAmountNotValid(String.valueOf(amountObject)) ?
                BigDecimal.ZERO :
                new BigDecimal(String.valueOf(amountObject));
    }
}
