package com.xhs.reimburse.assembler.mapper;

import com.xhs.reimburse.modal.dto.BankAccountDto;
import com.xhs.reimburse.modal.entity.BankAccountEntity;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date :2025/02/07 - 下午9:37
 * @description :
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BankAccountDtoMapper {
    BankAccountDto toDto(BankAccountEntity entity);

    List<BankAccountDto> toDtoList(List<BankAccountEntity> entityList);

    BankAccountEntity toEntity(BankAccountDto dto);

    List<BankAccountEntity> toEntityList(List<BankAccountDto> dtoList);
}
