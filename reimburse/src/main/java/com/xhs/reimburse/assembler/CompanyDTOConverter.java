package com.xhs.reimburse.assembler;

import com.xhs.reimburse.modal.dto.CheckNeedPayDto;
import com.xhs.reimburse.modal.dto.ReimburseBasicInfo;
import com.xhs.reimburse.modal.request.ReimburseFormRequest;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Component
public class CompanyDTOConverter {

    public CheckNeedPayDto toCheckParam(ReimburseFormRequest formRequest) {
        if (Objects.nonNull(formRequest)) {
            Set<Integer> payWaySet = new HashSet<>();
            ReimburseBasicInfo reimburseBasicInfoVo = formRequest.getReimburseBasicInfoVo();
            CheckNeedPayDto checkNeedPayDto = new CheckNeedPayDto();
            checkNeedPayDto.setFormType(formRequest.getFormType());
            checkNeedPayDto.setCompanyNameList(Arrays.asList(reimburseBasicInfoVo.getPaymentCompanyName()));
            payWaySet.add(formRequest.getPaymentType());
            checkNeedPayDto.setPaymentTypeList(payWaySet);
            checkNeedPayDto.setAccountType(formRequest.getAccountType());
            return checkNeedPayDto;
        }
        return null;
    }
}
