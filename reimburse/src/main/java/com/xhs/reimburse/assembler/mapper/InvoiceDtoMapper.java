package com.xhs.reimburse.assembler.mapper;

import com.xhs.reimburse.modal.dto.InvoiceDto;
import com.xhs.reimburse.modal.entity.InvoiceEntity;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date :2025/02/07 - 下午9:37
 * @description :
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface InvoiceDtoMapper {
    InvoiceDto toDto(InvoiceEntity entity);
    List<InvoiceDto> toDtoList(List<InvoiceEntity> entityList);

    InvoiceEntity toEntity(InvoiceDto dto);
    List<InvoiceEntity> toEntityList(List<InvoiceDto> dtoList);
}
