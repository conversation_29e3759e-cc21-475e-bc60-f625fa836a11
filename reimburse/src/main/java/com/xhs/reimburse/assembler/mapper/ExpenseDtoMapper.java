package com.xhs.reimburse.assembler.mapper;

import com.xhs.reimburse.modal.dto.ExpenseDto;
import com.xhs.reimburse.modal.entity.ExpenseEntity;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date :2025/02/07 - 下午9:37
 * @description :
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ExpenseDtoMapper {
    ExpenseDto toDto(ExpenseEntity entity);
    List<ExpenseDto> toDtoList(List<ExpenseEntity> entityList);

    ExpenseEntity toEntity(ExpenseDto dto);
    List<ExpenseEntity> toEntityList(List<ExpenseDto> dtoList);
}
