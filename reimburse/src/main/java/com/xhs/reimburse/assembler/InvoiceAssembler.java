package com.xhs.reimburse.assembler;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.xhs.reimburse.assembler.mapper.InvoiceDtoMapper;
import com.xhs.reimburse.constant.CommonConstant;
import com.xhs.reimburse.enums.InvoiceStatusShowEnum;
import com.xhs.reimburse.enums.InvoiceTypeEnum;
import com.xhs.reimburse.modal.dto.DynamicFormFieldDto;
import com.xhs.reimburse.modal.dto.FileInfoDto;
import com.xhs.reimburse.modal.dto.InvoiceDto;
import com.xhs.reimburse.modal.dto.InvoiceExceptionDto;
import com.xhs.reimburse.modal.entity.InvoiceEntity;
import com.xhs.reimburse.modal.request.BatchOcrParseRequest;
import com.xhs.reimburse.rpc.consumer.MultiCdnRpcService;
import com.xhs.reimburse.service.DynamicFormFieldService;
import com.xhs.reimburse.utils.StreamUtil;
import com.xiaohongshu.fls.rpc.oa.office.reimburse.entity.DynamicFormField;
import com.xiaohongshu.fls.rpc.oa.office.reimburse.entity.FileInfo;
import com.xiaohongshu.fls.rpc.oa.office.reimburse.entity.Invoice;
import com.xiaohongshu.fls.rpc.oa.office.reimburse.entity.InvoiceException;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date :2025/02/20 - 下午3:04
 * @description :
 */
@Component
public class InvoiceAssembler {
    @Resource
    private InvoiceDtoMapper invoiceDtoMapper;
    @Resource
    private MultiCdnRpcService multiCdnRpcService;
    @Resource
    private DynamicFormFieldService dynamicFormFieldService;
    @Resource
    private CommonAssembler commonAssembler;

    public InvoiceDto toDto(InvoiceEntity entity) {
        InvoiceDto dto = toBaseDto(entity);
        batchSetFileNewUrl(Collections.singletonList(dto.getUploadFileInfo()));
        return dto;
    }

    private InvoiceDto toBaseDto(InvoiceEntity entity) {
        InvoiceDto dto = invoiceDtoMapper.toDto(entity);

        // 动态表单
        String details = entity.getDetails();
        List<DynamicFormFieldDto> dynamicFormFieldDtoList = JSON.parseArray(details, DynamicFormFieldDto.class);
        dynamicFormFieldService.buildDynamicFormDefaultValueMap(dynamicFormFieldDtoList);
        dto.setDynamicFormFieldDtoList(dynamicFormFieldDtoList);

        // 异常状态
        String exceptions = entity.getException();
        List<InvoiceExceptionDto> InvoiceExceptionDtoList = JSON.parseArray(exceptions, InvoiceExceptionDto.class);
        dto.setExceptionList(InvoiceExceptionDtoList);
        Optional.ofNullable(entity.getStatus()).ifPresent(dto::setStatus);
        // 文件信息
        FileInfoDto fileInfoDto = JSON.parseObject(entity.getUploadFile(), FileInfoDto.class);
        dto.setUploadFileInfo(fileInfoDto);

        // 发票tag
        dto.setInvoiceTypeTag(InvoiceTypeEnum.getInvoiceTag(entity.getTicketType()));

        // 购方税号
        Object purchaserTaxNoObject = dynamicFormFieldService.getDefaultValueObject(dynamicFormFieldDtoList, CommonConstant.INVOICE_FIELD_ID_CODE_PURCHASER_TAX_NO);
        dto.setPurchaserTaxNo(dynamicFormFieldService.isValueEmpty(purchaserTaxNoObject) ?  "" : String.valueOf(purchaserTaxNoObject));

        // 购方名称
        Object purchaserNameobject = dynamicFormFieldService.getDefaultValueObject(dynamicFormFieldDtoList, CommonConstant.INVOICE_FIELD_ID_CODE_PURCHASER_NAME);
        dto.setPurchaserName(dynamicFormFieldService.isValueEmpty(purchaserNameobject) ?  "" : String.valueOf(purchaserNameobject));

        // 金额
        Object totalAmountObject = dynamicFormFieldService.getDefaultValueObject(dynamicFormFieldDtoList, CommonConstant.INVOICE_FIELD_ID_CODE_TOTAL_AMOUNT);
        BigDecimal amount = dynamicFormFieldService.isValueEmpty(totalAmountObject) ? BigDecimal.ZERO : new BigDecimal(String.valueOf(totalAmountObject));
        dto.setAmount(amount.setScale(2, RoundingMode.HALF_UP));

        // 开票时间
        Object invoiceDateObject = dynamicFormFieldService.getDefaultValueObject(dynamicFormFieldDtoList, CommonConstant.INVOICE_FIELD_ID_CODE_INVOICE_DATE);
        dto.setInvoiceDate((dynamicFormFieldService.isValueEmpty(invoiceDateObject) ?  "" : String.valueOf(invoiceDateObject)));

        // 设置发票实际状态
        InvoiceStatusShowEnum statusShowEnum = InvoiceStatusShowEnum.getInvoiceStatusShowEnum(dto.getInvoiceStatus(), dto.getInvoiceExpenseStatus(), dto.getInvoiceFormStatus());
        if (statusShowEnum != null) {
            dto.setInvoiceStatusShowNumber(statusShowEnum.getCode());
            dto.setInvoiceStatusShowName(statusShowEnum.getName());
        } else {
            dto.setInvoiceStatusShowNumber(0);
            dto.setInvoiceStatusShowName("异常状态");
        }

        return dto;
    }

    public List<InvoiceDto> toDtoList(List<InvoiceEntity> entityList) {
        List<InvoiceDto> list = entityList.stream().map(this::toBaseDto).collect(Collectors.toList());

        List<FileInfoDto> fileInfoDtoList = StreamUtil.toList(list, InvoiceDto::getUploadFileInfo)
                .stream().filter(fileInfoDto -> {
                    return !StringUtils.isEmpty(fileInfoDto.getFileId()) && !StringUtils.isEmpty(fileInfoDto.getScene()) && !StringUtils.isEmpty(fileInfoDto.getBusiness());
                }).collect(Collectors.toList());

        batchSetFileNewUrl(fileInfoDtoList);
        return list;
    }

    public InvoiceEntity toEntity(InvoiceDto dto) {
        InvoiceEntity entity = invoiceDtoMapper.toEntity(dto);

        // 动态表单转String
        dynamicFormFieldService.validateAndCorrectHasDefault(dto.getDynamicFormFieldDtoList());
        String dynamicJsonString = CollectionUtils.isEmpty(dto.getDynamicFormFieldDtoList())
                ? "[]"
                : JSON.toJSONString(dto.getDynamicFormFieldDtoList());
        entity.setDetails(dynamicJsonString);

        // 异常状态
        String exceptionJsonString = CollectionUtils.isEmpty(dto.getExceptionList())
                ? "[]"
                : JSON.toJSONString(dto.getExceptionList());
        entity.setException(exceptionJsonString);

        // 文件信息
        String fileInfo = dto.getUploadFileInfo() == null
                ? "{}"
                : JSON.toJSONString(dto.getUploadFileInfo());
        entity.setUploadFile(fileInfo);


        return entity;
    }

    public void batchSetFileNewUrl(List<FileInfoDto> fileInfoDtoList) {
        multiCdnRpcService.batchGetAndSetFileUrl(fileInfoDtoList);
    }

    public List<InvoiceEntity> toEntityList(List<InvoiceDto> dtoList) {
        return dtoList.stream().map(this::toEntity).collect(Collectors.toList());
    }

    public List<Invoice> dtoListToRpc(List<InvoiceDto> dtoList) {
        List<Invoice> invoiceList = new ArrayList<>();
        if (CollectionUtils.isEmpty(dtoList)) {
            return invoiceList;
        }

        for (InvoiceDto dto : dtoList) {
            Invoice invoice = new Invoice();
            invoice.setUuid(dto.getUuid());
            invoice.setTicketType(dto.getTicketType());
            invoice.setInvoiceType(dto.getInvoiceType());
            invoice.setInvoiceNo(dto.getInvoiceNo());
            invoice.setInvoiceCode(dto.getInvoiceCode());
            String amount = Optional.ofNullable(dto.getAmount()).orElse(new BigDecimal(0)).toString();
            invoice.setAmount(amount);
            invoice.setInvoiceDate(dto.getInvoiceDate());
            invoice.setOcrSourceContent(dto.getOcrSourceContent());
            invoice.setCreatorUserId(dto.getCreatorUserId());
            invoice.setInvoiceTypeTag(dto.getInvoiceTypeTag());
            Optional.ofNullable(dto.getStatus()).ifPresent(invoice::setStatus);
            if (Objects.nonNull(dto.getInvoiceStatus())) {
                invoice.setInvoiceStatus(dto.getInvoiceStatus());
            }
            if (Objects.nonNull(dto.getInvoiceExpenseStatus())) {
                invoice.setInvoiceExpenseStatus(dto.getInvoiceExpenseStatus());
            }
            if (Objects.nonNull(dto.getInvoiceFormStatus())) {
                invoice.setInvoiceFormStatus(dto.getInvoiceFormStatus());
            }
            List<InvoiceExceptionDto> exceptionDtoList = dto.getExceptionList();
            List<InvoiceException> exceptionList = commonAssembler.exceptionListToRpc(exceptionDtoList);
            invoice.setExceptionList(exceptionList);

            List<DynamicFormFieldDto> dynamicFormFieldDtoList = dto.getDynamicFormFieldDtoList();
            List<DynamicFormField> dynamicFormFieldList = commonAssembler.dynamicFormFieldListToRpc(dynamicFormFieldDtoList);
            invoice.setDynamicFieldList(dynamicFormFieldList);

            FileInfoDto uploadFileInfo = dto.getUploadFileInfo();
            List<FileInfo> fileInfoList = commonAssembler.uploadFileInfoListToRpc(Collections.singletonList(uploadFileInfo));
            invoice.setFileInfo(CollectionUtils.isEmpty(fileInfoList) ? null : fileInfoList.get(0));
            invoiceList.add(invoice);
        }
        return invoiceList;
    }

    public BatchOcrParseRequest fileInfoListToRequest(List<FileInfo> fileUrlList) {
        BatchOcrParseRequest request = new BatchOcrParseRequest();
        if (CollectionUtils.isEmpty(fileUrlList)) {
            return request;
        }

        List<FileInfoDto> fileInfoDtoList = commonAssembler.fileInfoListToDto(fileUrlList);
        request.setOrcParseItems(fileInfoDtoList);
        return request;
    }
}
