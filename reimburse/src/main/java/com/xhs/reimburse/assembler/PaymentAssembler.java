package com.xhs.reimburse.assembler;

import com.xhs.reimburse.modal.dto.PaymentCheckDto;
import com.xhs.reimburse.modal.dto.PaymentInfoDto;
import com.xhs.reimburse.modal.dto.ReimburseBasicInfo;
import com.xhs.reimburse.modal.request.ReimburseFormRequest;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@Component
public class PaymentAssembler {

    public PaymentCheckDto toCheckDto(ReimburseFormRequest formRequest, PaymentInfoDto paymentInfo) {
        if (Objects.nonNull(formRequest) && Objects.nonNull(paymentInfo)) {
            ReimburseBasicInfo reimburseBasicInfoVo = formRequest.getReimburseBasicInfoVo();
            PaymentCheckDto dto = new PaymentCheckDto();
            dto.setAmount(formRequest.getAmount());
            dto.setSubject(paymentInfo.getSubjectCode());
            dto.setPaymentNo("OA" + UUID.randomUUID().toString().replaceAll("-", ""));
            dto.setPaymentCurrency(formRequest.getCurrency());
            dto.setPaymentAbstract(formRequest.getFormType());
            dto.setGatheringName(reimburseBasicInfoVo.getGatheringName());
            dto.setGatheringAccount(reimburseBasicInfoVo.getGatheringAccount());
            dto.setBankName(reimburseBasicInfoVo.getGatheringBank());
            dto.setBankCode(reimburseBasicInfoVo.getGatheringBankCode());
            dto.setAccountType(formRequest.getAccountType());
            dto.setPaymentType(formRequest.getPaymentType());
            return dto;
        }
        return null;
    }
}
