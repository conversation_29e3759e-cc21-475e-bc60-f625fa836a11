package com.xhs.reimburse.assembler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.xhs.reimburse.modal.entity.travel.RelationTravelReimbursementForm;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class TravelReimbursementFormRelationConverter {


    public List<RelationTravelReimbursementForm> convert2Entity(String formUuid, List<String> travelApplyFormNums, String userId) {

        if (StrUtil.isBlank(formUuid) || CollUtil.isEmpty(travelApplyFormNums)) {
            return null;
        }

        List<RelationTravelReimbursementForm> relations = new ArrayList<>();
        travelApplyFormNums.forEach(travelApplyFormNum -> {

            RelationTravelReimbursementForm relation = new RelationTravelReimbursementForm();
            relation.setCreatorNo(userId);
            relation.setUpdaterNo(userId);
            relation.setReimbursementId(formUuid);
            relation.setTravelFormNum(travelApplyFormNum);
            relations.add(relation);
        });

        return relations;
    }
}
