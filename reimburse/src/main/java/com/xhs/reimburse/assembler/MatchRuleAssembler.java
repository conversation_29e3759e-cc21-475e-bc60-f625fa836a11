package com.xhs.reimburse.assembler;

import com.xhs.reimburse.modal.dto.ExpenseMatchRuleDto;
import com.xhs.reimburse.modal.dto.ExpenseQueryRuleDto;
import com.xhs.reimburse.modal.dto.InvoiceMatchRuleDto;
import com.xhs.reimburse.modal.dto.InvoiceQueryRuleDto;
import com.xiaohongshu.fls.rpc.oa.office.reimburse.entity.ExpenseMatchRule;
import com.xiaohongshu.fls.rpc.oa.office.reimburse.entity.ExpenseQueryRule;
import com.xiaohongshu.fls.rpc.oa.office.reimburse.entity.InvoiceMatchRule;
import com.xiaohongshu.fls.rpc.oa.office.reimburse.entity.InvoiceQueryRule;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date :2025/02/22 - 下午3:14
 * @description :
 */
@Component
public class MatchRuleAssembler {

    public InvoiceMatchRuleDto invoiceMatchRuleToDto(InvoiceMatchRule invoiceMatchRule) {
        InvoiceMatchRuleDto dto = new InvoiceMatchRuleDto();
        String userId = Optional.ofNullable(invoiceMatchRule.getUserId()).orElse("");
        String amount = Optional.ofNullable(invoiceMatchRule.getAmount()).orElse("");
        String amountDiff = Optional.ofNullable(invoiceMatchRule.getAmountDiff()).orElse("0");
        String time = Optional.ofNullable(invoiceMatchRule.getTime()).orElse("");
        List<Integer> invoiceStatusList = Optional.ofNullable(invoiceMatchRule.getInvoiceStatusList()).orElse(new ArrayList<>());
        List<Integer> invoiceExpenseStatusList = Optional.ofNullable(invoiceMatchRule.getInvoiceExpenseStatusList()).orElse(new ArrayList<>());

        dto.setUserId(userId);
        dto.setAmount(amount);
        dto.setAmountDiff(amountDiff);
        dto.setTime(time);
        dto.setInvoiceStatusList(invoiceStatusList);
        dto.setInvoiceExpenseStatusList(invoiceExpenseStatusList);
        return dto;
    }

    public ExpenseMatchRuleDto expenseMatchRuleToDto(ExpenseMatchRule expenseMatchRule) {
        ExpenseMatchRuleDto dto = new ExpenseMatchRuleDto();
        String userId = Optional.ofNullable(expenseMatchRule.getUserId()).orElse("");
        String amount = Optional.ofNullable(expenseMatchRule.getAmount()).orElse("");
        String amountDiff = Optional.ofNullable(expenseMatchRule.getAmountDiff()).orElse("0");
        String time = Optional.ofNullable(expenseMatchRule.getTime()).orElse("");
        List<Integer> invoiceStatusList = Optional.ofNullable(expenseMatchRule.getExpenseStatusList()).orElse(new ArrayList<>());
        List<Integer> invoiceExpenseStatusList = Optional.ofNullable(expenseMatchRule.getExpenseFormStatusList()).orElse(new ArrayList<>());

        dto.setUserId(userId);
        dto.setAmount(amount);
        dto.setAmountDiff(amountDiff);
        dto.setTime(time);
        dto.setExpenseStatusList(invoiceStatusList);
        dto.setExpenseFormStatusList(invoiceExpenseStatusList);
        return dto;
    }

    public ExpenseQueryRuleDto expenseQueryRuleToDto(ExpenseQueryRule expenseQueryRule) {
        ExpenseQueryRuleDto dto = new ExpenseQueryRuleDto();
        String userId = Optional.ofNullable(expenseQueryRule.getUserId()).orElse("");
        String amount = Optional.ofNullable(expenseQueryRule.getAmount()).orElse("");
        String amountDiff = Optional.ofNullable(expenseQueryRule.getAmountDiff()).orElse("0");
        String startTime = Optional.ofNullable(expenseQueryRule.getStartTime()).orElse("");
        String endTime = Optional.ofNullable(expenseQueryRule.getEndTime()).orElse("");
        List<Integer> invoiceStatusList = Optional.ofNullable(expenseQueryRule.getExpenseStatusList()).orElse(new ArrayList<>());
        List<Integer> invoiceExpenseStatusList = Optional.ofNullable(expenseQueryRule.getExpenseFormStatusList()).orElse(new ArrayList<>());

        dto.setUserId(userId);
        dto.setAmount(amount);
        dto.setAmountDiff(amountDiff);
        dto.setStartTime(startTime);
        dto.setEndTime(endTime);
        dto.setExpenseStatusList(invoiceStatusList);
        dto.setExpenseFormStatusList(invoiceExpenseStatusList);
        return dto;
    }

    public InvoiceQueryRuleDto invoiceQueryRuleToDto(InvoiceQueryRule invoiceQueryRule) {
        InvoiceQueryRuleDto dto = new InvoiceQueryRuleDto();
        String userId = Optional.ofNullable(invoiceQueryRule.getUserId()).orElse("");
        String amount = Optional.ofNullable(invoiceQueryRule.getAmount()).orElse("");
        String amountDiff = Optional.ofNullable(invoiceQueryRule.getAmountDiff()).orElse("0");
        String startTime = Optional.ofNullable(invoiceQueryRule.getStartTime()).orElse("");
        String endTime = Optional.ofNullable(invoiceQueryRule.getEndTime()).orElse("");
        List<Integer> invoiceStatusList = Optional.ofNullable(invoiceQueryRule.getInvoiceStatusList()).orElse(new ArrayList<>());
        List<Integer> invoiceExpenseStatusList = Optional.ofNullable(invoiceQueryRule.getInvoiceExpenseStatusList()).orElse(new ArrayList<>());

        dto.setUserId(userId);
        dto.setAmount(amount);
        dto.setAmountDiff(amountDiff);
        dto.setStartTime(startTime);
        dto.setEndTime(endTime);
        dto.setInvoiceStatusList(invoiceStatusList);
        dto.setInvoiceExpenseStatusList(invoiceExpenseStatusList);
        return dto;
    }
}
