package com.xhs.reimburse.assembler.mapper;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.xhs.ehr.rpc.response.EmployeeInfo;
import com.xhs.reimburse.modal.entity.EmployeeEntity;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class EhrEmployeeMapper {

    @ApolloJsonValue("${workplaceMap:{}}")
    private Map<String, String> workplaceMap;

    @ApolloJsonValue("${ehr_italent_work_place_map:{}}")
    private Map<String, String> beiSengWorkPlaceMap;

    public EmployeeEntity baseToEntity(EmployeeInfo employeeInfo) {
        if (Objects.nonNull(employeeInfo)) {
            EmployeeEntity employeeEntity = new EmployeeEntity();
            employeeEntity.setCompany(employeeInfo.getCompanyName());
            employeeEntity.setUserId(String.valueOf(employeeInfo.getEmployeeId()));
            employeeEntity.setUserName(employeeInfo.getShowName());
            employeeEntity.setEmployeeType(employeeInfo.getEmploymentType());
            employeeEntity.setDepartmentId(String.valueOf(employeeInfo.getDepartmentId()));
            employeeEntity.setDepartmentName(employeeInfo.getDepartmentName());
            String departmentIdPath = employeeInfo.getDepartmentIdPath();
            List<String> departmentIdPathList = StrUtil.isBlank(departmentIdPath) ? ListUtil.empty() : Arrays.asList(departmentIdPath.split(","));
            employeeEntity.setDepartmentIdPath(departmentIdPathList);
            String departmentNamePath = employeeInfo.getDepartmentNamePath();
            List<String> departmentNamePathList = StrUtil.isBlank(departmentNamePath) ? ListUtil.empty() : Arrays.asList(departmentNamePath.split(","));
            employeeEntity.setDepartmentNamePath(departmentNamePathList);
            String redMail = employeeInfo.getRedMail();
            employeeEntity.setUserEmail(StrUtil.emptyToDefault(redMail, employeeInfo.getEmail()));
            String workplaceCode = String.valueOf(employeeInfo.getWorkPlace());
            String beiSenWorkplaceCode = beiSengWorkPlaceMap.get(workplaceCode);
            String finalWorkplaceCode = StrUtil.isBlank(beiSenWorkplaceCode) ? workplaceCode : beiSenWorkplaceCode;
            employeeEntity.setWorkplace(workplaceMap.getOrDefault(finalWorkplaceCode, ""));
            employeeEntity.setWorkplaceCode(finalWorkplaceCode);
            return employeeEntity;
        }
        return null;
    }

    public List<EmployeeEntity> baseToEntityList(List<EmployeeInfo> employeeInfoList) {
        if (CollectionUtils.isEmpty(employeeInfoList)) {
            return Collections.emptyList();
        }
        return employeeInfoList.stream().map(this::baseToEntity).collect(Collectors.toList());
    }
}
