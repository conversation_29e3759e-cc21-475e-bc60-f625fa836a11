package com.xhs.reimburse.assembler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.xhs.reimburse.modal.dto.travel.TravelerDto;
import com.xhs.reimburse.modal.response.TravelApplySummaryInfo;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class TravelApplyAssembler {

    public List<TravelApplySummaryInfo> setTravelTravelerName(List<TravelApplySummaryInfo> infos) {

        if (CollUtil.isNotEmpty(infos)) {
            infos.forEach(info -> {

                List<TravelerDto> travelers = info.getTravelers();
                if (CollUtil.isEmpty(travelers)) {
                    return;
                }

                List<String> travelNames = travelers.stream()
                        .map(TravelerDto::getUserName).filter(StrUtil::isNotBlank).collect(Collectors.toList());

                info.setTraveler(String.join(",", travelNames));
            });
        }
        return infos;
    }
}
