package com.xhs.reimburse.assembler.mapper;

import com.xhs.ehr.rpc.response.DepartmentInfo;
import com.xhs.reimburse.modal.entity.EhrDepartmentEntity;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface EhrDepartmentMapper {

    EhrDepartmentEntity baseToEntity(DepartmentInfo departmentInfo);

    List<EhrDepartmentEntity> baseToEntity(List<DepartmentInfo> departmentInfos);

}