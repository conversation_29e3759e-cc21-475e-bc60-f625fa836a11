package com.xhs.reimburse.common;

import com.xhs.reimburse.mq.consumer.ExternalInvoiceEnterProcessor;
import com.xhs.reimburse.mq.consumer.TravelApplyChangeProcessor;
import com.xiaohongshu.events.client.consumer.EventsPushConsumer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
public class ReimburseEventClientConfiguration {

    @Value("${reimburse.event.external.invoice.enter.group}")
    private String externalInvoiceEnterGroup;

    @Value("${reimburse.event.external.invoice.enter.topic}")
    private String externalInvoiceEnterTopic;

    @Value("${travel.apply.schedule.change.group}")
    private String travelApplyScheduleChangeGroup;

    @Value("${travel.apply.schedule.change.topic}")
    private String travelApplyScheduleChangeTopic;

    @Bean(name = "externalInvoiceEnterConsumer")
    public EventsPushConsumer externalInvoiceEnterConsumer(ExternalInvoiceEnterProcessor processor) {
        EventsPushConsumer consumer = new EventsPushConsumer();
        consumer.setGroup(externalInvoiceEnterGroup);
        consumer.setTopic(externalInvoiceEnterTopic);
        consumer.setMessageProcessor(processor);
        consumer.setSubmitMaxRetries(3);
        consumer.start();
        return consumer;
    }

    @Bean(name = "travelApplyChangeConsumer")
    public EventsPushConsumer travelApplyChangeProcessorConsumer(TravelApplyChangeProcessor processor) {
        EventsPushConsumer consumer = new EventsPushConsumer();
        consumer.setGroup(travelApplyScheduleChangeGroup);
        consumer.setTopic(travelApplyScheduleChangeTopic);
        consumer.setMessageProcessor(processor);
        consumer.setSubmitMaxRetries(3);
        consumer.start();
        return consumer;
    }

}