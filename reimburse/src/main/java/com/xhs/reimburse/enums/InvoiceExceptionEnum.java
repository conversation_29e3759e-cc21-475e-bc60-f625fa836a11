package com.xhs.reimburse.enums;

import com.xhs.reimburse.modal.dto.InvoiceExceptionDto;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName InvoiceExceptionEnum.java
 * @createTime 2025年02月22日 16:24:00
 */
@AllArgsConstructor
@Getter
public enum InvoiceExceptionEnum {

    // OVERDUE_90_DAYS(1, "超90天", "发票已超过90天", "icon_overdue", "HIGH"),
    SUBJECT_MISMATCH(2, "主体异常", "该张发票的购买方主体与你的开票主体%s不一致，会影响提报，请检查是否需要重新开票～", "icon_mismatch", "MEDIUM","主体不一致"),
    AUTHENTICATION_FAILED(3, "验真失败", "该张发票验真失败，不可提报", "icon_auth_failed", "HIGH","验真失败");

    private final Integer exceptionCode;
    private final String exceptionName;
    private final String exceptionDes;
    private final String exceptionIcon;
    private final String exceptionLevel;
    private final String exceptionTitle;

    public static InvoiceExceptionDto getInvoiceExceptionDto(InvoiceExceptionEnum invoiceExceptionEnum) {
        InvoiceExceptionDto exceptionDto = new InvoiceExceptionDto();
        exceptionDto.setExceptionCode(invoiceExceptionEnum.getExceptionCode());
        exceptionDto.setExceptionName(invoiceExceptionEnum.getExceptionName());
        exceptionDto.setExceptionIcon(invoiceExceptionEnum.getExceptionIcon());
        exceptionDto.setExceptionLevel(invoiceExceptionEnum.getExceptionLevel());
        exceptionDto.setExceptionDes(invoiceExceptionEnum.getExceptionDes());
        exceptionDto.setExceptionTitle(invoiceExceptionEnum.getExceptionTitle());
        return exceptionDto;
    }
}
