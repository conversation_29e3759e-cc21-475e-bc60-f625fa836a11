package com.xhs.reimburse.enums.travel;

import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TravelScheduleStatus.java
 * @createTime 2025年04月01日 19:42:00
 */
@Getter
public enum TravelScheduleStatus {
    /**
     * 未知
     */
    UNKNOWN(-1),

    /**
     * 已申请，用户提交时的初始状态
     */
    APPLIED(0),

    /**
     * 已审批通过，直属领导手动审核后转换成此状态，统计用
     */
    PASSED(1),

    /**
     * 已失效
     */
    INVALID(2),

    /**
     * 新状态，已接受，系统自动审核后转换成此状态，此状态可同步携程审批单
     */
    ACCEPTED(3),

    /**
     * 新状态，已取消，提报人通过取消行程接口触发，仍可能产生费用
     */
    CANCELED(4),

    /**
     * 新状态，取消已确认，已取消后置状态，移动端月报专用，取消差旅后，如有费用回传，移动端仍需要进行审批，这部分审批数据通过审批记录的方式来记录
     */
    CANCELED_CONFIRMED(5),

    /**
     * 新状态，取消已拒绝，已取消后置状态
     */
    CANCELED_REFUSE(6),

    /**
     * 新状态，被质疑，直属领导手动拒绝后转换成此状态，统计用
     */
    CHALLENGED(7),
    ;

    private int code;

    TravelScheduleStatus(int code) {
        this.code = code;
    }

    public static List<Integer> getPassStatusList() {
        return Lists.newArrayList(PASSED.getCode(), ACCEPTED.getCode());
    }
}
