package com.xhs.reimburse.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName OcrSingleInvoiceTypeEnum.java
 * @createTime 2025年02月26日 22:43:00
 */
@Getter
@AllArgsConstructor
public enum OcrSingleInvoiceTypeEnum {

    VAT_SPECIAL_INVOICE("100", "增值税专用发票", InvoiceTypeEnum.OTHER),
    VAT_ORDINARY_INVOICE("101", "增值税普通发票", InvoiceTypeEnum.OTHER),
    ELECTRONIC_VAT_ORDINARY_INVOICE("102", "增值税电子普通发票", InvoiceTypeEnum.OTHER),
    ELECTRONIC_VAT_SPECIAL_INVOICE("103", "增值税电子专用发票", InvoiceTypeEnum.OTHER),
    ELECTRONIC_INVOICE_SPECIAL("107", "电子发票（增值税专用发票）", InvoiceTypeEnum.OTHER),
    ELECTRONIC_INVOICE_ORDINARY("108", "电子发票（普通发票）", InvoiceTypeEnum.OTHER),
    MOTOR_VEHICLE_SALES_INVOICE("200", "机动车销售统一发票", InvoiceTypeEnum.OTHER),
    USED_CAR_SALES_INVOICE("201", "二手车销售统一发票", InvoiceTypeEnum.OTHER),
    VAT_ORDINARY_ROLL_INVOICE("300", "增值税普通发票（卷票)", InvoiceTypeEnum.OTHER),
    TRAIN_TICKET("301", "火车票", InvoiceTypeEnum.OTHER),
    AIR_TICKET_ITINERARY("302", "飞机票（电子客票行程单）", InvoiceTypeEnum.OTHER),
    TAXI_INVOICE("303", "出租车发票", InvoiceTypeEnum.OTHER),
    PASSENGER_BUS_INVOICE("304", "客运汽车发票", InvoiceTypeEnum.OTHER),
    QUOTA_INVOICE("305", "定额发票", InvoiceTypeEnum.OTHER),
    MACHINE_INVOICE("306", "机打发票", InvoiceTypeEnum.OTHER),
    TOLL_INVOICE("307", "过路费发票", InvoiceTypeEnum.OTHER),
    PASSENGER_SHIP_TICKET("308", "客运船票", InvoiceTypeEnum.OTHER),
    BLOCKCHAIN_ELECTRONIC_INVOICE("309", "区块链电子发票", InvoiceTypeEnum.OTHER),
    DIDI_ITINERARY("310", "滴滴行程单", InvoiceTypeEnum.OTHER),
    TRAIN_TICKET_REFUND("311", "火车票退票费", InvoiceTypeEnum.OTHER),
    MACHINE_ELECTRONIC_INVOICE("312", "机打电子发票", InvoiceTypeEnum.OTHER),
    DIGITAL_RAIL_TICKET("313", "数电票（铁路电子客票）", InvoiceTypeEnum.OTHER),
    DIGITAL_AIR_ITINERARY("314", "数电票（航空运输电子客票行程单）", InvoiceTypeEnum.OTHER),
    DIGITAL_RAIL_REFUND("315", "数电票（铁路电子客票）退改费", InvoiceTypeEnum.OTHER),
    DIGITAL_AIR_REFUND("316", "数电票（航空运输电子客票行程单）退改费", InvoiceTypeEnum.OTHER),
    AIR_TICKET_REFUND("317", "机票退改费", InvoiceTypeEnum.OTHER),
    MEDICAL_ELECTRONIC_OUTPATIENT("401", "医疗电子票据（门诊）", InvoiceTypeEnum.OTHER),
    MEDICAL_ELECTRONIC_HOSPITALIZED("402", "医疗电子票据（住院）", InvoiceTypeEnum.OTHER),
    TAX_PAYMENT_CERTIFICATE("500", "完税证明", InvoiceTypeEnum.OTHER),
    CUSTOMS_PAYMENT_DOCUMENT("600", "海关缴款书", InvoiceTypeEnum.OTHER),
    NON_INVOICE("998", "非发票", InvoiceTypeEnum.OTHER),
    OTHER_INVOICE("999", "其他发票", InvoiceTypeEnum.OTHER);

    private final String code;
    private final String name;
    private final InvoiceTypeEnum invoiceType;

    public static List<String> getNeedInvoiceValidateTypeCodes() {
        // 需要验真的所有类型
        // 01:增值税专用发票 03:机动车销售统一发票 04:增值税普通发票 08:增值税电子专用发票 10:增值税电子普通发票 11:增值税普通发票(卷式)
        // 14: 道路通行费电子普通发票 15:二手车销售统一发票 31:数电票（增值税专用发票） 32:数电票（普通发票）
        // 51: 数电票（铁路电子客票）61:数电票（航空运输电子客票行程单） 85 :数电纸质发票（增值税专用发票）86:数电纸质发票（普通发票）


        // 可以验真,但是单个OCR识别没有的类型
        // 31-数电票（增值税专用发票）- 单个接口无直接对应项
        // 32-数电票（普通发票）- 单个接口无直接对应项
        // 85-数电纸质发票（增值税专用发票）- 单个接口无直接对应项
        // 86-数电纸质发票（普通发票）- 单个接口无直接对应项
        return Arrays.asList(
                OcrSingleInvoiceTypeEnum.VAT_SPECIAL_INVOICE.getCode(),           // 100-增值税专用发票（对应批量01）
                OcrSingleInvoiceTypeEnum.MOTOR_VEHICLE_SALES_INVOICE.getCode(),   // 200-机动车销售统一发票（对应批量03）
                OcrSingleInvoiceTypeEnum.VAT_ORDINARY_INVOICE.getCode(),          // 101-增值税普通发票（对应批量04）
                OcrSingleInvoiceTypeEnum.ELECTRONIC_VAT_SPECIAL_INVOICE.getCode(),// 103-增值税电子专用发票（对应批量08）
                OcrSingleInvoiceTypeEnum.ELECTRONIC_VAT_ORDINARY_INVOICE.getCode(),//102-增值税电子普通发票（对应批量10）
                OcrSingleInvoiceTypeEnum.VAT_ORDINARY_ROLL_INVOICE.getCode(),     // 300-增值税普通发票卷式（对应批量11）
                OcrSingleInvoiceTypeEnum.TOLL_INVOICE.getCode(),                  // 307-过路费发票（对应批量14道路通行费）
                OcrSingleInvoiceTypeEnum.USED_CAR_SALES_INVOICE.getCode(),        // 201-二手车销售统一发票（对应批量15）
                OcrSingleInvoiceTypeEnum.DIGITAL_RAIL_TICKET.getCode(),           // 313-数电票铁路（对应批量51）
                OcrSingleInvoiceTypeEnum.DIGITAL_AIR_ITINERARY.getCode()          // 314-数电票航空（对应批量61）
        );
    }
}