package com.xhs.reimburse.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date :2025/07/08 - 下午5:25
 * @description :报销校验项目
 */
@Getter
@AllArgsConstructor
public enum ReimbursementFormCheckItemEnum {
    /**
     * 报销单维度
     */
    FORM_AUTH_CHECK("FORM_AUTH_CHECK", "用户身份校验", true),
    FORM_STATUS_CHECK("FORM_STATUS_CHECK", "单据状态校验", true),
    FORM_BASE_INFO_CHECK("FORM_BASE_INFO_CHECK", "单据基础信息校验", true),
    FORM_RELATE_EXPENSE_CHECK("FORM_RELATE_EXPENSE_CHECK", "单据关联费用校验", true),
    FORM_SINGLE_SUBMIT_CHECK("FORM_SINGLE_SUBMIT_CHECK", "单据单独提交校验", true),
    FORM_RELATE_TRAVEL_APPLY_CHECK("FORM_RELATE_TRAVEL_APPLY_CHECK", "单据行程关联校验", true);

    private final String code;
    private final String name;
    private final Boolean blockSubmit;
}
