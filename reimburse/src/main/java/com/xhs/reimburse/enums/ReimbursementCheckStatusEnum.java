package com.xhs.reimburse.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * [0，1]由REDflow控制
 *
 * <AUTHOR>
 * @date :2025/02/08 - 下午4:53
 * @description : 表单状态
 */
@AllArgsConstructor
@Getter
public enum ReimbursementCheckStatusEnum {
    NOT_PASS(0, "无效"),
    PASS(1, "有效");

    @EnumValue
    private Integer code;

    private String name;

    public static ReimbursementCheckStatusEnum valueOf(Integer code) {
        for (ReimbursementCheckStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("No enum constant with code " + code);
    }
}
