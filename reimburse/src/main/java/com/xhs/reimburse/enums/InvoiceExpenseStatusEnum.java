package com.xhs.reimburse.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName InvoiceExpenseStatusEnum.java
 * @createTime 2025年03月06日 15:24:00
 */
@AllArgsConstructor
@Getter
public enum InvoiceExpenseStatusEnum {
    WGL(1, "未关联"),
    YGL(2, "已关联");

    @EnumValue
    private final int code;
    private final String name;
}
