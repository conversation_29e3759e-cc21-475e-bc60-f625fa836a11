package com.xhs.reimburse.enums;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ExpenseSecondSubjectEnum.java
 * @createTime 2025年02月25日 14:33:00
 */
@Getter
@AllArgsConstructor
public enum ExpenseSecondSubjectEnum {


    /**
     * 一般费用报销单
     */
    EXPRESS_FEE("taxi_ticket", "出租车",false,  "如果因公外出须要乘坐出租车时，须完整填写《市内交通报销单据》，并填写外出原因、时间、上下车地点等信息，同时保留并上传完整、清晰的出租车发票。",
            Lists.newArrayList(InvoiceTypeEnum.TAXI_TICKET, InvoiceTypeEnum.INVOICE_TOLL, InvoiceTypeEnum.INVOICE_QUOTA)
    ),
    RIDE_SHARE("didi", "网约车", false, "如果因公外出须要乘坐网约车时，请优先使用企业滴滴进行打车，如企业滴滴无法使用，请使用其他正规网约车平台进行出行，须完整填写《市内交通报销单据》，打印发票，并附上《行程单》；原则上车型仅限快车、优享，若有特殊情况报销时需备注升级车型的理由、证明，如多人出行、商务车费用人均价格相对最低。",
            Lists.newArrayList(InvoiceTypeEnum.INVOICE_ORDINARY, InvoiceTypeEnum.INVOICE_MACHINE_ELECTRONIC)
    ),
    FUEL_FEE("fuel_parking", "油费", false, "① 如私车公用，发生油费/电车费，原则上不允许超过同样条件下快车的预计产生费用，若有特殊情况请备注说明。② 油费1.5元/公里，电费0.75元/公里，过路费/停车费等其他费用不允许报销。",
            Lists.newArrayList(InvoiceTypeEnum.INVOICE_TOLL, InvoiceTypeEnum.INVOICE_MACHINE, InvoiceTypeEnum.INVOICE_MACHINE_ELECTRONIC, InvoiceTypeEnum.INVOICE_ORDINARY_PAPER, InvoiceTypeEnum.INVOICE_ORDINARY)
    ),
    ELECTRIC_FEE("dcf", "电车费", false, "① 如私车公用，发生油费/电车费，原则上不允许超过同样条件下快车的预计产生费用，若有特殊情况请备注说明。② 油费1.5元/公里，电费0.75元/公里，过路费/停车费等其他费用不允许报销。",
            Lists.newArrayList(InvoiceTypeEnum.INVOICE_TOLL, InvoiceTypeEnum.INVOICE_MACHINE, InvoiceTypeEnum.INVOICE_MACHINE_ELECTRONIC, InvoiceTypeEnum.INVOICE_ORDINARY_PAPER, InvoiceTypeEnum.INVOICE_ORDINARY)
    ),
    OTHER("other", "其他", false, "如私车公用，发生油费/停车费/其他费用，原则上不允许超过同样条件下快车的预计产生费用，若有特殊情况请备注说明。", Lists.newArrayList(InvoiceTypeEnum.INVOICE_TOLL, InvoiceTypeEnum.INVOICE_MACHINE, InvoiceTypeEnum.INVOICE_MACHINE_ELECTRONIC, InvoiceTypeEnum.INVOICE_ORDINARY_PAPER, InvoiceTypeEnum.INVOICE_ORDINARY)
    ),

    // 20250515新增类目---start
    EXPRESS_RIDE_FEE("taxi_didi_ticket", "出租车/网约车",true,  "在工作地所在城市产生的出租车/网约车费用，区别于差旅交通费和团建交通费\n网约车：需在附件处上传行程单\n出租车：需在费用说明处注明上下车地点",
            Lists.newArrayList(InvoiceTypeEnum.TAXI_TICKET, InvoiceTypeEnum.INVOICE_TOLL, InvoiceTypeEnum.INVOICE_QUOTA,InvoiceTypeEnum.INVOICE_ORDINARY, InvoiceTypeEnum.INVOICE_MACHINE_ELECTRONIC)
    ),
    FUEL_ELECTRIC_OTHER_FEE("fuel_parking_dcf_other", "油费/停车费/其他", true, "在工作地所在城市产生的油费/停车费等。报销金额原则上不允许超过同样条件下快车的预计产生费用，若有特殊情况请备注说明。",
            Lists.newArrayList(InvoiceTypeEnum.INVOICE_TOLL, InvoiceTypeEnum.INVOICE_MACHINE, InvoiceTypeEnum.INVOICE_MACHINE_ELECTRONIC, InvoiceTypeEnum.INVOICE_ORDINARY_PAPER, InvoiceTypeEnum.INVOICE_ORDINARY)
    ),
    // 20250515新增类目---end




    KDYF("kdyf", "快递/运费", true, "需提供发票及运费明细，报销需说明邮寄事由、收件人信息等。",
            Lists.newArrayList(InvoiceTypeEnum.INVOICE_SPECIAL, InvoiceTypeEnum.INVOICE_ORDINARY,
                    InvoiceTypeEnum.INVOICE_SPECIAL_PAPER, InvoiceTypeEnum.INVOICE_ORDINARY_PAPER,
                    InvoiceTypeEnum.INVOICE_QUOTA, InvoiceTypeEnum.INVOICE_MACHINE, InvoiceTypeEnum.INVOICE_MACHINE_ELECTRONIC, InvoiceTypeEnum.OTHER)
    ),

    ZYSJ("zysj", "专业书籍", true, "需说明书籍名称，书籍实物保管人和存放地点。", Lists.newArrayList(InvoiceTypeEnum.INVOICE_SPECIAL, InvoiceTypeEnum.INVOICE_ORDINARY,
            InvoiceTypeEnum.INVOICE_SPECIAL_PAPER, InvoiceTypeEnum.INVOICE_ORDINARY_PAPER,
            InvoiceTypeEnum.INVOICE_MACHINE, InvoiceTypeEnum.INVOICE_MACHINE_ELECTRONIC, InvoiceTypeEnum.OTHER)
    ),

    PXJF("pxjf", "培训相关经费", true, "需说明培训名称并提供培训通知、现场照片、参与人等信息。",
            Lists.newArrayList(InvoiceTypeEnum.INVOICE_SPECIAL, InvoiceTypeEnum.INVOICE_ORDINARY,
                    InvoiceTypeEnum.INVOICE_SPECIAL_PAPER, InvoiceTypeEnum.INVOICE_ORDINARY_PAPER,
                    InvoiceTypeEnum.INVOICE_MACHINE, InvoiceTypeEnum.INVOICE_MACHINE_ELECTRONIC, InvoiceTypeEnum.OTHER)
    ),
    GZC("gzc", "工作餐", true, "特定情况下的加班、外勤、培训发生的餐费。请注意团队聚餐（团建费）、差旅餐费（差旅费）、商务宴请（招待费）等场景请走对应的报销单/科目。",
            Lists.newArrayList(InvoiceTypeEnum.INVOICE_SPECIAL, InvoiceTypeEnum.INVOICE_ORDINARY,
                    InvoiceTypeEnum.INVOICE_SPECIAL_PAPER, InvoiceTypeEnum.INVOICE_ORDINARY_PAPER,
                    InvoiceTypeEnum.INVOICE_QUOTA, InvoiceTypeEnum.INVOICE_MACHINE, InvoiceTypeEnum.INVOICE_MACHINE_ELECTRONIC, InvoiceTypeEnum.OTHER)
    ),

    TJF("tjf", "体检费", true, "员工体检产生的费用（仅对特定员工适用），其余新入职正式员工的体检由公司统一安排。",
            Lists.newArrayList(InvoiceTypeEnum.INVOICE_SPECIAL, InvoiceTypeEnum.INVOICE_ORDINARY,
                    InvoiceTypeEnum.INVOICE_SPECIAL_PAPER, InvoiceTypeEnum.INVOICE_ORDINARY_PAPER,
                    InvoiceTypeEnum.INVOICE_MACHINE, InvoiceTypeEnum.INVOICE_MACHINE_ELECTRONIC, InvoiceTypeEnum.OTHER)
    ),

    YWZDCY("cy", "业务招待-餐饮", true, "因工作关系所发生的商务宴请餐饮费。",
            Lists.newArrayList(InvoiceTypeEnum.INVOICE_SPECIAL, InvoiceTypeEnum.INVOICE_ORDINARY,
                    InvoiceTypeEnum.INVOICE_SPECIAL_PAPER, InvoiceTypeEnum.INVOICE_ORDINARY_PAPER,
                    InvoiceTypeEnum.INVOICE_QUOTA, InvoiceTypeEnum.INVOICE_MACHINE, InvoiceTypeEnum.INVOICE_MACHINE_ELECTRONIC,
                    InvoiceTypeEnum.AIR_TICKET, InvoiceTypeEnum.TAXI_TICKET, InvoiceTypeEnum.TRAIN_TICKET,
                    InvoiceTypeEnum.BUS_TICKET, InvoiceTypeEnum.OTHER, InvoiceTypeEnum.INVOICE_TOLL)
    ),

    YWZDQT("qt", "业务招待-其他", true, "因工作原因发生的礼品或其他形式的业务招待费用。",
            Lists.newArrayList(InvoiceTypeEnum.INVOICE_SPECIAL, InvoiceTypeEnum.INVOICE_ORDINARY,
                    InvoiceTypeEnum.INVOICE_SPECIAL_PAPER, InvoiceTypeEnum.INVOICE_ORDINARY_PAPER,
                    InvoiceTypeEnum.INVOICE_QUOTA, InvoiceTypeEnum.INVOICE_MACHINE,
                    InvoiceTypeEnum.AIR_TICKET, InvoiceTypeEnum.TAXI_TICKET, InvoiceTypeEnum.TRAIN_TICKET,
                    InvoiceTypeEnum.BUS_TICKET, InvoiceTypeEnum.OTHER, InvoiceTypeEnum.INVOICE_MACHINE_ELECTRONIC, InvoiceTypeEnum.INVOICE_TOLL, InvoiceTypeEnum.ELECTRONIC_TRAIN_TICKET
            )
    ),

    CDZLF("cdzlf", "场地租赁费", true, "会议活动场地租赁费，请优先通过采购流程执行。报销时需提供会议名称、预算申请记录、会议照片等会议信息。",
            Lists.newArrayList(InvoiceTypeEnum.INVOICE_SPECIAL, InvoiceTypeEnum.INVOICE_ORDINARY,
                    InvoiceTypeEnum.INVOICE_SPECIAL_PAPER, InvoiceTypeEnum.INVOICE_ORDINARY_PAPER,
                    InvoiceTypeEnum.INVOICE_MACHINE, InvoiceTypeEnum.INVOICE_MACHINE_ELECTRONIC, InvoiceTypeEnum.OTHER)
    ),

    BZF("bzf", "布置费", true, "会议场地布置产生的费用，请优先通过采购流程执行。报销时需提供会议名称、预算申请记录、会议照片等会议信息。",
            Lists.newArrayList(InvoiceTypeEnum.INVOICE_SPECIAL, InvoiceTypeEnum.INVOICE_ORDINARY,
                    InvoiceTypeEnum.INVOICE_SPECIAL_PAPER, InvoiceTypeEnum.INVOICE_ORDINARY_PAPER,
                    InvoiceTypeEnum.INVOICE_MACHINE, InvoiceTypeEnum.INVOICE_MACHINE_ELECTRONIC, InvoiceTypeEnum.OTHER)
    ),

    HYYP("hyyp", "会议用品", true, "各类会议用品、物料、奖品等，若出现大额物料购置，请优先通过采购流程执行。",
            Lists.newArrayList(InvoiceTypeEnum.INVOICE_SPECIAL, InvoiceTypeEnum.INVOICE_ORDINARY,
                    InvoiceTypeEnum.INVOICE_SPECIAL_PAPER, InvoiceTypeEnum.INVOICE_ORDINARY_PAPER,
                    InvoiceTypeEnum.INVOICE_MACHINE, InvoiceTypeEnum.INVOICE_MACHINE_ELECTRONIC, InvoiceTypeEnum.OTHER)
    ),

    HYJTF("hyjtf", "会议市内交通费", true, "会议活动相关的包车费用，请优先通过采购流程执行。报销时需提供会议名称、预算申请记录、会议照片等会议信息。",
            Lists.newArrayList(InvoiceTypeEnum.INVOICE_SPECIAL, InvoiceTypeEnum.INVOICE_ORDINARY,
                    InvoiceTypeEnum.INVOICE_SPECIAL_PAPER, InvoiceTypeEnum.INVOICE_ORDINARY_PAPER,
                    InvoiceTypeEnum.INVOICE_QUOTA, InvoiceTypeEnum.INVOICE_MACHINE, InvoiceTypeEnum.TAXI_TICKET,
                    InvoiceTypeEnum.TRAIN_TICKET, InvoiceTypeEnum.BUS_TICKET, InvoiceTypeEnum.INVOICE_MACHINE_ELECTRONIC, InvoiceTypeEnum.OTHER,
                    InvoiceTypeEnum.INVOICE_TOLL)
    ),

    ZZZZ("zzzz", "证照/资质/公证相关费用", true, "因工作需要开具证照/资质/公证相关产生的费用", getAllInvoiceTypeEnum()),

    FLXG("flxg", "案件/法律相关专业服务", true, "因工作需要进行案件咨询/法律相关服务咨询产生的费用，请优先通过采购流程执行",
            Lists.newArrayList(InvoiceTypeEnum.INVOICE_SPECIAL, InvoiceTypeEnum.INVOICE_ORDINARY,
                    InvoiceTypeEnum.INVOICE_SPECIAL_PAPER, InvoiceTypeEnum.INVOICE_ORDINARY_PAPER,
                    InvoiceTypeEnum.INVOICE_MACHINE, InvoiceTypeEnum.INVOICE_MACHINE_ELECTRONIC, InvoiceTypeEnum.OTHER)
    ),

    XZFK("xzfk", "行政罚款", true, "行政罚款产生的费用，请优先通过小红花平台对公付款", getAllInvoiceTypeEnum()),

    BGHCF("bghcf", "办公耗材费", true, "需注明需求背景",
            Lists.newArrayList(InvoiceTypeEnum.INVOICE_SPECIAL, InvoiceTypeEnum.INVOICE_ORDINARY,
                    InvoiceTypeEnum.INVOICE_SPECIAL_PAPER, InvoiceTypeEnum.INVOICE_ORDINARY_PAPER,
                    InvoiceTypeEnum.INVOICE_MACHINE, InvoiceTypeEnum.INVOICE_MACHINE_ELECTRONIC, InvoiceTypeEnum.OTHER)
    ),

    ITDZCP("itdzcp", "IT电子产品", true, "需说明需求背景，大于等于2000元的IT资产在到货后联系IIT入库后方可提交报销",
            Lists.newArrayList(InvoiceTypeEnum.INVOICE_SPECIAL, InvoiceTypeEnum.INVOICE_ORDINARY,
                    InvoiceTypeEnum.INVOICE_SPECIAL_PAPER, InvoiceTypeEnum.INVOICE_ORDINARY_PAPER,
                    InvoiceTypeEnum.INVOICE_MACHINE, InvoiceTypeEnum.INVOICE_MACHINE_ELECTRONIC, InvoiceTypeEnum.OTHER)
    ),

    SBWXF("sbwxf", "设备维修费", true, "仅适用于除IT资产外的设备维修",
            Lists.newArrayList(InvoiceTypeEnum.INVOICE_SPECIAL, InvoiceTypeEnum.INVOICE_ORDINARY,
                    InvoiceTypeEnum.INVOICE_SPECIAL_PAPER, InvoiceTypeEnum.INVOICE_ORDINARY_PAPER,
                    InvoiceTypeEnum.INVOICE_MACHINE, InvoiceTypeEnum.INVOICE_MACHINE_ELECTRONIC, InvoiceTypeEnum.OTHER)
    ),

    SPYF("spyf", "商品运费", false, "",
            Lists.newArrayList(InvoiceTypeEnum.INVOICE_SPECIAL, InvoiceTypeEnum.INVOICE_ORDINARY,
                    InvoiceTypeEnum.INVOICE_SPECIAL_PAPER, InvoiceTypeEnum.INVOICE_ORDINARY_PAPER,
                    InvoiceTypeEnum.INVOICE_QUOTA, InvoiceTypeEnum.INVOICE_MACHINE, InvoiceTypeEnum.INVOICE_MACHINE_ELECTRONIC, InvoiceTypeEnum.OTHER)
    ),

    YPCGF("ypcgf", "样品采购费", true, "零星样品购买",
            Lists.newArrayList(InvoiceTypeEnum.INVOICE_SPECIAL, InvoiceTypeEnum.INVOICE_ORDINARY,
                    InvoiceTypeEnum.INVOICE_SPECIAL_PAPER, InvoiceTypeEnum.INVOICE_ORDINARY_PAPER,
                    InvoiceTypeEnum.INVOICE_MACHINE, InvoiceTypeEnum.INVOICE_MACHINE_ELECTRONIC, InvoiceTypeEnum.OTHER)
    ),

    RYJLJF("ryjljf", "人员奖励经费", true, "HR专用人员奖励经费，需说明奖励事由、被奖励人姓名、岗位、奖励明细等信息",
            Lists.newArrayList(InvoiceTypeEnum.INVOICE_SPECIAL, InvoiceTypeEnum.INVOICE_ORDINARY,
                    InvoiceTypeEnum.INVOICE_SPECIAL_PAPER, InvoiceTypeEnum.INVOICE_ORDINARY_PAPER,
                    InvoiceTypeEnum.INVOICE_MACHINE, InvoiceTypeEnum.INVOICE_MACHINE_ELECTRONIC, InvoiceTypeEnum.OTHER)
    ),

    JDZPRY("jdzpry", "接待招聘相关人员", true, "HR专用接待招聘相关人员费用，需说明招聘接待详情", Collections.emptyList()),

    WXF("wxf", "维修费", true, "行政维修费，需提供发票、附上维修/装修记录。优先通过采购流程执行。",
            Lists.newArrayList(InvoiceTypeEnum.INVOICE_SPECIAL, InvoiceTypeEnum.INVOICE_ORDINARY,
                    InvoiceTypeEnum.INVOICE_SPECIAL_PAPER, InvoiceTypeEnum.INVOICE_ORDINARY_PAPER,
                    InvoiceTypeEnum.INVOICE_MACHINE, InvoiceTypeEnum.INVOICE_MACHINE_ELECTRONIC, InvoiceTypeEnum.OTHER)
    ),

    ZSJJ("zsjj", "装饰和家具", true, "办公装饰和家具，需提供附上装饰和家具价格明细。优先通过采购流程执行。",
            Lists.newArrayList(InvoiceTypeEnum.INVOICE_SPECIAL, InvoiceTypeEnum.INVOICE_ORDINARY,
                    InvoiceTypeEnum.INVOICE_SPECIAL_PAPER, InvoiceTypeEnum.INVOICE_ORDINARY_PAPER,
                    InvoiceTypeEnum.INVOICE_MACHINE, InvoiceTypeEnum.INVOICE_MACHINE_ELECTRONIC, InvoiceTypeEnum.OTHER)
    ),

    BGWJ("bgwj", "办公文具", true, "购买办公用品产生的费用，需提供发票和明细。优先通过京东慧财、科力普等渠道采购。",
            Lists.newArrayList(InvoiceTypeEnum.INVOICE_SPECIAL, InvoiceTypeEnum.INVOICE_ORDINARY,
                    InvoiceTypeEnum.INVOICE_SPECIAL_PAPER, InvoiceTypeEnum.INVOICE_ORDINARY_PAPER,
                    InvoiceTypeEnum.INVOICE_MACHINE, InvoiceTypeEnum.INVOICE_MACHINE_ELECTRONIC, InvoiceTypeEnum.OTHER)
    ),

    GWCZF("gwczf", "公务车杂费", true, "公司公务车产生的能耗费、保养费、停车费等，需提供发票和明细，并备注出行事由。优先通过采购和小红花平台进行对公付款。",
            Lists.newArrayList(InvoiceTypeEnum.INVOICE_SPECIAL, InvoiceTypeEnum.INVOICE_ORDINARY,
                    InvoiceTypeEnum.INVOICE_SPECIAL_PAPER, InvoiceTypeEnum.INVOICE_ORDINARY_PAPER,
                    InvoiceTypeEnum.INVOICE_QUOTA, InvoiceTypeEnum.INVOICE_MACHINE, InvoiceTypeEnum.INVOICE_MACHINE_ELECTRONIC, InvoiceTypeEnum.OTHER)
    ),

    RYP("ryp", "药品/食品/日用品", true, "因工作需要产生的药品/食品/日用品等购买费用，需提供发票和明细。优先通过采购和小红花平台进行对公付款。",
            Lists.newArrayList(InvoiceTypeEnum.INVOICE_SPECIAL, InvoiceTypeEnum.INVOICE_ORDINARY,
                    InvoiceTypeEnum.INVOICE_SPECIAL_PAPER, InvoiceTypeEnum.INVOICE_ORDINARY_PAPER,
                    InvoiceTypeEnum.INVOICE_MACHINE, InvoiceTypeEnum.INVOICE_MACHINE_ELECTRONIC, InvoiceTypeEnum.OTHER)
    ),


    QYHD("qyhd", "全员活动商品和服务", true, "员工奖励相关的商品和服务，需提供发票和明细。大批量的商品服务采购请优先通过采购流程执行",
            Lists.newArrayList(InvoiceTypeEnum.INVOICE_SPECIAL, InvoiceTypeEnum.INVOICE_ORDINARY,
                    InvoiceTypeEnum.INVOICE_SPECIAL_PAPER, InvoiceTypeEnum.INVOICE_ORDINARY_PAPER,
                    InvoiceTypeEnum.INVOICE_QUOTA, InvoiceTypeEnum.INVOICE_MACHINE, InvoiceTypeEnum.INVOICE_MACHINE_ELECTRONIC, InvoiceTypeEnum.OTHER)
    ),

    STHD("sthd", "社团活动", true, "因举办社团活动产生的费用，需提供发票、明细和活动证明附件。优先通过采购和小红花平台进行对公付款。",
            Lists.newArrayList(InvoiceTypeEnum.INVOICE_SPECIAL, InvoiceTypeEnum.INVOICE_ORDINARY,
                    InvoiceTypeEnum.INVOICE_SPECIAL_PAPER, InvoiceTypeEnum.INVOICE_ORDINARY_PAPER,
                    InvoiceTypeEnum.INVOICE_MACHINE, InvoiceTypeEnum.INVOICE_MACHINE_ELECTRONIC, InvoiceTypeEnum.OTHER)
    ),

    JSKF("jskf", "技术开发和使用费", true, "因工作产生的技术服务充值和使用费，需提供发票和明细。优先通过采购和小红花平台进行对公付款。",
            Lists.newArrayList(InvoiceTypeEnum.INVOICE_SPECIAL, InvoiceTypeEnum.INVOICE_ORDINARY,
                    InvoiceTypeEnum.INVOICE_SPECIAL_PAPER, InvoiceTypeEnum.INVOICE_ORDINARY_PAPER,
                    InvoiceTypeEnum.INVOICE_MACHINE, InvoiceTypeEnum.INVOICE_MACHINE_ELECTRONIC, InvoiceTypeEnum.OTHER)
    ),

    CGHZ("cghz", "参观会展门票（非招待）", true, "因工作产生的会展门票等，需提供发票和门票购买记录。优先通过小红花平台进行对公付款。",
            Lists.newArrayList(InvoiceTypeEnum.INVOICE_SPECIAL, InvoiceTypeEnum.INVOICE_ORDINARY,
                    InvoiceTypeEnum.INVOICE_SPECIAL_PAPER, InvoiceTypeEnum.INVOICE_ORDINARY_PAPER,
                    InvoiceTypeEnum.INVOICE_MACHINE, InvoiceTypeEnum.INVOICE_MACHINE_ELECTRONIC, InvoiceTypeEnum.OTHER)
    ),

    WBFWF("wbfwf", "外部服务费", true, "因公产生的各类小额代理服务费",
            Lists.newArrayList(InvoiceTypeEnum.INVOICE_SPECIAL, InvoiceTypeEnum.INVOICE_ORDINARY,
                    InvoiceTypeEnum.INVOICE_SPECIAL_PAPER, InvoiceTypeEnum.INVOICE_ORDINARY_PAPER,
                    InvoiceTypeEnum.INVOICE_MACHINE, InvoiceTypeEnum.INVOICE_MACHINE_ELECTRONIC, InvoiceTypeEnum.OTHER)
    ),

    QTSX("qtsx", "其他", true, "仅可报销通过特定审批通过后的特殊事项。需提供发票、明细，和直属上级和财分BP的特批记录。", getAllInvoiceTypeEnum()),

    /**
     * bot
     */
    AI_COPILOT("ai_copilot", "领航薯报销", true, "", getAllInvoiceTypeEnum()),


    /**
     * 差旅
     */
    BSGS("bsgs", "北上广深", true, "若存在不同目的地的住宿，请分多笔费用报销", Lists.newArrayList(InvoiceTypeEnum.INVOICE_SPECIAL, InvoiceTypeEnum.INVOICE_ORDINARY,
            InvoiceTypeEnum.INVOICE_SPECIAL_PAPER, InvoiceTypeEnum.INVOICE_ORDINARY_PAPER,
            InvoiceTypeEnum.INVOICE_MACHINE, InvoiceTypeEnum.INVOICE_MACHINE_ELECTRONIC)
    ),

    GNQTCS("gnqtcs", "国内其他城市（港澳台除外）", true, "若存在不同目的地的住宿，请分多笔费用报销", Lists.newArrayList(InvoiceTypeEnum.INVOICE_SPECIAL, InvoiceTypeEnum.INVOICE_ORDINARY,
            InvoiceTypeEnum.INVOICE_SPECIAL_PAPER, InvoiceTypeEnum.INVOICE_ORDINARY_PAPER,
            InvoiceTypeEnum.INVOICE_MACHINE, InvoiceTypeEnum.INVOICE_MACHINE_ELECTRONIC)
    ),

    YZCS("yzcs", "亚洲国家及港澳台地区", true, "若存在不同目的地的住宿，请分多笔费用报销", Lists.newArrayList(InvoiceTypeEnum.INVOICE_SPECIAL, InvoiceTypeEnum.INVOICE_ORDINARY,
            InvoiceTypeEnum.INVOICE_SPECIAL_PAPER, InvoiceTypeEnum.INVOICE_ORDINARY_PAPER,
            InvoiceTypeEnum.INVOICE_MACHINE, InvoiceTypeEnum.INVOICE_MACHINE_ELECTRONIC, InvoiceTypeEnum.OTHER)
    ),

    YZYWCS("yzywcs", "亚洲以外其他国家", true, "若存在不同目的地的住宿，请分多笔费用报销", Lists.newArrayList(InvoiceTypeEnum.INVOICE_SPECIAL, InvoiceTypeEnum.INVOICE_ORDINARY,
            InvoiceTypeEnum.INVOICE_SPECIAL_PAPER, InvoiceTypeEnum.INVOICE_ORDINARY_PAPER,
            InvoiceTypeEnum.INVOICE_MACHINE, InvoiceTypeEnum.INVOICE_MACHINE_ELECTRONIC, InvoiceTypeEnum.OTHER)
    ),

    JNGZC("jngzc", "境内工作餐", true, "出差地为北京/上海由于职场供餐不享受补贴。非工作日/职场无供餐情况下，可申请报销（150元/天，O族&实习生100元/天）", Lists.newArrayList(InvoiceTypeEnum.INVOICE_SPECIAL, InvoiceTypeEnum.INVOICE_ORDINARY,
            InvoiceTypeEnum.INVOICE_SPECIAL_PAPER, InvoiceTypeEnum.INVOICE_ORDINARY_PAPER,
            InvoiceTypeEnum.INVOICE_QUOTA, InvoiceTypeEnum.INVOICE_MACHINE_ELECTRONIC, InvoiceTypeEnum.INVOICE_MACHINE)
    ),

    JWGZC("jwgzc", "境外工作餐", true, "可凭invoice/receipt报销", Lists.newArrayList(InvoiceTypeEnum.INVOICE_SPECIAL, InvoiceTypeEnum.INVOICE_ORDINARY,
            InvoiceTypeEnum.INVOICE_SPECIAL_PAPER, InvoiceTypeEnum.INVOICE_ORDINARY_PAPER,
            InvoiceTypeEnum.INVOICE_QUOTA, InvoiceTypeEnum.INVOICE_MACHINE, InvoiceTypeEnum.INVOICE_MACHINE_ELECTRONIC, InvoiceTypeEnum.OTHER)
    ),

    YF("yf", "油费", true, "1、如私车公用，发生油费，原则上不允许超过同样条件下快车的预计产生费用，若有特殊情况请备注说明。 2、油费1.5元/公里，过路费/停车费等其他费用不允许报销。", Lists.newArrayList(InvoiceTypeEnum.INVOICE_SPECIAL, InvoiceTypeEnum.INVOICE_ORDINARY,
            InvoiceTypeEnum.INVOICE_SPECIAL_PAPER, InvoiceTypeEnum.INVOICE_ORDINARY_PAPER,
            InvoiceTypeEnum.INVOICE_MACHINE, InvoiceTypeEnum.INVOICE_MACHINE_ELECTRONIC)
    ),

    DF("df", "电费", true, "1、如私车公用，发生电车费，原则上不允许超过同样条件下快车的预计产生费用，若有特殊情况请备注说明。2、电费0.75元/公里，过路费/停车费等其他费用不允许报销。", Lists.newArrayList(InvoiceTypeEnum.INVOICE_SPECIAL, InvoiceTypeEnum.INVOICE_ORDINARY,
            InvoiceTypeEnum.INVOICE_SPECIAL_PAPER, InvoiceTypeEnum.INVOICE_ORDINARY_PAPER,
            InvoiceTypeEnum.INVOICE_MACHINE, InvoiceTypeEnum.INVOICE_MACHINE_ELECTRONIC)
    ),
    ;

    // 二级科目code
    private String subjectCode;
    // 二级科目名称
    private String subjectName;
    // 是否启动true启用false禁用
    private boolean enableFlag;
    // 二级科目描述
    private String description;
    private List<InvoiceTypeEnum> allowedInvoiceTypeEnums;

    public static List<String> getAllowedLabelValues(String subjectCode) {
        for (ExpenseSecondSubjectEnum subjectEnum : ExpenseSecondSubjectEnum.values()) {
            if (subjectEnum.getSubjectCode().equals(subjectCode)) {
                List<String> list = new ArrayList<>();
                List<InvoiceTypeEnum> allowedList = subjectEnum.getAllowedInvoiceTypeEnums();
                for (InvoiceTypeEnum invoiceTypeEnum : allowedList) {
                    list.add(invoiceTypeEnum.getCode());
                }
                return list;
            }
        }
        return Collections.emptyList();
    }

    public static String getSubjectName(String subjectCode) {
        if (StringUtils.isEmpty(subjectCode)) {
            return "";
        }
        for (ExpenseSecondSubjectEnum subjectEnum : ExpenseSecondSubjectEnum.values()) {
            if (subjectEnum.getSubjectCode().equals(subjectCode)) {
                return subjectEnum.getSubjectName();
            }
        }
        return "";
    }

    public static List<InvoiceTypeEnum> getAllInvoiceTypeEnum() {
        return Lists.newArrayList(
                InvoiceTypeEnum.INVOICE_SPECIAL,
                InvoiceTypeEnum.INVOICE_ORDINARY,
                InvoiceTypeEnum.INVOICE_MACHINE_ELECTRONIC,
                InvoiceTypeEnum.INVOICE_SPECIAL_PAPER,
                InvoiceTypeEnum.INVOICE_ORDINARY_PAPER,
                InvoiceTypeEnum.INVOICE_QUOTA,
                InvoiceTypeEnum.INVOICE_MACHINE,
                InvoiceTypeEnum.TAXI_TICKET,
                InvoiceTypeEnum.TRAIN_TICKET,
                InvoiceTypeEnum.AIR_TICKET,
                InvoiceTypeEnum.BUS_TICKET,
                InvoiceTypeEnum.INVOICE_TOLL,
                InvoiceTypeEnum.AIR_TICKET_ELECTRONIC,
                InvoiceTypeEnum.OTHER
        );
    }

    /**
     * 属于境外的科目，不进行交票校验
     */
    public static List<String> overseasSecondSubject() {
        return Arrays.asList(ExpenseSecondSubjectEnum.YZCS.getSubjectCode(), ExpenseSecondSubjectEnum.YZYWCS.getSubjectCode()
                , ExpenseSecondSubjectEnum.JWGZC.getSubjectCode());
    }
}
