package com.xhs.reimburse.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName RecordStatusEnum.java
 * @createTime 2025年02月14日 15:43:00
 */
@AllArgsConstructor
@Getter
public enum RecordStatusEnum {
    INVALID(0, "无效"),
    VALID(1, "有效");

    @EnumValue
    private Integer code;

    private String name;

    public static RecordStatusEnum valueOf(Integer code) {
        for (RecordStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("No enum constant with code " + code);
    }
}
