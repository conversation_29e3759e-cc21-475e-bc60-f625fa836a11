package com.xhs.reimburse.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * [1, 2, 3] 在C端内部扭转，费用关联发票时1->2
 * [4, 5] 由REDflow的MQ控制
 *
 * <AUTHOR>
 * @date :2025/02/08 - 下午4:50
 *
 * @description : 发票状态
 */
@AllArgsConstructor
@Getter
public enum InvoiceStatusEnum {
    DBC(1, "待补充"),
    YBC(2, "已补充");

    @EnumValue
    private final int code;
    private final String name;
}
