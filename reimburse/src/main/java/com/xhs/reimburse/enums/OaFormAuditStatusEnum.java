package com.xhs.reimburse.enums;

import com.xhs.finance.exception.BusinessException;
import lombok.Getter;

import java.util.*;

/**
 * <AUTHOR>
 * @desc xhsoa中单据的审批状态
 */
@Getter
public enum OaFormAuditStatusEnum {
    DRAFT(0, "草稿"),
    IN_REVIEW(1, "审批中"),
    AUDIT_PASS(2, "审核通过"),
    /**
     * 审核拒绝状态暂时没用
     */
    AUDIT_REFUSE(3, "审核拒绝"),
    AUDIT_TERMINATE(4, "已终止"),
    WITHDRAWAL(5, "撤回待发起"),
    DELETE(6, "已删除");

    private Integer code;

    private String name;

    OaFormAuditStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static List<Integer> getValidStatusCode() {
        List<Integer> result = new ArrayList<>();
        result.add(OaFormAuditStatusEnum.IN_REVIEW.getCode());
        result.add(OaFormAuditStatusEnum.AUDIT_PASS.getCode());
        return result;
    }
}
