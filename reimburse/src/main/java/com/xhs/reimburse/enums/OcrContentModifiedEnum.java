package com.xhs.reimburse.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName OcrContentModifiedEnum.java
 * @createTime 2025年02月18日 20:11:00
 */
@AllArgsConstructor
@Getter
public enum OcrContentModifiedEnum {
    NOT_MODIFIED(1, "未修改"),
    MODIFIED(2, "已修改");

    @EnumValue
    private Integer code;

    private String name;

}
