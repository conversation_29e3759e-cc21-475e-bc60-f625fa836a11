package com.xhs.reimburse.enums;

import com.xhs.reimburse.modal.dto.ExpenseDto;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date :2025/07/09 - 下午4:32
 * @description : 费用阻碍项
 */
@Getter
@AllArgsConstructor
public enum ExpenseIntegrityFieldEnum {
    /**
     * @see ExpenseDto
     */
    FORM_TYPE("formType", "单据类型"),
    FIRST_SUBJECT("firstSubject", "一级科目"),
    TRIP_ROUTE_CITY("tripRouteCity", "行程"),
    BEYOND_STANDARD_INFO("beyondStandardInfo", "业务招待费-超标说明"),
    TRAVEL_MEAL_OVER_LIMIT_REASON("travelMealOverLimitReason", "差旅工作餐-超标原因");

    private String fieldCode;
    private String fieldName;
}