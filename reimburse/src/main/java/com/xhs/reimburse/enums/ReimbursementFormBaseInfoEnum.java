package com.xhs.reimburse.enums;

import com.xhs.reimburse.modal.dto.ReimburseBasicInfo;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date :2025/07/09 - 下午4:20
 * @description :
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum ReimbursementFormBaseInfoEnum {
    /**
     * @see ReimburseBasicInfo
     */
    PAYMENT_COMPANY_NAME("paymentCompanyName", "付款公司名称"),
    GATHERING_BANK("gatheringBank", "收款银行名称"),
    GATHERING_BANK_CODE("gatheringBankCode", "收款银行联行号"),
    GATHERING_ACCOUNT("gatheringAccount", "收款账号"),
    GATHERING_NAME("gatheringName", "收款账户名称"),
    DEPARTMENT_ID("departmentId", "当前用户所在部门ID"),
    DEPARTMENT_ID_PATH("departmentIdPath", "预算归属部门ID路径"),
    BUDGET_DEPARTMENT_ID("budgetDepartmentId", "预算归属部门ID"),
    BUDGET_DEPARTMENT_NAME_PATH("budgetDepartmentNamePath", "预算归属部门名称路径");

    private String fieldCode;
    private String fieldName;
}
