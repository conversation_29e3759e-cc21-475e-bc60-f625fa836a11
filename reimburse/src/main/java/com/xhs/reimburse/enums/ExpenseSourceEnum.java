package com.xhs.reimburse.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ExpenseSourceEnum.java
 * @createTime 2025年02月14日 15:46:00
 */
@AllArgsConstructor
@Getter
public enum ExpenseSourceEnum {
    EMAIL(1, "邮箱"),
    BOT(2, "BOT"),
    BUSINESS(3, "业务");

    @EnumValue
    private final Integer code;
    private final String name;
}
