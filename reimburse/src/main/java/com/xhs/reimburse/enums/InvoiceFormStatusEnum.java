package com.xhs.reimburse.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName InvoiceFormStatusEnum.java
 * @createTime 2025年03月06日 15:28:00
 */
@AllArgsConstructor
@Getter
public enum InvoiceFormStatusEnum {
    WGL(1, "未关联"),
    YGL(2, "已关联"),
    SHZ(3, "审核中"),
    CH(4, "撤回"),
    BH(5, "驳回"),
    YBX(6, "已报销");

    @EnumValue
    private final int code;
    private final String name;
}
