package com.xhs.reimburse.enums;

import com.xhs.reimburse.modal.response.LabelValueResponse;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TrainSeatTypeEnum.java
 * @createTime 2025年03月10日 23:54:00
 */
@Getter
@AllArgsConstructor
public enum TrainSeatTypeEnum {
    SECOND_CLASS("二等座"),
    FIRST_CLASS("一等座"),
    BUSINESS_CLASS("商务座"),
    NEW_AIR_CONDITIONED_HARD_SEAT("新空调硬座"),
    HARD_SLEEPER("硬卧"),
    SOFT_SLEEPER("软卧"),
    NO_SEAT("无座");

    private final String name;

    public static List<LabelValueResponse> getTrainSeatTypeList() {
        return Arrays.stream(TrainSeatTypeEnum.values())
                .map(seatType -> {
                    LabelValueResponse response = new LabelValueResponse();
                    response.setValue(seatType.getName());
                    response.setLabel(seatType.getName());
                    return response;
                }).collect(Collectors.toList());
    }
}
