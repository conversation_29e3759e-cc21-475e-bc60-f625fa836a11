package com.xhs.reimburse.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName OcrBatchInvoiceTypeEnum.java
 * @createTime 2025年02月26日 22:44:00
 */
@Getter
@AllArgsConstructor
public enum OcrBatchInvoiceTypeEnum {

    OTHER_INVOICE("99", "其他发票", InvoiceTypeEnum.OTHER),
    VAT_SPECIAL_INVOICE("01", "增值税专用发票", InvoiceTypeEnum.INVOICE_SPECIAL_PAPER),
    VAT_ORDINARY_INVOICE("04", "增值税普通发票", InvoiceTypeEnum.INVOICE_ORDINARY_PAPER),
    MOTOR_VEHICLE_SALES_INVOICE("03", "机动车销售统一发票", InvoiceTypeEnum.OTHER),
    ELECTRONIC_VAT_SPECIAL_INVOICE("08", "增值税电子专用发票", InvoiceTypeEnum.INVOICE_SPECIAL),
    ELECTRONIC_VAT_ORDINARY_INVOICE("10", "增值税电子普通发票", InvoiceTypeEnum.INVOICE_ORDINARY),
    VAT_ORDINARY_ROLL_INVOICE("11", "增值税普通发票(卷式)", InvoiceTypeEnum.INVOICE_ORDINARY_PAPER),
    ELECTRONIC_TOLL_INVOICE("14", "增值税电子普通发票(通行费)", InvoiceTypeEnum.INVOICE_TOLL),
    USED_CAR_SALES_INVOICE("15", "二手车销售统一发票", InvoiceTypeEnum.OTHER),
    DIGITAL_VAT_SPECIAL("31", "数电发票（增值税专用发票）", InvoiceTypeEnum.INVOICE_SPECIAL),
    DIGITAL_VAT_ORDINARY("32", "数电发票（增值税普通发票）", InvoiceTypeEnum.INVOICE_ORDINARY),
    TRAIN_TICKET("1002", "火车票", InvoiceTypeEnum.TRAIN_TICKET),
    AIR_ITINERARY("1003", "航空电子客票行程单", InvoiceTypeEnum.AIR_TICKET),
    TAXI_INVOICE("1004", "出租车发票", InvoiceTypeEnum.TAXI_TICKET),
    QUOTA_INVOICE("1005", "通用定额发票", InvoiceTypeEnum.INVOICE_QUOTA),
    PASSENGER_TRANSPORT_INVOICE("1006", "公路水路客运发票", InvoiceTypeEnum.BUS_TICKET),
    MACHINE_INVOICE("1007", "通用机打发票", InvoiceTypeEnum.INVOICE_MACHINE),
    TOLL_INVOICE("1008", "过路费发票", InvoiceTypeEnum.OTHER),
    BLOCKCHAIN_ELECTRONIC_INVOICE("1009", "区块链电子发票", InvoiceTypeEnum.OTHER),
    TRAIN_TICKET_REFUND("1010", "火车票退票费", InvoiceTypeEnum.OTHER),
    MEDICAL_HOSPITALIZED("1011", "医疗电子票据（住院）", InvoiceTypeEnum.OTHER),
    MEDICAL_OUTPATIENT("1012", "医疗电子票据（门诊）", InvoiceTypeEnum.OTHER),
    GENERAL_ELECTRONIC_INVOICE("1013", "通用（电子）发票", InvoiceTypeEnum.INVOICE_MACHINE_ELECTRONIC),
    DIGITAL_RAIL_TICKET("51", "数电发票（铁路电子客票）", InvoiceTypeEnum.ELECTRONIC_TRAIN_TICKET),
    DIGITAL_AIR_ITINERARY("61", "数电发票（航空运输电子客票单）", InvoiceTypeEnum.AIR_TICKET_ELECTRONIC),
    CUSTOMS_PAYMENT("1015", "海关缴款书", InvoiceTypeEnum.OTHER),
    TAX_PAYMENT_CERTIFICATE("1014", "完税证明", InvoiceTypeEnum.OTHER),
    SALES_UNIFIED_ELECTRONIC_INVOICE("83", "电子发票（机动车销售统一发票）", InvoiceTypeEnum.OTHER),
    DIGITAL_PAPER_VAT_SPECIAL("85", "数电纸质发票（增值税专用发票）", InvoiceTypeEnum.INVOICE_SPECIAL_PAPER),
    DIGITAL_PAPER_VAT_ORDINARY("86", "数电纸质发票（普通发票）", InvoiceTypeEnum.INVOICE_ORDINARY_PAPER);

    private final String code;
    private final String name;
    private final InvoiceTypeEnum invoiceType;

    public static List<String> getNeedInvoiceValidateTypeCodes() {
        return Arrays.asList(
                OcrBatchInvoiceTypeEnum.VAT_SPECIAL_INVOICE.getCode(),            // 01-增值税专用发票
                OcrBatchInvoiceTypeEnum.MOTOR_VEHICLE_SALES_INVOICE.getCode(),    // 03-机动车销售统一发票
                OcrBatchInvoiceTypeEnum.VAT_ORDINARY_INVOICE.getCode(),           // 04-增值税普通发票
                OcrBatchInvoiceTypeEnum.ELECTRONIC_VAT_SPECIAL_INVOICE.getCode(), // 08-增值税电子专用发票
                OcrBatchInvoiceTypeEnum.ELECTRONIC_VAT_ORDINARY_INVOICE.getCode(),//10-增值税电子普通发票
                OcrBatchInvoiceTypeEnum.VAT_ORDINARY_ROLL_INVOICE.getCode(),      // 11-增值税普通发票(卷式)
                OcrBatchInvoiceTypeEnum.ELECTRONIC_TOLL_INVOICE.getCode(),        // 14-道路通行费电子普通发票
                OcrBatchInvoiceTypeEnum.USED_CAR_SALES_INVOICE.getCode(),         // 15-二手车销售统一发票
                OcrBatchInvoiceTypeEnum.DIGITAL_VAT_SPECIAL.getCode(),            // 31-数电票（增值税专用发票）
                OcrBatchInvoiceTypeEnum.DIGITAL_VAT_ORDINARY.getCode(),           // 32-数电票（普通发票）
                OcrBatchInvoiceTypeEnum.DIGITAL_RAIL_TICKET.getCode(),            // 51-数电票（铁路电子客票）
                OcrBatchInvoiceTypeEnum.DIGITAL_AIR_ITINERARY.getCode(),          // 61-数电票（航空运输电子客票单）
                OcrBatchInvoiceTypeEnum.DIGITAL_PAPER_VAT_SPECIAL.getCode(),      // 85-数电纸质发票（增值税专用发票）
                OcrBatchInvoiceTypeEnum.DIGITAL_PAPER_VAT_ORDINARY.getCode()      // 86-数电纸质发票（普通发票）
        );
    }

    public static InvoiceTypeEnum getInvoiceTypeByCode(String code) {
        if (StringUtils.isEmpty(code)) {
            return null;
        }

        for (OcrBatchInvoiceTypeEnum ocrBatchInvoiceTypeEnum : OcrBatchInvoiceTypeEnum.values()) {
            if (ocrBatchInvoiceTypeEnum.getCode().equals(code)) {
                return ocrBatchInvoiceTypeEnum.getInvoiceType();
            }
        }
        return null;
    }

    public static List<String> needResetPurchaserTaxNoCodeList() {
        return Arrays.asList(
                OcrBatchInvoiceTypeEnum.DIGITAL_RAIL_TICKET.getCode(),
                OcrBatchInvoiceTypeEnum.TRAIN_TICKET.getCode(),
                OcrBatchInvoiceTypeEnum.DIGITAL_AIR_ITINERARY.getCode(),
                OcrBatchInvoiceTypeEnum.AIR_ITINERARY.getCode(),
                OcrBatchInvoiceTypeEnum.DIGITAL_VAT_ORDINARY.getCode(),
                OcrBatchInvoiceTypeEnum.DIGITAL_VAT_SPECIAL.getCode()
        );
    }
}
