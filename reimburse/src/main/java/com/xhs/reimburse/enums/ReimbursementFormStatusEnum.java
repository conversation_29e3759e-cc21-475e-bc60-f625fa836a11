package com.xhs.reimburse.enums;

import com.xhs.oa.office.utils.AssertHelper;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * [2, 3, 4]由REDflow控制
 *
 * <AUTHOR>
 * @date :2025/02/08 - 下午4:53
 * @description : 报销单状态
 */
@AllArgsConstructor
@Getter
public enum ReimbursementFormStatusEnum {
    CG(1, "草稿"),
    SHZ(2, "审核中"),

    // 审批人「驳回」至「发起人提交」节点
    BH(3, "驳回"),

    // 发起人「撤回」单据
    CH(4, "撤回"),

    // 发起人「终止」单据
    YZZ(5, "已终止"),

    // 发起人「删除」单据
    YSC(6, "已删除"),

    // 财务打款
    YWJ(7, "已完结");

    private final Integer code;
    private final String name;

    public static String getDescByStatus(Integer reimburseStatus) {
        ReimbursementFormStatusEnum status = getEnumByStatus(reimburseStatus);
        return Objects.nonNull(status) ? status.getName() : "";
    }

    /**
     * 待处理 PENDING
     * 处理中 PROCESSING
     * 已报销 DONE
     */
    public static List<Integer> getCodeByQueryStatus(String queryStatus) {
        AssertHelper.notBlank(queryStatus, "未知查询状态");
        switch (queryStatus) {
            case "PENDING":
                return Arrays.asList(CG.getCode(), BH.getCode(), CH.getCode());
            case "PROCESSING":
                return Arrays.asList(SHZ.getCode());
            case "DONE":
                return Arrays.asList(YWJ.getCode(), YZZ.getCode());
            default:
                return Arrays.asList(CG.getCode(), BH.getCode());
        }
    }

    //提交人可以编辑的状态
    public static Boolean canEditStatus(Integer reimburseStatus) {
        return Arrays.asList(CG.getCode(), BH.getCode(), CH.getCode()).contains(reimburseStatus);
    }

    public static ReimbursementFormStatusEnum getEnumByStatus(Integer reimburseStatus) {
        for (ReimbursementFormStatusEnum formStatusEnum : ReimbursementFormStatusEnum.values()) {
            if (formStatusEnum.getCode().equals(reimburseStatus)) {
                return formStatusEnum;
            }
        }
        return null;
    }
}
