package com.xhs.reimburse.enums.travel;

import com.xhs.reimburse.modal.dto.travel.CheckResultItemDto;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 费用校验项目枚举类
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ExpenseCheckItemEnum.java
 * @createTime 2025年03月18日 10:33:00
 */
@Getter
@AllArgsConstructor
public enum ExpenseCheckItemEnum {

    /**
     * 费用行程校验
     */
    EXPENSE_ITINERARY_CHECK("EXPENSE_ITINERARY_CHECK","费用行程校验", true,"关联差旅申请中无%s行程"),

    /**
     * 费用地点校验
     */
    EXPENSE_LOCATION_CHECK("EXPENSE_LOCATION_CHECK","费用地点校验", true,"关联差旅申请中无%s行程"),

    /**
     * 费用天数校验
     */
    EXPENSE_DURATION_CHECK("EXPENSE_DURATION_CHECK","费用天数校验", true,"出差天数超出了关联申请的总天数"),

    /**
     * 费用状态校验
     */
    EXPENSE_STATUS_CHECK("EXPENSE_STATUS_CHECK","费用状态校验", false, "该费用的tag为「待完善」"),

    /**
     * 发票验真
     */
    EXPENSE_INVOICE_VERIFICATION_CHECK("EXPENSE_INVOICE_VERIFICATION_CHECK", "发票验真", true, "存在验真失败的发票"),

    /**
     * 费用主体校验
     */
    EXPENSE_COMPANY_CHECK("EXPENSE_COMPANY_CHECK", "费用主体校验", true, "存在主体与报销单中付款公司不一致的发票"),

    /**
     * 费用时间校验
     */
    EXPENSE_TIME_CHECK("EXPENSE_TIME_CHECK", "费用时间校验", false, "存在开票时间异常发票"),

    /**
     * 费用金额校验-油电费用
     */
    EXPENSE_EXPENSE_EXCEED_REIMBURSABLE_LIMIT("EXPENSE_EXPENSE_EXCEED_REIMBURSABLE_LIMIT","费用金额校验-油电费用", true,"报销金额超出了可报销金额"),

    /**
     * 费用差标校验-火车票/飞机
     */
    EXPENSE_SEAT_STANDARD_DIFFERENCE_CHECK("EXPENSE_SEAT_STANDARD_DIFFERENCE_CHECK","费用差标校验-火车票/飞机", false,"存在超标%s"),

    /**
     * 费用差标校验-其他
     */
    EXPENSE_COMMON_STANDARD_DIFFERENCE_CHECK("EXPENSE_COMMON_STANDARD_DIFFERENCE_CHECK","费用差标校验-其他", false,"提报金额超标"),

    /**
     * 费用完整性校验
     */
    EXPENSE_INTEGRITY_CHECK("EXPENSE_INTEGRITY_CHECK", "费用完整性校验", true, "费用为待完善");


    private final String code;
    private final String name;
    private final Boolean blockSubmit;
    private final String description;

    public static CheckResultItemDto getCheckResultItemDto(ExpenseCheckItemEnum type) {
        CheckResultItemDto checkResultItemDto = new CheckResultItemDto();
        checkResultItemDto.setCode(type.getCode());
        checkResultItemDto.setDescription(type.getDescription());
        return checkResultItemDto;
    }

    /**
     * 座位超预算
     */
    public static boolean seatOverStandard(String code) {
        return EXPENSE_SEAT_STANDARD_DIFFERENCE_CHECK.getCode().equals(code);
    }

    /**
     * 差标超预算
     */
    public static boolean overStandard(String code) {
        return EXPENSE_SEAT_STANDARD_DIFFERENCE_CHECK.getCode().equals(code) || EXPENSE_COMMON_STANDARD_DIFFERENCE_CHECK.getCode().equals(code);
    }

}
