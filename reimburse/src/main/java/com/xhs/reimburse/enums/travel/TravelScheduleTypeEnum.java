package com.xhs.reimburse.enums.travel;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TravelScheduleTypeEnum.java
 * @createTime 2025年03月18日 19:23:00
 */
@Getter
@AllArgsConstructor
public enum TravelScheduleTypeEnum {

    /**
     * 允许携程预定
     */
    HOTEL("hotel", "ctrip", "酒店住宿", "hotel"),

    FLIGHT("flight", "ctrip", "飞机", "traffic"),

    /**
     * 其他平台预定
     */
    TRAIN("train", "ctrip", "火车", "traffic"),

    BUS("bus", "other", "汽车", "traffic"),

    OTHER("other", "other", "其他", "traffic");

    public static final List<String> TYPES = Arrays.asList(HOTEL.getCode(), FLIGHT.getCode(), TRAIN.getCode());

    public static final String TYPE_CTRIP = "ctrip";

    public static final String TYPE_OTHER = "other";

    private String code;

    private String type;

    private String name;

    private String routeType;
}
