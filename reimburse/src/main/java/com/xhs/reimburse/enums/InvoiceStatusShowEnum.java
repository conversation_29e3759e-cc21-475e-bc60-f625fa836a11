package com.xhs.reimburse.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName InvoiceStatusShowEnum.java
 * @createTime 2025年03月06日 15:39:00
 */
@AllArgsConstructor
@Getter
public enum InvoiceStatusShowEnum {

    DBC(1, "待补充"),
    WGL(2, "可提报"), // 未关联
    KTB(3, "可提报"),

    SHZ(4, "审核中"),
    CH(5, "撤回"),
    BH(6, "驳回"),
    YBX(7, "已报销");

    @EnumValue
    private final int code;
    private final String name;


    public static InvoiceStatusShowEnum getInvoiceStatusShowEnum(Integer invoiceStatus, Integer invoiceExpenseStatus, Integer invoiceFormStatus) {
        if (invoiceStatus.equals(InvoiceStatusEnum.DBC.getCode()) && invoiceExpenseStatus.equals(InvoiceExpenseStatusEnum.WGL.getCode()) && invoiceFormStatus.equals(InvoiceFormStatusEnum.WGL.getCode())) {
            return InvoiceStatusShowEnum.DBC;
        }

        if (invoiceStatus.equals(InvoiceStatusEnum.YBC.getCode()) && invoiceExpenseStatus.equals(InvoiceExpenseStatusEnum.WGL.getCode()) && invoiceFormStatus.equals(InvoiceFormStatusEnum.WGL.getCode())) {
            return InvoiceStatusShowEnum.WGL;
        }

        if (invoiceStatus.equals(InvoiceStatusEnum.YBC.getCode()) && invoiceExpenseStatus.equals(InvoiceExpenseStatusEnum.YGL.getCode()) && invoiceFormStatus.equals(InvoiceFormStatusEnum.WGL.getCode())) {
            return InvoiceStatusShowEnum.KTB;
        }

        if (invoiceStatus.equals(InvoiceStatusEnum.YBC.getCode()) && invoiceExpenseStatus.equals(InvoiceExpenseStatusEnum.YGL.getCode()) && invoiceFormStatus.equals(InvoiceFormStatusEnum.SHZ.getCode())) {
            return InvoiceStatusShowEnum.SHZ;
        }

        if (invoiceStatus.equals(InvoiceStatusEnum.YBC.getCode()) && invoiceExpenseStatus.equals(InvoiceExpenseStatusEnum.YGL.getCode()) && invoiceFormStatus.equals(InvoiceFormStatusEnum.CH.getCode())) {
            return InvoiceStatusShowEnum.CH;
        }

        if (invoiceStatus.equals(InvoiceStatusEnum.YBC.getCode()) && invoiceExpenseStatus.equals(InvoiceExpenseStatusEnum.YGL.getCode()) && invoiceFormStatus.equals(InvoiceFormStatusEnum.BH.getCode())) {
            return InvoiceStatusShowEnum.BH;
        }

        if (invoiceStatus.equals(InvoiceStatusEnum.YBC.getCode()) && invoiceExpenseStatus.equals(InvoiceExpenseStatusEnum.YGL.getCode()) && invoiceFormStatus.equals(InvoiceFormStatusEnum.YBX.getCode())) {
            return InvoiceStatusShowEnum.YBX;
        }

        return null;
    }
}
