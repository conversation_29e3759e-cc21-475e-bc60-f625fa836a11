package com.xhs.reimburse.enums.travel;

import com.xhs.reimburse.modal.dto.travel.CheckResultItemDto;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 发票校验项目枚举类
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName InvoiceCheckItemEnum.java
 * @createTime 2025年03月18日 11:29:00
 */
@Getter
@AllArgsConstructor
public enum InvoiceCheckItemEnum {

    /**
     * 发票类型校验
     */
    INVOICE_TYPE_CHECK("INVOICE_TYPE_CHECK", "发票类型校验", true, "%s不支持上传该类型发票"),

    /**
     * 发票验真
     */
    INVOICE_AUTHENTICATION_CHECK("INVOICE_AUTHENTICATION_CHECK", "发票验真", true, "验真失败"),

    /**
     * 发票主体校验
     */
    INVOICE_ENTITY_CHECK("INVOICE_ENTITY_CHECK","发票主体校验", false, "发票主体与报销单中的付款公司不一致"),

    /**
     * 发票时间校验
     */
    INVOICE_TIME_CHECK("INVOICE_TIME_CHECK","发票时间校验", false, "开票日期不在关联差旅行程时间段内，建议在附件处上传支付截图等真实消费日期证明"),

    /**
     * 发票关联费用状态校验
     */
    INVOICE_RELATION_EXPENSE_STATUS_CHECK("INVOICE_RELATION_EXPENSE_STATUS_CHECK", "发票关联费用状态校验", true, "费用为待完善"),

    /**
     * 发票完整性校验
     */
    INVOICE_INTEGRITY_CHECK("INVOICE_INTEGRITY_CHECK", "发票完整性校验", true, "费用为待完善");

    private final String code;
    private final String name;
    private final Boolean blockSubmit;
    private final String description;

    public static CheckResultItemDto getCheckResultItemDto(InvoiceCheckItemEnum type) {
        CheckResultItemDto checkResultItemDto = new CheckResultItemDto();
        checkResultItemDto.setCode(type.getCode());
        checkResultItemDto.setDescription(type.getDescription());
        return checkResultItemDto;
    }
}
