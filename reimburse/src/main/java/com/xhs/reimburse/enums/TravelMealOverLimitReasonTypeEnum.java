package com.xhs.reimburse.enums;

import com.xhs.reimburse.modal.response.LabelValueResponse;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TravelMealOverLimitReasonTypeEnum.java
 * @createTime 2025年03月13日 14:19:00
 */
@Getter
@AllArgsConstructor
public enum TravelMealOverLimitReasonTypeEnum {
    OTHER("other","其他"),
    MULTI_EMPLOYEE_MEAL("multi_employee_meal","多人餐费");

    private final String code;
    private final String name;

    public static List<LabelValueResponse> getList(){
        List<LabelValueResponse> list = new ArrayList<>();
        for (TravelMealOverLimitReasonTypeEnum type : TravelMealOverLimitReasonTypeEnum.values()) {
            LabelValueResponse response = new LabelValueResponse();
            response.setLabel(type.getName());
            response.setValue(type.getCode());
            list.add(response);
        }
        return list;
    }
}
