package com.xhs.reimburse.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ExpenseStatusShowEnum.java
 * @createTime 2025年03月06日 16:30:00
 */
@AllArgsConstructor
@Getter
public enum ExpenseStatusShowEnum {
    DBC(1, "待完善"),
    WGL(2, "可提报"), // 未关联
    KTB(3, "可提报"),

    SHZ(4, "审核中"),
    CH(5, "撤回"),
    BH(6, "驳回"),
    YBX(7, "已报销"),

    DBC_YGL(8,"待完善");

    @EnumValue
    private final int code;
    private final String name;

    // 2 2 3
    public static ExpenseStatusShowEnum getExpenseStatusShowEnum(Integer expenseStatus, Integer invoiceExpenseStatus, Integer expenseFormStatus) {
        if (expenseStatus.equals(ExpenseStatusEnum.DBC.getCode()) && expenseFormStatus.equals(ExpenseFormStatusEnum.WGL.getCode())) {
            return ExpenseStatusShowEnum.DBC;
        }

        if (expenseStatus.equals(ExpenseStatusEnum.DBC.getCode()) && expenseFormStatus.equals(ExpenseFormStatusEnum.YGL.getCode())) {
            return ExpenseStatusShowEnum.DBC_YGL;
        }

        if (expenseStatus.equals(InvoiceStatusEnum.YBC.getCode()) && expenseFormStatus.equals(ExpenseFormStatusEnum.WGL.getCode())) {
            return ExpenseStatusShowEnum.WGL;
        }

        if (expenseStatus.equals(InvoiceStatusEnum.YBC.getCode()) && expenseFormStatus.equals(ExpenseFormStatusEnum.YGL.getCode())) {
            return ExpenseStatusShowEnum.KTB;
        }

        if (expenseStatus.equals(InvoiceStatusEnum.YBC.getCode()) && expenseFormStatus.equals(ExpenseFormStatusEnum.SHZ.getCode())) {
            return ExpenseStatusShowEnum.SHZ;
        }

        if (expenseStatus.equals(InvoiceStatusEnum.YBC.getCode()) && expenseFormStatus.equals(ExpenseFormStatusEnum.CH.getCode())) {
            return ExpenseStatusShowEnum.CH;
        }

        if (expenseStatus.equals(InvoiceStatusEnum.YBC.getCode()) && expenseFormStatus.equals(ExpenseFormStatusEnum.BH.getCode())) {
            return ExpenseStatusShowEnum.BH;
        }

        if (expenseStatus.equals(InvoiceStatusEnum.YBC.getCode()) && expenseFormStatus.equals(ExpenseFormStatusEnum.YBX.getCode())) {
            return ExpenseStatusShowEnum.YBX;
        }

        return null;
    }
}
