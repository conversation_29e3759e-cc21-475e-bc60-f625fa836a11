package com.xhs.reimburse.enums;

import com.xhs.reimburse.modal.dto.ExpenseDto;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * [1, 2] 在C端内部扭转，每次update的时候会校验，所有字段都通过时1->2
 * [3, 4] 由REDflow的MQ控制
 *
 * <AUTHOR>
 * @date :2025/02/08 - 下午4:50
 * @description : 费用状态
 */
@AllArgsConstructor
@Getter
public enum ExpenseStatusEnum {
    // TAB - 待处理
    DBC(1, "待完善"),
    YBC(2, "已完善");

    private final Integer code;
    private final String name;

    public static Boolean checkExpenseCanSubmit(ExpenseDto expenseDto) {
        if (ExpenseStatusEnum.YBC.getCode() == expenseDto.getExpenseStatus() && Arrays.asList(
                ExpenseFormStatusEnum.WGL.getCode(), ExpenseFormStatusEnum.CH.getCode()
                , ExpenseFormStatusEnum.BH.getCode(), ExpenseFormStatusEnum.YGL.getCode())
                .contains(expenseDto.getExpenseFormStatus())) {
            return true;
        }
        return false;
    }
}
