package com.xhs.reimburse.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Getter
public enum ReimburseTypeEnum {

    YBFYBXD("YBFYBXD", "一般费用报销单"),
    CLBXD("CLBXD", "差旅报销单"),
    TJBXD("TJBXD", "团建报销单"),
    YDFQBXD("YDFQBXD", "异地夫妻报销单"),
    ;

    private String type;

    private String name;

    ReimburseTypeEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    public static String getFormTypeName(String formType) {
        ReimburseTypeEnum reimburseTypeEnum = getEnumByType(formType);
        return Objects.nonNull(reimburseTypeEnum) ? reimburseTypeEnum.getName() : "";
    }

    public static ReimburseTypeEnum getEnumByType(String formType) {
        for (ReimburseTypeEnum formTypeEnum : ReimburseTypeEnum.values()) {
            if (formTypeEnum.getType().equals(formType)) {
                return formTypeEnum;
            }
        }
        return null;
    }
}
