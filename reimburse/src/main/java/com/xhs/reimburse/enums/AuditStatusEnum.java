package com.xhs.reimburse.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date :2025/02/26 - 下午2:38
 * @description :
 */
@AllArgsConstructor
@Getter
public enum AuditStatusEnum {
    IN_REVIEW("IN_REVIEW","审批中"),
    AUDIT_PASS("AUDIT_PASS","审核通过"),
    AUDIT_REFUSE("AUDIT_REFUSE","审核拒绝"),
    AUDIT_TERMINATE("AUDIT_TERMINATE","已终止"),
    WITHDRAWAL("WITHDRAWAL", "撤回待发起"),
    DELETE("DELETE","已删除");

    private String status;
    private String desc;
}
