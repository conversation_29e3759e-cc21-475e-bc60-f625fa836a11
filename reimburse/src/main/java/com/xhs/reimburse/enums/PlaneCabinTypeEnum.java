package com.xhs.reimburse.enums;

import com.xhs.reimburse.modal.response.LabelValueResponse;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName PlaneCabinTypeEnum.java
 * @createTime 2025年03月10日 23:59:00
 */
@Getter
@AllArgsConstructor
public enum PlaneCabinTypeEnum {

    ECONOMY("经济舱"),
    FIRST_CLASS("头等舱"),
    BUSINESS("商务舱");

    private final String name;

    public static List<LabelValueResponse> getPlaneCabinList() {
        return Arrays.stream(PlaneCabinTypeEnum.values())
                .map(seatType -> {
                    LabelValueResponse response = new LabelValueResponse();
                    response.setValue(seatType.getName());
                    response.setLabel(seatType.getName());
                    return response;
                }).collect(Collectors.toList());
    }
}
