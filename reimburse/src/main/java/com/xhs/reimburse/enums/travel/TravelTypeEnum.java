package com.xhs.reimburse.enums.travel;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TravelTypeEnum.java
 * @createTime 2025年03月18日 17:45:00
 */
@Getter
public enum TravelTypeEnum {
    /**
     * 短期行程
     */
    SHORT_TERM("short_term", "员工-短期出差（3个月以内）"),
    /**
     * 长期行程
     */
    LONG_TERM("long_term", "员工-长期出差（3-12个月）"),
    /**
     * 字段对应短期行程
     */
    EXTERNAL("external", "替外部人员代订");

    private String code;

    private String name;

    TravelTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code) {
        for (TravelTypeEnum typeEnum : TravelTypeEnum.values()) {
            if (StringUtils.equals(code, typeEnum.getCode())) {
                return typeEnum.getName();
            }
        }
        return "";
    }
}

