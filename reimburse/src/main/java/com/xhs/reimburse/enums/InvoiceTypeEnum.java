package com.xhs.reimburse.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName InvoiceTypeEnum.java
 * @createTime 2025年02月19日 10:55:00
 */

@Getter
@AllArgsConstructor
public enum InvoiceTypeEnum {

    /**
     * 电子发票
     */
    INVOICE_SPECIAL("special_invoice", "增值税专用发票（电子）", "专用发票", "电子发票"),
    INVOICE_ORDINARY("ordinary_invoice", "增值税普通发票（电子）", "普通发票", "电子发票"),
    INVOICE_MACHINE_ELECTRONIC("machine_invoice_electronic", "机打电子发票", "普通发票", "电子发票"),

    /**
     * 纸质发票
     */
    INVOICE_SPECIAL_PAPER("invoice_special_paper", "增值税专用发票（纸质）", "专用发票", "纸质发票"),
    INVOICE_ORDINARY_PAPER("invoice_ordinary_paper", "增值税普通发票（纸质）", "普通发票", "纸质发票"),
    INVOICE_QUOTA("invoice_quota", "定额发票", "普通发票", "纸质发票"),
    INVOICE_MACHINE("invoice_machine", "通用机打发票", "普通发票", "纸质发票"),

    /**
     * 车票
     */
    TAXI_TICKET("taxi", "的士票", "的士票", "车票"),
    TRAIN_TICKET("train", "火车票", "火车票", "车票"),
    ELECTRONIC_TRAIN_TICKET("electronic_train", "电子火车票", "火车票", "车票"),
    AIR_TICKET("air", "飞机行程单", "行程单", "车票"),
    BUS_TICKET("bus", "汽车票", "汽车票", "车票"),
    INVOICE_TOLL("toll", "通行费", "通行费", "车票"),
    AIR_TICKET_ELECTRONIC("air_ticket_electronic", "航空运输电子客票行程单", "行程单", "车票"),

    /**
     * 其他发票
     */
    OTHER("other", "其他票", "其他票", "其他发票");

    private final String code;
    private final String name;
    private final String tagName;
    private final String showDesc;

    public static InvoiceTypeEnum getByCode(String code) {
        for (InvoiceTypeEnum type : InvoiceTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return OTHER;
    }

    public static String getInvoiceTag(String code) {
        return getByCode(code).getTagName();
    }

    public static List<String> getNeedCheckCompany() {
        return Arrays.asList(
                InvoiceTypeEnum.INVOICE_SPECIAL_PAPER.getCode(),
                InvoiceTypeEnum.INVOICE_SPECIAL.getCode(),
                InvoiceTypeEnum.INVOICE_MACHINE.getCode(),
                InvoiceTypeEnum.INVOICE_MACHINE_ELECTRONIC.getCode(),
                InvoiceTypeEnum.INVOICE_ORDINARY.getCode(),
                InvoiceTypeEnum.INVOICE_ORDINARY_PAPER.getCode()
        );
    }

    public static List<String> paperTickets() {
        return Arrays.asList(INVOICE_SPECIAL_PAPER.getCode(), INVOICE_ORDINARY_PAPER.getCode(), INVOICE_QUOTA.getCode()
                , INVOICE_MACHINE.getCode(), TAXI_TICKET.getCode(), TRAIN_TICKET.getCode(), BUS_TICKET.getCode()
                , INVOICE_TOLL.getCode(), AIR_TICKET.getCode(), OTHER.getCode());
    }
}
