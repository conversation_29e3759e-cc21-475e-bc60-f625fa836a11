package com.xhs.reimburse.enums;

import com.google.common.collect.Lists;
import com.xhs.reimburse.modal.response.LabelValueExtendResponse;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ExpenseFirstSubjectEnum.java
 * @createTime 2025年02月25日 14:28:00
 */
@Getter
@AllArgsConstructor
public enum ExpenseFirstSubjectEnum {

    /**
     * 一般费用报销单
     */
    YBFY_SNJT("snjt", "市内交通",true, "", Lists.newArrayList(ExpenseSecondSubjectEnum.EXPRESS_FEE, ExpenseSecondSubjectEnum.RIDE_SHARE, ExpenseSecondSubjectEnum.FUEL_FEE, ExpenseSecondSubjectEnum.ELECTRIC_FEE, ExpenseSecondSubjectEnum.OTHER, ExpenseSecondSubjectEnum.EXPRESS_RIDE_FEE, ExpenseSecondSubjectEnum.FUEL_ELECTRIC_OTHER_FEE), Collections.emptyList()),
    YBFY_DAILY("daily", "日常费用", true, "", Lists.newArrayList(ExpenseSecondSubjectEnum.KDYF, ExpenseSecondSubjectEnum.ZYSJ, ExpenseSecondSubjectEnum.PXJF, ExpenseSecondSubjectEnum.GZC, ExpenseSecondSubjectEnum.TJF), Collections.emptyList()),
    YBFY_RECEPTION("reception", "业务招待费", true, "", Lists.newArrayList(ExpenseSecondSubjectEnum.YWZDCY, ExpenseSecondSubjectEnum.YWZDQT), Collections.emptyList()),
    YBFY_CONFERENCE("conference", "会议和活动", true, "", Lists.newArrayList(ExpenseSecondSubjectEnum.CDZLF, ExpenseSecondSubjectEnum.BZF, ExpenseSecondSubjectEnum.HYYP, ExpenseSecondSubjectEnum.HYJTF), Collections.emptyList()),
    YBFY_EXT_AFFAIR("ext_affair", "对外事务", true, "", Lists.newArrayList(ExpenseSecondSubjectEnum.ZZZZ, ExpenseSecondSubjectEnum.FLXG, ExpenseSecondSubjectEnum.XZFK), Collections.emptyList()),
    YBFY_GOODS("goods", "商品购置/租赁/维修", true, "", Lists.newArrayList(ExpenseSecondSubjectEnum.BGHCF, ExpenseSecondSubjectEnum.SBWXF, ExpenseSecondSubjectEnum.ITDZCP, ExpenseSecondSubjectEnum.SPYF, ExpenseSecondSubjectEnum.YPCGF), Collections.emptyList()),
    YBFY_PERSONNEL("personnel", "人事专用", true, "", Lists.newArrayList(ExpenseSecondSubjectEnum.RYJLJF, ExpenseSecondSubjectEnum.JDZPRY), Collections.emptyList()),
    YBFY_ADMIN("admin_ware", "行政费用", true, "", Lists.newArrayList(ExpenseSecondSubjectEnum.WXF, ExpenseSecondSubjectEnum.ZSJJ, ExpenseSecondSubjectEnum.BGWJ, ExpenseSecondSubjectEnum.GWCZF, ExpenseSecondSubjectEnum.RYP, ExpenseSecondSubjectEnum.QYHD, ExpenseSecondSubjectEnum.STHD), Collections.emptyList()),
    YBFY_OTHER("other", "其他费用", true, "", Lists.newArrayList(ExpenseSecondSubjectEnum.JSKF, ExpenseSecondSubjectEnum.CGHZ, ExpenseSecondSubjectEnum.WBFWF, ExpenseSecondSubjectEnum.QTSX), Collections.emptyList()),


    /**
     * 差旅报销单
     */
    CL_HOTEL("hotel", "短期出差住宿", true, "", Lists.newArrayList(ExpenseSecondSubjectEnum.BSGS, ExpenseSecondSubjectEnum.GNQTCS, ExpenseSecondSubjectEnum.YZCS, ExpenseSecondSubjectEnum.YZYWCS), Collections.emptyList()),
    CL_LONG_TRIP("long_trip", "长期出差住宿", true, "", Lists.newArrayList(ExpenseSecondSubjectEnum.BSGS, ExpenseSecondSubjectEnum.GNQTCS, ExpenseSecondSubjectEnum.YZCS, ExpenseSecondSubjectEnum.YZYWCS), Collections.emptyList()),
    CL_BUSINESS_MEAL("business_meal", "差旅工作餐", true, "", Lists.newArrayList(ExpenseSecondSubjectEnum.JNGZC, ExpenseSecondSubjectEnum.JWGZC), Collections.emptyList()),
    CL_TRAIN_TICKET("train_ticket", "火车票", true, "", Collections.emptyList(), Lists.newArrayList(InvoiceTypeEnum.TRAIN_TICKET, InvoiceTypeEnum.ELECTRONIC_TRAIN_TICKET)),
    CL_AIR_TICKET("air_ticket", "飞机票", true, "", Collections.emptyList(), Lists.newArrayList(InvoiceTypeEnum.AIR_TICKET, InvoiceTypeEnum.AIR_TICKET_ELECTRONIC, InvoiceTypeEnum.INVOICE_ORDINARY)),
    CL_TAXI_TICKET("taxi_ticket", "出租车", true, "", Collections.emptyList(), Lists.newArrayList(InvoiceTypeEnum.TAXI_TICKET, InvoiceTypeEnum.INVOICE_QUOTA,
            InvoiceTypeEnum.OTHER, InvoiceTypeEnum.INVOICE_TOLL, InvoiceTypeEnum.INVOICE_ORDINARY,
            InvoiceTypeEnum.INVOICE_ORDINARY_PAPER)),
    CL_DIDI("didi", "网约车", true, "原则上仅限快车、优享，若有特殊情况请说明升级车型理由&证明：如多人出行、商务车费用人均价格最低", Collections.emptyList(), Lists.newArrayList(InvoiceTypeEnum.INVOICE_ORDINARY, InvoiceTypeEnum.INVOICE_ORDINARY_PAPER)),
    CL_BUS_TICKET("bus_ticket", "大巴票", true, "", Collections.emptyList(), Lists.newArrayList(InvoiceTypeEnum.BUS_TICKET)),
    CL_FERRY_TICKET("ferry_ticket", "轮船票", true, "", Collections.emptyList(), Lists.newArrayList(InvoiceTypeEnum.INVOICE_SPECIAL, InvoiceTypeEnum.INVOICE_ORDINARY,
            InvoiceTypeEnum.INVOICE_SPECIAL_PAPER, InvoiceTypeEnum.INVOICE_ORDINARY_PAPER,
            InvoiceTypeEnum.INVOICE_QUOTA, InvoiceTypeEnum.INVOICE_MACHINE_ELECTRONIC, InvoiceTypeEnum.INVOICE_MACHINE)),
    CL_OVERSEAS_TRAFFIC("overseas_traffic", "境外交通（外币）", true, "", Collections.emptyList(), ExpenseSecondSubjectEnum.getAllInvoiceTypeEnum()),
    CL_FUEL_PARKING("fuel_parking", "油电车费", true, "", Lists.newArrayList(ExpenseSecondSubjectEnum.YF, ExpenseSecondSubjectEnum.DF), Collections.emptyList()),
    CL_OTHER("other", "其他", true, "", Collections.emptyList(), ExpenseSecondSubjectEnum.getAllInvoiceTypeEnum()),


    /**
     * 异地夫妻交通报销科目
     */
    YBFQBXD_TRAIN_TICKET("train_ticket", "火车票", true, "夫妻异地交通中乘坐高铁/火车产生的费用", Collections.emptyList(),
            Lists.newArrayList(InvoiceTypeEnum.TRAIN_TICKET, InvoiceTypeEnum.ELECTRONIC_TRAIN_TICKET)
    ),

    YBFQBXD_AIR_TICKET("air_ticket", "飞机票", true, "夫妻异地交通中乘坐飞机产生的费用", Collections.emptyList(),
            Lists.newArrayList(InvoiceTypeEnum.AIR_TICKET, InvoiceTypeEnum.INVOICE_SPECIAL
                    , InvoiceTypeEnum.AIR_TICKET_ELECTRONIC, InvoiceTypeEnum.INVOICE_ORDINARY)
    ),

    /**
     * 团建费用报销单
     */
    TJBXD_HOTEL("welfare_hotel", "酒店住宿", true, "团建过程中住宿产生的费用",
            Collections.emptyList(),
            Lists.newArrayList(InvoiceTypeEnum.INVOICE_SPECIAL, InvoiceTypeEnum.INVOICE_ORDINARY, InvoiceTypeEnum.INVOICE_SPECIAL_PAPER, InvoiceTypeEnum.INVOICE_ORDINARY_PAPER, InvoiceTypeEnum.INVOICE_QUOTA, InvoiceTypeEnum.INVOICE_MACHINE_ELECTRONIC, InvoiceTypeEnum.INVOICE_MACHINE, InvoiceTypeEnum.OTHER)
    ),

    TJBXD_MEAL("welfare_meal", "餐饮", true, "团建过程中餐饮产生的费用",
            Collections.emptyList(),
            Lists.newArrayList(InvoiceTypeEnum.INVOICE_SPECIAL, InvoiceTypeEnum.INVOICE_ORDINARY, InvoiceTypeEnum.INVOICE_SPECIAL_PAPER, InvoiceTypeEnum.INVOICE_ORDINARY_PAPER, InvoiceTypeEnum.INVOICE_QUOTA, InvoiceTypeEnum.INVOICE_MACHINE_ELECTRONIC, InvoiceTypeEnum.INVOICE_MACHINE, InvoiceTypeEnum.OTHER)
    ),


    TJBXD_TAXI_TICKET("welfare_taxi_ticket", "出租车", true, "团建过程中需要乘坐出租/的士产生的费用",
            Collections.emptyList(),
            Lists.newArrayList(InvoiceTypeEnum.TAXI_TICKET, InvoiceTypeEnum.INVOICE_QUOTA, InvoiceTypeEnum.OTHER, InvoiceTypeEnum.INVOICE_TOLL, InvoiceTypeEnum.INVOICE_ORDINARY, InvoiceTypeEnum.INVOICE_ORDINARY_PAPER)
    ),

    TJBXD_DIDI("welfare_didi", "网约车", true, "团建过程中打车产生的费用", Collections.emptyList(),
            Lists.newArrayList(InvoiceTypeEnum.INVOICE_ORDINARY, InvoiceTypeEnum.INVOICE_ORDINARY_PAPER)
    ),

    TJBXD_TRAIN_TICKET("welfare_train_ticket", "火车票", true, "团建过程中乘坐火车/高铁产生的费用", Collections.emptyList(),
            Lists.newArrayList(InvoiceTypeEnum.TRAIN_TICKET)
    ),

    TJBXD_AIR_TICKET("welfare_air_ticket", "飞机票", true, "团建过程中乘坐飞机产生的费用", Collections.emptyList(),
            ExpenseSecondSubjectEnum.getAllInvoiceTypeEnum()
    ),


    TJBXD_BUS_TICKET("welfare_bus_ticket", "大巴票", true, "团建过程中乘坐大巴车产生的费用", Collections.emptyList(),
            Lists.newArrayList(InvoiceTypeEnum.BUS_TICKET)
    ),

    TJBXD_FERRY_TICKET("welfare_ferry_ticket", "轮船票", true, "团建过程中乘坐轮船产生的费用", Collections.emptyList(),
            Lists.newArrayList(InvoiceTypeEnum.INVOICE_SPECIAL, InvoiceTypeEnum.INVOICE_ORDINARY,
                    InvoiceTypeEnum.INVOICE_SPECIAL_PAPER, InvoiceTypeEnum.INVOICE_ORDINARY_PAPER,
                    InvoiceTypeEnum.INVOICE_QUOTA, InvoiceTypeEnum.INVOICE_MACHINE_ELECTRONIC, InvoiceTypeEnum.INVOICE_MACHINE, InvoiceTypeEnum.OTHER)
    ),

    TJBXD_FUEL_PARKING("welfare_fuel_parking", "油费/停车费", true, "团建过程中用车产生的油费", Collections.emptyList(),
            Lists.newArrayList(InvoiceTypeEnum.INVOICE_SPECIAL, InvoiceTypeEnum.INVOICE_ORDINARY,
                    InvoiceTypeEnum.INVOICE_SPECIAL_PAPER, InvoiceTypeEnum.INVOICE_ORDINARY_PAPER,
                    InvoiceTypeEnum.INVOICE_QUOTA, InvoiceTypeEnum.INVOICE_MACHINE_ELECTRONIC, InvoiceTypeEnum.INVOICE_MACHINE, InvoiceTypeEnum.OTHER,
                    InvoiceTypeEnum.INVOICE_TOLL)
    ),

    TJBXD_CAR_RENTAL("welfare_car_rental", "租车费", true, "团建过程中租车产生的费用", Collections.emptyList(),
            Lists.newArrayList(InvoiceTypeEnum.INVOICE_SPECIAL, InvoiceTypeEnum.INVOICE_ORDINARY,
                    InvoiceTypeEnum.INVOICE_SPECIAL_PAPER, InvoiceTypeEnum.INVOICE_ORDINARY_PAPER,
                    InvoiceTypeEnum.INVOICE_QUOTA, InvoiceTypeEnum.INVOICE_MACHINE_ELECTRONIC, InvoiceTypeEnum.INVOICE_MACHINE, InvoiceTypeEnum.OTHER)
    ),

    TJBXD_ACTIVITIES("welfare_activities", "活动", true, "团建过程中举行活动产生的费用", Collections.emptyList(),
            Lists.newArrayList(InvoiceTypeEnum.INVOICE_SPECIAL, InvoiceTypeEnum.INVOICE_ORDINARY, InvoiceTypeEnum.INVOICE_SPECIAL_PAPER, InvoiceTypeEnum.INVOICE_ORDINARY_PAPER,
                    InvoiceTypeEnum.INVOICE_QUOTA, InvoiceTypeEnum.INVOICE_MACHINE_ELECTRONIC, InvoiceTypeEnum.INVOICE_MACHINE, InvoiceTypeEnum.OTHER)
    ),

    TJBXD_OTHER("welfare_other", "其他", true, "", Collections.emptyList(),
            Lists.newArrayList(InvoiceTypeEnum.INVOICE_SPECIAL, InvoiceTypeEnum.INVOICE_ORDINARY,
                    InvoiceTypeEnum.INVOICE_SPECIAL_PAPER, InvoiceTypeEnum.INVOICE_ORDINARY_PAPER,
                    InvoiceTypeEnum.INVOICE_QUOTA, InvoiceTypeEnum.INVOICE_MACHINE_ELECTRONIC, InvoiceTypeEnum.INVOICE_MACHINE, InvoiceTypeEnum.OTHER,
                    InvoiceTypeEnum.INVOICE_TOLL)
    ),
    ;


    // 一级科目code
    private String subjectCode;
    // 一级科目名称
    private String subjectName;
    // 是否启动true启用false禁用
    private boolean enableFlag;
    // 一级科目描述
    private String description;
    // 二级科目对应的枚举
    private List<ExpenseSecondSubjectEnum> expenseSecondSubjectEnumList;
    // 一级科目允许关联的发票类型
    private List<InvoiceTypeEnum> allowedInvoiceTypeEnums;


    public static List<LabelValueExtendResponse> getSubjectsByFromType(ExpenseFirstSubjectEnum subjectEnum) {
        List<LabelValueExtendResponse> secondSubjects = new ArrayList<>();
        for (ExpenseSecondSubjectEnum subject : subjectEnum.getExpenseSecondSubjectEnumList()) {
            LabelValueExtendResponse response = new LabelValueExtendResponse();
            response.setLabel(subject.getSubjectName());
            response.setValue(subject.getSubjectCode());
            response.setDescription(subject.getDescription());
            response.setChildren(new ArrayList<>());
            response.setValidFlag(subject.isEnableFlag());
            secondSubjects.add(response);
        }
        return secondSubjects;
    }

    public static List<String> getAllowedLabelValues(String subjectCode) {
        for (ExpenseFirstSubjectEnum subjectEnum : ExpenseFirstSubjectEnum.values()) {
            if (subjectEnum.getSubjectCode().equals(subjectCode)) {
                return subjectEnum.getAllowedInvoiceTypeEnums().stream().map(InvoiceTypeEnum::getCode).collect(Collectors.toList());
            }
        }
        return Collections.emptyList();
    }

    public static String getSubjectName(String subjectCode) {
        if (StringUtils.isEmpty(subjectCode)) {
            return "";
        }
        for (ExpenseFirstSubjectEnum subjectEnum : ExpenseFirstSubjectEnum.values()) {
            if (subjectEnum.getSubjectCode().equals(subjectCode)) {
                return subjectEnum.getSubjectName();
            }
        }
        return "";
    }

    /**
     * 属于境外的科目，不进行交票校验
     */
    public static List<String> overseasFirstSubject() {
        return Collections.singletonList(ExpenseFirstSubjectEnum.CL_OVERSEAS_TRAFFIC.getSubjectCode());
    }
}
