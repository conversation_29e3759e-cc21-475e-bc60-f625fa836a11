package com.xhs.reimburse.utils;


import java.util.Objects;
import java.util.regex.Pattern;


public class NewBeeStringUtil {

    private static final String BRACKET_REGEX = "[()（）]+";

    /**
     * 忽略括号内的内容比较两个字符串是否相等
     * <br>1. null : null -> true<br/>
     * <br>2. null : not null -> false<br/>
     * <br>3. A()BC : A（）BC -> true<br/>
     * <br>4. A()BC : ABC -> false<br/>
     *
     * @return 是否相同
     */
    public static boolean ignoreBracketEquals(String s1, String s2) {
        if (Objects.isNull(s1) && Objects.isNull(s2)) {
            return true;
        } else if (Objects.isNull(s1) || Objects.isNull(s2)) {
            return false;
        }
        Pattern bracket = Pattern.compile(BRACKET_REGEX);
        if (bracket.matcher(s1).find() && bracket.matcher(s2).find()) {
            return s1.replaceAll(BRACKET_REGEX, "").equals(s2.replaceAll(BRACKET_REGEX, ""));
        }
        return s1.equals(s2);
    }

}
