package com.xhs.reimburse.utils;

import com.xhs.finance.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;

/**
 * <AUTHOR>
 */
@Slf4j
public class FileUtil {

    public static InputStream getFileInputStream(String fileUrl) {
        InputStream inputStream = null;
        try {
            //从文件链接里获取文件流
            URL url = new URL(fileUrl);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            //设置超时间
            conn.setConnectTimeout(10 * 1000);
            conn.setReadTimeout(10 * 1000);
            //防止屏蔽程序抓取而返回403错误
            conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
            //得到输入流`
            inputStream = conn.getInputStream();

        } catch (FileNotFoundException e) {
            throw new BusinessException("图片找不到");
        } catch (Exception e) {
            throw new BusinessException("responseFileStream error:" + e.getMessage());
        }
        return inputStream;
    }

}
