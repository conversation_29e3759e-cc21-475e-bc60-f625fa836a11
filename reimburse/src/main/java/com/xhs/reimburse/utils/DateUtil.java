package com.xhs.reimburse.utils;

import com.xhs.cache.utils.StringUtils;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DateUtil.java
 * @createTime 2025年04月10日 11:12:00
 */
public class DateUtil {


    /**
     * yyyy-MM-dd 转为 yyyy.MM.dd
     *
     * @param date
     * @return
     */
    public static String convertToDotSeparatedDate(String date) {
        if (StringUtils.isEmpty(date)) {
            return date;
        }

        try {
            LocalDate localDate = LocalDate.parse(date, java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            return localDate.format(java.time.format.DateTimeFormatter.ofPattern("yyyy.MM.dd"));
        } catch (Exception e) {
            return date;
        }
    }
}
