package com.xhs.reimburse.mq.consumer;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.xhs.reimburse.service.ReimburseFormFlowComponentService;
import com.xhs.reimburse.service.ReimburseFormFlowComponentServiceFactory;
import com.xhs.reimburse.service.ReimbursementFormService;
import com.xiaohongshu.events.client.MessageExt;
import com.xiaohongshu.events.client.api.MessageProcessor;
import com.xiaohongshu.events.client.consumer.ConsumeContext;
import com.xiaohongshu.events.client.consumer.ConsumeStatus;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

import static com.xhs.reimburse.enums.AuditStatusEnum.*;

/**
 * <AUTHOR>
 * @date :2025/02/25 - 下午9:13
 * @description :
 */
@Component
@Slf4j
public class RedflowFormAuditProcessor implements MessageProcessor {
    // 常量
    private static final String AUDIT_STATUS = "auditStatus";
    private static final String FORM_TYPE = "formType";
    private static final String FORM_NUM = "formNo";
    private static final String TASK_NODE_KEY = "taskNodeKey";
    private static final String CURRENT_TASK_INFO = "currentTaskInfo";
    private static final String SUBMIT_NODE_KEY = "Activity_submit";
    private static final String PROCESS_END = "processEnd";


    // Service
    @Resource
    private ReimburseFormFlowComponentServiceFactory componentServiceFactory;

    // Service
    @Resource
    private ReimbursementFormService reimbursementFormService;

    @Override
    public ConsumeStatus process(MessageExt msg, ConsumeContext context) {
        JSONObject body = JSON.parseObject(new String(msg.getBody(), StandardCharsets.UTF_8));
        log.info("form = {}", JSONObject.toJSONString(body));

        try {
            String auditStatus = body.getString(AUDIT_STATUS);
            String formType = body.getString(FORM_TYPE);
            String formNum = body.getString(FORM_NUM);
            boolean processEnd = BooleanUtils.toBoolean(body.getString(PROCESS_END));

            // 创建单据选择器
            ReimburseFormFlowComponentService reimburseFormFlowService = componentServiceFactory.getComponentServiceByFormType(formType);

            // 审批人「驳回」至「发起人提交」节点
            if (AUDIT_REFUSE.getStatus().equals(auditStatus)) {
                String currentNodeKey = body.getJSONArray(CURRENT_TASK_INFO).getJSONObject(0).getString(TASK_NODE_KEY);
                if (SUBMIT_NODE_KEY.equals(currentNodeKey)) {
                    reimburseFormFlowService.formRefuse(formNum);
                }
            }

            // 发起人「撤回」单据
            else if (WITHDRAWAL.getStatus().equals(auditStatus)) {
                reimburseFormFlowService.formWithdrawal(formNum);
            }

            // 发起人「删除」&「中止」单据
            else if (DELETE.getStatus().equals(auditStatus) || AUDIT_TERMINATE.getStatus().equals(auditStatus)) {
                reimburseFormFlowService.formTerminate(formNum);
                if (DELETE.getStatus().equals(auditStatus)) {
                    //删除redFlow单据时，作废本地库数据
                    reimbursementFormService.invalidReimbursementForm(formNum);
                }
            }

            // 单据完成
            else if (AUDIT_PASS.getStatus().equals(auditStatus) && processEnd) {
                reimburseFormFlowService.formComplete(formNum);
            }
        } catch (Exception e) {
            log.error("RedflowFormAuditProcessor - {}", e.getMessage(), e);
            return ConsumeStatus.SUCCESS;
        }

        return ConsumeStatus.SUCCESS;
    }
}
