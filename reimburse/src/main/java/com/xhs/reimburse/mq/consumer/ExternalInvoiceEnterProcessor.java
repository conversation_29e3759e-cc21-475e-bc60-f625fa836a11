package com.xhs.reimburse.mq.consumer;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.google.common.collect.Lists;
import com.xhs.reimburse.enums.InvoiceSourceEnum;
import com.xhs.reimburse.modal.dto.ExternalInvoiceInfoDto;
import com.xhs.reimburse.modal.dto.FileInfoDto;
import com.xhs.reimburse.modal.dto.InvoiceDto;
import com.xhs.reimburse.modal.request.BatchOcrParseRequest;
import com.xhs.reimburse.modal.request.BatchSaveInvoiceRequest;
import com.xhs.reimburse.modal.response.BatchOcrParseResponse;
import com.xhs.reimburse.mq.producer.EmailScanInvoiceProducer;
import com.xhs.reimburse.service.InvoiceService;
import com.xhs.reimburse.service.external.ros.ReimburseRosService;
import com.xhs.reimburse.utils.FileUtil;
import com.xiaohongshu.events.client.MessageExt;
import com.xiaohongshu.events.client.api.MessageProcessor;
import com.xiaohongshu.events.client.consumer.ConsumeContext;
import com.xiaohongshu.events.client.consumer.ConsumeStatus;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * IT推过来的外部邮箱发的邮件消息流
 */
@Slf4j
@Component
public class ExternalInvoiceEnterProcessor implements MessageProcessor {
    @ApolloJsonValue("${support_file_type_list:['pdf']}")
    private List<String> supportFileTypeList;

    @Resource
    private ReimburseRosService rosService;

    @Resource
    private InvoiceService invoiceService;

    @Resource
    private EmailScanInvoiceProducer emailScanInvoiceProducer;

    @Override
    public ConsumeStatus process(MessageExt msg, ConsumeContext consumeContext) {
        // 邮件标题必须包含”发票“，发件人必须是外部邮箱IT才可读取邮件内容
        String msgBody = new String(msg.getBody());
        if (StringUtils.isBlank(msgBody)) {
            log.info("ExternalInvoiceEnterProcessor msg consume msgBody empty mId:{}", msg.getMsgId());
            return ConsumeStatus.SUCCESS;
        }

        //拿到发票文件和用户编号
        List<ExternalInvoiceInfoDto> invoiceInfos = JSONObject.parseArray(msgBody, ExternalInvoiceInfoDto.class);
        if (CollectionUtils.isEmpty(invoiceInfos)) {
            log.info("ExternalInvoiceEnterProcessor msg consume msg empty mId:{}", msg.getMsgId());
            return ConsumeStatus.SUCCESS;
        }

        // 暂时去掉.ofd文件
        filterSupportedFileTypes(invoiceInfos);
        List<String> invoiceUuidList = new ArrayList<>();
        for (ExternalInvoiceInfoDto invoiceInfo : invoiceInfos) {
            if (StrUtil.isBlank(invoiceInfo.getUserId()) || CollectionUtils.isEmpty(invoiceInfo.getFiles())) {
                continue;
            }

            String userId = invoiceInfo.getUserId();
            try {
                //转存文件
                List<FileInfoDto> files = reSaveFile(invoiceInfo.getFiles());
                if (CollectionUtils.isEmpty(files)) {
                    continue;
                }

                //发票OCR 发票验真
                BatchOcrParseResponse response = invoiceService.batchOcrParse(new BatchOcrParseRequest(files, userId), true);

                List<InvoiceDto> invoices = response.getOcrParseSuccessList();
                if (CollectionUtils.isEmpty(invoices)) {
                    continue;
                }

                //发票入库
                invoiceUuidList.addAll(invoiceService.batchSaveInvoices(new BatchSaveInvoiceRequest(invoices, userId, InvoiceSourceEnum.EMAIL.getCode())));
            } catch (Exception e) {
                log.error("ExternalInvoiceEnterProcessor msg consume msg error:{}", e.getMessage(), e);
            }
        }

        // 发送MQ消息
        emailScanInvoiceProducer.send(invoiceUuidList);
        return ConsumeStatus.SUCCESS;
    }

    private void filterSupportedFileTypes(List<ExternalInvoiceInfoDto> invoiceInfos) {
        log.info("filterSupportedFileTypes - invoiceInfos start= {}", JSONObject.toJSONString(invoiceInfos));
        for (ExternalInvoiceInfoDto dto : invoiceInfos) {
            List<FileInfoDto> files = dto.getFiles();
            if (CollectionUtils.isEmpty(files)) {
                continue;
            }
            List<FileInfoDto> newFiles = Lists.newArrayList();
            // 遍历文件
            for (FileInfoDto file : files) {
                String name = file.getName();
                if (StringUtils.isBlank(name)) {
                    continue;
                }
                // files的name截取"."的后缀名
                String suffer = name.substring(name.lastIndexOf(".") + 1);
                if (StringUtils.isBlank(suffer)) {
                    continue;
                }
                // 后缀名包含pdf的文件名包含"pdf"的都需要保留
                // 例如pdf=、pdf==等
                filterGetSupportFile(file, suffer, newFiles);
            }
            // 覆盖式set
            dto.setFiles(newFiles);
        }
        log.info("filterSupportedFileTypes - invoiceInfos end= {}", JSONObject.toJSONString(invoiceInfos));
    }


    private void filterGetSupportFile(FileInfoDto file, String suffer, List<FileInfoDto> newFiles) {
        for (String s : supportFileTypeList) {
            if (suffer.contains(s)){
                specialFilePdfDeal(file);
                newFiles.add(file);
                break;
            }
        }
    }

    /**
     * ros文件转存
     * <br>financeoa中ros的版本冲突无法在financeoa侧直接上传到文件服务器，所以在oaoffice中进行上传。现将文件url转成FileInputStream，然后再进行ros上传
     * </br>
     *
     * @param files 文件
     * @return 完整的文件
     */
    private List<FileInfoDto> reSaveFile(List<FileInfoDto> files) {
        if (CollectionUtils.isEmpty(files)) {
            return null;
        }
        List<FileInfoDto> result = new ArrayList<>();
        for (FileInfoDto file : files) {
            try {
                //获取文件的流
                InputStream fileInputStream = FileUtil.getFileInputStream(file.getUrl());
                //上传到专用的ROS
                FileInfoDto fileInfoDto = rosService.putObject(file.getName(), fileInputStream);
                if (Objects.nonNull(fileInfoDto)) {
                    result.add(fileInfoDto);
                }
            } catch (Exception e) {
                log.error("ExternalInvoiceEnterProcessor msg consume msg error,file resave fail:{}", e.getMessage(), e);
            }
        }

        return result;
    }

    private  void specialFilePdfDeal(FileInfoDto file) {
        String name = file.getName();
        int lastPdfIndex = name.lastIndexOf(".pdf");
        if (lastPdfIndex != -1) {
            String newName = name.substring(0, lastPdfIndex + 4);
            file.setName(newName);
        }
    }

}
