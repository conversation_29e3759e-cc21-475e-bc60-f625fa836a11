package com.xhs.reimburse.mq.consumer;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.xhs.reimburse.modal.dto.travel.TravelApplyChangeDto;
import com.xhs.reimburse.service.TravelApplyExpenseFormRelationService;
import com.xiaohongshu.events.client.MessageExt;
import com.xiaohongshu.events.client.api.MessageProcessor;
import com.xiaohongshu.events.client.consumer.ConsumeContext;
import com.xiaohongshu.events.client.consumer.ConsumeStatus;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class TravelApplyChangeProcessor implements MessageProcessor {

    @Resource
    private TravelApplyExpenseFormRelationService travelApplyExpenseFormRelationService;

    final static String TRAVEL_FORM_CHANGE_TAG = "travel_apply_form_change";

    @Override
    public ConsumeStatus process(MessageExt msg, ConsumeContext consumeContext) {

        String msgBody = new String(msg.getBody());
        //tag不是travel_apply_form_change的消息不处理
        if (StringUtils.isBlank(msgBody) || !TRAVEL_FORM_CHANGE_TAG.equals(msg.getTags())) {
            log.info("TravelApplyChangeProcessor msg consume msgBody empty mId:{}", msg.getMsgId());
            return ConsumeStatus.SUCCESS;
        }

        //拿到发票文件和用户编号
        TravelApplyChangeDto dto = JSONObject.parseObject(msgBody, TravelApplyChangeDto.class);
        if (Objects.isNull(dto)) {
            log.info("TravelApplyChangeProcessor msg consume msg empty mId:{}", msg.getMsgId());
            return ConsumeStatus.SUCCESS;
        }

        try {
            String travelApplyFormNum = dto.getTravelApplyFormNum();
            String preTravelApplyFormNum = dto.getPreTravelApplyFormNum();

            travelApplyExpenseFormRelationService.travelApplyChangeHandle(travelApplyFormNum, preTravelApplyFormNum);
        } catch (Exception e) {

            log.error("TravelApplyChangeProcessor msg consume msg error:{}", e.getMessage(), e);
        }

        return ConsumeStatus.SUCCESS;
    }

}
