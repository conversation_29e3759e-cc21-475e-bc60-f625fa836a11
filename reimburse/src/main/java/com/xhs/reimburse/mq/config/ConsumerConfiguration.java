package com.xhs.reimburse.mq.config;

import com.xhs.reimburse.mq.consumer.RedflowFormAuditProcessor;
import com.xiaohongshu.events.client.consumer.EventsPushConsumer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date :2025/02/25 - 下午9:09
 * @description :
 */
@Slf4j
@Configuration
public class ConsumerConfiguration {
    // 生产者所属topic
    private static final String REDFLOW_FORM_AUDIT_TOPIC = "oasis_form-notice";
    // 消费者所属group
    private static final String REDFLOW_FORM_AUDIT_GROUP = "oasis_form-notice-oaoffice-consumer";
    // 已在event平台指定tags

    @Bean(name = "RedflowFormAuditConsumer")
    public EventsPushConsumer RedflowFormAuditConsumer(RedflowFormAuditProcessor redflowFormAuditProcessor) {
        EventsPushConsumer consumer = new EventsPushConsumer();
        consumer.setTopic(REDFLOW_FORM_AUDIT_TOPIC);
        consumer.setGroup(REDFLOW_FORM_AUDIT_GROUP);
        consumer.setMessageProcessor(redflowFormAuditProcessor);
        consumer.start();
        log.info("------------------ RedflowFormAuditConsumer MQ启动成功！------------------");
        return consumer;
    }
}
