package com.xhs.reimburse.mq.producer;

import com.alibaba.fastjson.JSONObject;
import com.xiaohongshu.events.client.producer.EventsProducer;
import com.xiaohongshu.events.client.producer.SendResult;
import com.xiaohongshu.events.client.producer.SendStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date :2025/02/24 - 上午10:58
 * @description :
 */
@Component
@Slf4j
public class EmailScanInvoiceProducer {

    EventsProducer eventsProducer;

    private static final String topic = "oa_office_reimburse_email_scan_invoice";

    EmailScanInvoiceProducer() {
        eventsProducer = new EventsProducer(topic);
        eventsProducer.start();
    }

    public void send(List<String> invoiceUuidList) {
        try {
            SendResult result = eventsProducer.send(topic, JSONObject.toJSONString(invoiceUuidList));
            if (SendStatus.SUCCESS.equals(result.getSendStatus())) {
                log.info("邮箱扫描发票消息推送成功 - invoiceUuidList = {}", JSONObject.toJSONString(invoiceUuidList));
            } else {
                log.error("邮箱扫描发票消息推送失败 - error = {}", JSONObject.toJSONString(result));
            }
        } catch (Exception e) {
            log.error("邮箱扫描发票系统错误 {}", e.getMessage(), e);
            throw new RuntimeException("邮箱扫描发票系统错误" + e.getMessage());
        }
    }
}
