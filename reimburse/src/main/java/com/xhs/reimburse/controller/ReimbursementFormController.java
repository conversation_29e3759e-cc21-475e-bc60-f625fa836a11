package com.xhs.reimburse.controller;

import com.xhs.finance.framework.resubmit.Resubmit;
import com.xhs.finance.sso.UserInfoBag;
import com.xhs.reimburse.modal.dto.ReimbursementFormSimpleDto;
import com.xhs.reimburse.modal.request.PageQueryReimburseFormRequest;
import com.xhs.reimburse.modal.request.ReimburseFormRequest;
import com.xhs.reimburse.modal.response.ReimbursementFormPrintResponse;
import com.xhs.reimburse.modal.response.ReimbursementFormResponse;
import com.xhs.reimburse.service.ReimburseFormFlowService;
import com.xhs.reimburse.service.ReimbursementFormService;
import com.xiaohongshu.erp.common.framework.page.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/oa-office/reimburse/reimbursement/form")
@Api(value = "/oa-office/reimburse/reimbursement/form", description = "报销单相关接口")
public class ReimbursementFormController {

    @Resource
    private ReimbursementFormService reimbursementFormService;

    @Resource
    private ReimburseFormFlowService reimbursementFormFlowService;

    @PostMapping(value = "/saveOrSubmit")
    @ApiOperation(notes = "/saveOrSubmit", value = "保存或提交报销单")
    public ReimbursementFormResponse addOrUpdate(@RequestBody @Validated ReimburseFormRequest reimburseFormRequest
            , @RequestParam(defaultValue = "0") Integer startUp) {
        //费用页面跳转时可能拿不到userId
        reimburseFormRequest.setCreatorNo(UserInfoBag.get().getUserId());
        return reimbursementFormFlowService.saveOrSubmitReimbursementForm(reimburseFormRequest, startUp == 0);
    }

    @Resubmit
    @PostMapping(value = "/batchSave")
    @ApiOperation(notes = "/batchSave", value = "批量保存报销单")
    public List<ReimbursementFormSimpleDto> batchSaveReimbursementForm(@RequestBody List<String> eIds) {
        return reimbursementFormFlowService.batchSaveReimbursementForm(eIds, UserInfoBag.get().getUserId());
    }

    @GetMapping(value = "/query")
    @ApiOperation(notes = "/query", value = "查询报销单单据详情")
    public ReimbursementFormResponse queryReimburseFormDetail(@RequestParam(value = "formNum") String formNum) {
        return reimbursementFormFlowService.queryReimburseFormDetail(formNum);
    }

    @GetMapping(value = "/queryWithDeleted")
    @ApiOperation(notes = "/queryWithDeleted", value = "查询报销单单据详情")
    public ReimbursementFormResponse queryReimburseFormDetailWithDeleted(@RequestParam(value = "formNum") String formNum) {
        return reimbursementFormFlowService.queryReimburseFormDetail(formNum, true);
    }

    @GetMapping(value = "/print")
    @ApiOperation(notes = "/print", value = "填充打印单据的表单")
    public ReimbursementFormPrintResponse queryReimburseFormDetail4Print(@RequestParam(value = "formNum") String formNum) {
        return reimbursementFormFlowService.queryReimburseFormDetail4Print(formNum);
    }

    @GetMapping(value = "/delete")
    @ApiOperation(notes = "/delete", value = "待处理删除单据")
    public void deleteReimburseForm(@RequestParam(value = "formNum") String formNum) {
        reimbursementFormFlowService.deleteReimburseForm(formNum);
    }

    @PostMapping(value = "/pageQuery")
    @ApiOperation(notes = "/pageQuery", value = "分页查询报销单")
    public PageResult<ReimbursementFormSimpleDto> pageQueryReimburseFormDetail(@RequestBody PageQueryReimburseFormRequest request) {
        return reimbursementFormService.pageQuery(request);
    }

}
