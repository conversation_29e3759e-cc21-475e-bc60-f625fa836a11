package com.xhs.reimburse.controller;

import com.xhs.cache.RedisClient;
import com.xhs.reimburse.modal.dto.DynamicFormFieldDto;
import com.xhs.reimburse.modal.dto.ExpenseDto;
import com.xhs.reimburse.modal.dto.ReimbursementFormDto;
import com.xhs.reimburse.modal.dto.travel.SeatContentDto;
import com.xhs.reimburse.modal.request.*;
import com.xhs.reimburse.modal.request.travel.ExpenseBudgetCheckRequest;
import com.xhs.reimburse.modal.request.travel.SeatIsOverBudgetRequest;
import com.xhs.reimburse.modal.response.LabelValueExtendResponse;
import com.xhs.reimburse.modal.response.LabelValueResponse;
import com.xhs.reimburse.service.CheckOverBudgetService;
import com.xhs.reimburse.service.ExpenseBusinessService;
import com.xhs.reimburse.service.ExpenseCityService;
import com.xhs.reimburse.service.impl.ExpenseServiceImpl;
import com.xhs.reimburse.xhsoa.mapper.TravelPlaceMapper;
import com.xhs.reimburse.xhsoa.service.TravelService;
import com.xiaohongshu.erp.common.framework.page.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ExpenseController.java
 * @createTime 2025年02月13日 20:39:00
 */
@Api(value = "/oa-office/reimburse/expense", description = "费用相关接口")
@RestController
@RequestMapping("/oa-office/reimburse/expense")
public class ExpenseController {

    @Resource
    private ExpenseServiceImpl expenseService;
    @Resource
    private ExpenseCityService expenseCityService;
    @Resource
    private TravelPlaceMapper travelPlaceMapper;
    @Resource
    private TravelService travelService;
    @Resource
    private CheckOverBudgetService checkOverBudgetService;
    @Resource
    private ExpenseBusinessService expenseBusinessService;
    @Resource
    private RedisClient redisClient;

    @ApiOperation(value = "分页查询费用", notes = "/pageQueryExpense")
    @PostMapping(value = "/pageQueryExpense")
    public PageResult<ExpenseDto> pageQueryExpense(@RequestBody @Valid PageQueryExpenseRequest request) {
        return expenseService.pageQueryExpense(request);
    }

    @ApiOperation(value = "保存费用", notes = "/saveExpense")
    @PostMapping(value = "/saveExpense")
    public ExpenseDto saveExpense(@RequestBody @Valid ExpenseDto expenseDto) {
        String uuId = expenseBusinessService.saveExpenseCheckAuth(expenseDto);
        return expenseService.getExpenseDtoByUuid(uuId);
    }

    @ApiOperation(value = "更新费用", notes = "/updateExpense")
    @PostMapping(value = "/updateExpense")
    public void updateExpense(@RequestBody @Valid ExpenseDto expenseDto) {
        expenseBusinessService.updateExpenseCheckAuth(expenseDto);
    }

    @ApiOperation(value = "查询费用详情", notes = "/getExpenseByUuId")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuId", value = "费用ID", dataType = "String", required = true)
    })
    @GetMapping(value = "/getExpenseByUuId")
    public ExpenseDto getExpenseByUuId(@RequestParam(value = "uuId", required = true) String uuId) {
        return expenseBusinessService.getExpenseDtoByUuidCheckAuth(uuId);
    }

    @ApiOperation(value = "查询费用详情（包含已经删除的）", notes = "/getExpenseByUuIdWithDeleted")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuId", value = "费用ID", dataType = "String", required = true)
    })
    @GetMapping(value = "/getExpenseByUuIdWithDeleted")
    public ExpenseDto getExpenseByUuIdWithDeleted(@RequestParam(value = "uuId", required = true) String uuId) {
        return expenseBusinessService.getExpenseByUuIdWithDeletedCheckAuth(uuId);
    }

    @ApiOperation(value = "删除费用", notes = "/deleteExpense")
    @PostMapping(value = "/deleteExpense")
    public void deleteExpense(@RequestBody @Valid ExpenseDeleteRequest request) {
        expenseBusinessService.logicDeleteExpenseCheckAuth(request.getUuid());
    }

    @ApiOperation(value = "获取费用动态表单字段", notes = "/getExpenseDynamicFormFields")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "formType", value = "表单类型", dataType = "String", required = true),
            @ApiImplicitParam(name = "firstSubjectCode", value = "一级科目", dataType = "String", required = true),
            @ApiImplicitParam(name = "secondSubjectCode", value = "二级科目", dataType = "String", required = true)
    })
    @GetMapping(value = "/getExpenseDynamicFormFields")
    public List<DynamicFormFieldDto> getExpenseDynamicFormFields(@RequestParam(value = "formType", required = true) String formType,
                                                                 @RequestParam(value = "firstSubjectCode", required = true) String firstSubjectCode,
                                                                 @RequestParam(value = "secondSubjectCode", required = false) String secondSubjectCode) {
        return expenseService.getExpenseDynamicFormFields(formType, firstSubjectCode, secondSubjectCode);
    }

    @ApiOperation(value = "查询费用单据类型", notes = "/getExpenseFormType")
    @GetMapping(value = "/getExpenseFormType")
    public List<LabelValueResponse> getExpenseFormType() {
        return expenseService.getExpenseFormType();
    }

    @ApiOperation(value = "查询费用科目类型", notes = "/getExpenseSubject")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "formType", value = "表单类型", dataType = "String", required = true)
    })
    @GetMapping(value = "/getExpenseSubject")
    public List<LabelValueExtendResponse> getExpenseSubject(@RequestParam(value = "formType", required = true) String formType) {
        return expenseService.getExpenseSubject(formType);
    }

    @ApiOperation(value = "费用允许关联的发票类型", notes = "/getAllowedInvoiceTypes")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "formType", value = "表单类型", dataType = "String", required = true),
            @ApiImplicitParam(name = "firstSubjectCode", value = "一级科目", dataType = "String", required = true),
            @ApiImplicitParam(name = "secondSubjectCode", value = "二级科目", dataType = "String", required = true)
    })
    @GetMapping("/getAllowedInvoiceTypes")
    public List<String> getAllowedInvoiceTypes(@RequestParam(value = "formType", required = false) String formType, @RequestParam(value = "firstSubjectCode", required = false) String firstSubjectCode, @RequestParam(value = "secondSubjectCode", required = false) String secondSubjectCode) {
        return expenseService.getAllowedInvoiceTypes(formType, firstSubjectCode, secondSubjectCode);
    }

    @ApiOperation(value = "查询城市", notes = "/selectCity")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "key", value = "查询内容", dataType = "String", required = true)
    })
    @GetMapping("/selectCity")
    public List<String> selectCity(String key) {
        return travelPlaceMapper.searchPlaceByPlaceName(key);
    }

    @GetMapping("/getWaterLevelByCityName")
    @ApiOperation(notes = "/getWaterLevelByCityName", value = "根据城市名称获取水位线")
    public BigDecimal getWaterLevelByCityName(@RequestParam String cityName) {
        return expenseCityService.getWaterLevelByCityName(cityName);
    }

    @PostMapping("/queryExpenseBelongReimbursementForm")
    @ApiOperation(notes = "/queryExpenseBelongReimbursementForm", value = "费用对应报销单")
    public Map<String, ReimbursementFormDto> queryExpenseBelongReimbursementForm(@RequestBody ExpenseBelongReimbursementFormRequest request) {
        List<String> expenseUuidList = request.getExpenseUuidList();
        return expenseBusinessService.batchQueryExpenseBelongReimbursementFormCheckAuth(expenseUuidList);
    }

    @ApiOperation(value = "查询火车/飞机座位类型", notes = "/getSeats")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "seatType", value = "座位类型 火车:train 飞机: plane ", dataType = "String", required = true)
    })
    @GetMapping("/getSeats")
    public List<LabelValueResponse> getSeats(String seatType) {
        return expenseService.getSeats(seatType);
    }

    @ApiOperation(value = "查询差旅工作餐超标原因类型", notes = "/getTravelMealOverLimitReasonTypes")
    @GetMapping("/getTravelMealOverLimitReasonTypes")
    public List<LabelValueResponse> getTravelMealOverLimitReasonTypes() {
        return expenseService.getTravelMealOverLimitReasonTypes();
    }

    @ApiOperation(value = "查询差旅工作餐餐补标准", notes = "/queryTravelMealLimit")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户userId", dataType = "String", required = true)
    })
    @GetMapping("/queryTravelMealLimit")
    public BigDecimal queryTravelMealLimit(@RequestParam(value = "userId", required = false) String userId) {
        return expenseService.queryTravelMealLimit(userId);
    }

    @ApiOperation(value = "查询出差城市", notes = "/queryTravelCity")
    @PostMapping(value = "/queryTravelCity")
    public List<Map<String, String>> queryTravelCity(@RequestBody @Valid QueryTravelCityRequest searchTravelCityRequest) {
        return travelService.queryCityInfoByFormNums(searchTravelCityRequest);
    }

    @ApiOperation(value = "火车票/飞机票是否超标", notes = "/seatIsOverBudget")
    @PostMapping(value = "/seatIsOverBudget")
    public List<SeatContentDto> seatIsOverBudget(@RequestBody @Valid SeatIsOverBudgetRequest request) {
        return checkOverBudgetService.seatIsOverBudget(request);
    }

    @ApiOperation(value = "差旅工作餐是否超标", notes = "/businessMealIsOverBudget")
    @PostMapping(value = "/businessMealIsOverBudget")
    public Boolean businessMealIsOverBudget(@RequestBody @Valid ExpenseBudgetCheckRequest request) {
        return checkOverBudgetService.checkBusinessMealOverBudget(request);
    }

    @ApiOperation(value = "绑定预分类发票与费用类型", notes = "/bindInvoiceExpenseType")
    @PostMapping(value = "/bindInvoiceExpenseType")
    public void bindInvoiceExpenseType(@RequestBody @Valid InvoiceExpenseBindRequest request) {
        String redisKey = String.format("invoice_expense_bind:%s:%s", request.getSessionId(), request.getSceneId());
        // 保存到Redis，过期时间2小时（7200秒）
        redisClient.set(redisKey, request.getInvoiceExpenseBindList(), 7200);
    }

    @ApiOperation(value = "获取预分类发票与费用类型绑定信息", notes = "/getInvoiceExpenseBindInfo")
    @GetMapping(value = "/getInvoiceExpenseBindInfo")
    public List<InvoiceExpenseBindRequest.InvoiceExpenseBind> getInvoiceExpenseBindInfo(String sessionId, String sceneId) {
        String redisKey = String.format("invoice_expense_bind:%s:%s", sessionId, sceneId);
        Object result = redisClient.get(redisKey);
        if (result == null) {
            return new ArrayList<>();
        }
        return (List<InvoiceExpenseBindRequest.InvoiceExpenseBind>) result;
    }

}