package com.xhs.reimburse.controller;

import com.xhs.finance.sso.UserInfoBag;
import com.xhs.reimburse.modal.dto.BankAccountDto;
import com.xhs.reimburse.modal.request.BankAccountRequest;
import com.xhs.reimburse.service.BankAccountService;
import com.xiaohongshu.erp.common.framework.resubmit.v2.ResubmitV2;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date :2025/02/07 - 下午8:41
 * @description :
 */
@Slf4j
@RestController
@RequestMapping("/oa-office/reimburse/reimbursement/bankAccount")
public class BankAccountController {

    @Resource
    private BankAccountService bankAccountService;

    @GetMapping(value = "/getBankAccounts")
    @ApiOperation(notes = "/getBankAccounts", value = "获得当前用户报销银行账户列表")
    public List<BankAccountDto> getBankAccounts() {
        String userId = UserInfoBag.get().getUserId();
        return bankAccountService.getBankAccountList(userId);
    }

    @PostMapping(value = "/addOrUpdate")
    @ApiOperation(notes = "/addOrUpdate", value = "新增或编辑当前银行账户信息")
    @ResubmitV2(spaceTimeSecond = 1)
    public void addOrUpdate(@RequestBody @Validated BankAccountRequest request) {
        bankAccountService.addOrUpdate(request);
    }
}
