package com.xhs.reimburse.controller;

import com.xhs.oa.office.exception.BusinessException;
import com.xhs.reimburse.modal.request.PageQueryTravelApplyRequest;
import com.xhs.reimburse.modal.response.TravelApplySummaryInfo;
import com.xhs.reimburse.service.TravelApplyService;
import com.xiaohongshu.erp.common.framework.page.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/oa-office/reimburse/travelApply")
@Api(value = "/oa-office/travelApply", description = "差旅申请单相关")
public class TravelApplyController
{

    @Autowired
    private TravelApplyService travelApplyService;

    @PostMapping(value = "/pageQuery")
    @ApiOperation(notes = "/pageQuery", value = "分页查询差旅申请单卡片信息")
    public PageResult<TravelApplySummaryInfo> pageQueryTravelApplyFormSummary(@RequestBody PageQueryTravelApplyRequest request)
    {
        try
        {
            return travelApplyService.pageQueryTravelApplyFormSummary(request);
        } catch (Exception e)
        {
            log.error("分页查询差旅申请单信息失败,req :{},errorMsg ", request, e);
            throw new BusinessException("查询差旅申请单信息失败");
        }
    }

    @PostMapping(value = "/query")
    @ApiOperation(notes = "/query", value = "查询差旅申请单卡片信息")
    public List<TravelApplySummaryInfo> queryTravelApplyFormSummary(@RequestBody PageQueryTravelApplyRequest request)
    {
        try
        {
            return travelApplyService.queryTravelApplyFormSummary(request);
        } catch (Exception e)
        {
            log.error("查询差旅申请单信息失败,req :{},errorMsg ", request, e);
            throw new BusinessException("查询差旅申请单信息失败");
        }
    }
}
