package com.xhs.reimburse.controller;

import com.xhs.reimburse.modal.dto.DynamicFormFieldDto;
import com.xhs.reimburse.modal.dto.InvoiceDto;
import com.xhs.reimburse.modal.request.*;
import com.xhs.reimburse.modal.response.BatchOcrParseResponse;
import com.xhs.reimburse.modal.response.LabelValueResponse;
import com.xhs.reimburse.service.InvoiceBusinessService;
import com.xhs.reimburse.service.impl.InvoiceServiceImpl;
import com.xiaohongshu.erp.common.framework.page.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName InvoiceController.java
 * @createTime 2025年02月11日 17:48:00
 */
@Slf4j
@RestController
@RequestMapping("/oa-office/reimburse/invoice")
@Api(value = "/oa-office/reimburse/invoice", description = "发票相关接口")
public class InvoiceController {

    @Resource
    private InvoiceServiceImpl invoiceService;
    @Resource
    private InvoiceBusinessService invoiceBusinessService;

    @ApiOperation(value = "分页查询", notes = "/pageQueryInvoices")
    @PostMapping(value = "/pageQueryInvoices")
    public PageResult<InvoiceDto> pageQueryInvoices(@RequestBody @Valid PageQueryInvoiceRequest request) {
        return invoiceService.pageQueryInvoice(request);
    }

    @ApiOperation(value = "ocr批量识别", notes = "/batchOcrParse")
    @PostMapping(value = "/batchOcrParse")
    public BatchOcrParseResponse batchOcrParse(@RequestBody @Valid BatchOcrParseRequest request) {
        return invoiceService.batchOcrParse(request, true);
    }

    @ApiOperation(value = "批量保存发票", notes = "/batchSaveInvoices")
    @PostMapping(value = "/batchSaveInvoices")
    public List<InvoiceDto> batchSaveInvoices(@RequestBody @Valid SaveInvoiceRequest request) {
        BatchSaveInvoiceRequest batchRequest = new BatchSaveInvoiceRequest();
        batchRequest.setInvoiceDtoList(request.getInvoiceDtoList());
        List<String> uuIdList = invoiceService.batchSaveInvoices(batchRequest);
        return invoiceService.queryInvoice(uuIdList);
    }

    @ApiOperation(value = "查询发票详情", notes = "/getInvoiceById")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuId", value = "发票ID", dataType = "String", required = true)
    })
    @GetMapping(value = "/getInvoiceById")
    public InvoiceDto getInvoiceById(@RequestParam(value = "uuId", required = true) String uuId,  @RequestParam(value = "needExistMsg", required = false, defaultValue = "false") boolean needExistMsg) {
        return invoiceBusinessService.getInvoiceDtoByUuidCheckAuth(uuId, needExistMsg);
    }

    @ApiOperation(value = "查询发票详情", notes = "/getInvoiceForBotByUuid")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuId", value = "发票ID", dataType = "String", required = true)
    })
    @GetMapping(value = "/getInvoiceForBotByUuid")
    public InvoiceDto getInvoiceForBotByUuid(@RequestParam(value = "uuId", required = true) String uuId,  @RequestParam(value = "needExistMsg", required = false, defaultValue = "false") boolean needExistMsg) {
        return invoiceBusinessService.getInvoiceForBotByUuidCheckAuth(uuId, needExistMsg);
    }

    @ApiOperation(value = "查询发票详情（包含已经删除的）-本人调用", notes = "/getInvoiceByIdWithDeleted")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuId", value = "发票ID", dataType = "String", required = true)
    })
    @GetMapping(value = "/getInvoiceByIdWithDeleted")
    public InvoiceDto getInvoiceByIdWithDeleted(@RequestParam(value = "uuId", required = true) String uuId) {
        return invoiceBusinessService.getInvoiceByIdWithDeleted(uuId);
    }

    @ApiOperation(value = "更新发票信息", notes = "/updateInvoice")
    @PostMapping(value = "/updateInvoice")
    public void updateInvoice(@RequestBody @Valid InvoiceDto invoiceDto) {
        invoiceService.updateInvoice(invoiceDto);
    }

    @ApiOperation(value = "删除票据", notes = "/deleteInvoice")
    @PostMapping(value = "/deleteInvoice")
    public void deleteInvoice(@RequestBody @Valid InvoiceDeleteRequest request) {
        invoiceService.logicDeleteInvoice(request.getUuId());
    }

    @ApiOperation(value = "获取发票动态表单", notes = "/getInvoiceDynamicFormFields")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "invoiceType", value = "发票类型", dataType = "String", required = true)
    })
    @GetMapping(value = "/getInvoiceDynamicFormFields")
    public List<DynamicFormFieldDto> getInvoiceDynamicFormFields(@RequestParam(value = "ticketType", required = true) String ticketType) {
        return invoiceService.getInvoiceDynamicFormFields(ticketType);
    }

    @ApiOperation(value = "获取发票类型枚举", notes = "/getInvoiceTypes")
    @GetMapping(value = "/getInvoiceTypes")
    public List<LabelValueResponse> getInvoiceTypes() {
        return invoiceService.getInvoiceTypes();
    }
}