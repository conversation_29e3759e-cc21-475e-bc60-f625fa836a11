package com.xhs.reimburse.controller;

import com.xhs.reimburse.modal.response.UnclaimedCountResponse;
import com.xhs.reimburse.service.ReimbursementFormService;
import com.xhs.reimburse.service.impl.ExpenseServiceImpl;
import com.xhs.reimburse.service.impl.InvoiceServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName HomepageController.java
 * @createTime 2025年02月11日 17:33:00
 */
@Slf4j
@RestController
@RequestMapping("/oa-office/reimburse/homepage")
@Api(value = "/oa-office/reimburse/homepage", description = "票夹首页")
public class HomepageController {

    @Resource
    private InvoiceServiceImpl invoiceService;
    @Resource
    private ExpenseServiceImpl expenseService;
    @Resource
    private ReimbursementFormService reimbursementFormService;

    @ApiOperation(value = "查询首页三个Tab中未报销的红点气泡数量", notes = "返回发票夹、费用夹、报销单中未报销的数量")
    @GetMapping(value = "/getUnclaimedCount")
    public UnclaimedCountResponse getUnclaimedCount() {
        UnclaimedCountResponse response = new UnclaimedCountResponse();
        response.setInvoiceFolderUnclaimedCount(invoiceService.getPendingCount());
        response.setExpenseFolderUnclaimedCount(expenseService.getPendingCount());
        response.setReimbursementFolderUnclaimedCount(reimbursementFormService.getPendingCount());
        return response;
    }
}
