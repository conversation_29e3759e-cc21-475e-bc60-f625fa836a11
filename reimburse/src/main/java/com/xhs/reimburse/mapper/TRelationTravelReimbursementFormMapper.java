package com.xhs.reimburse.mapper;

import java.util.List;

import com.xhs.reimburse.modal.entity.travel.RelationTravelReimbursementForm;
import com.xhs.reimburse.modal.entity.travel.RelationTravelReimbursementFormExample;
import org.apache.ibatis.annotations.Param;

public interface TRelationTravelReimbursementFormMapper {
    /**
     * @mbg.generated generated automatically, do not modify!
     */
    long countByExample(RelationTravelReimbursementFormExample example);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int deleteByExample(RelationTravelReimbursementFormExample example);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int deleteByPrimaryKey(Long id);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int insert(RelationTravelReimbursementForm row);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int insertSelective(RelationTravelReimbursementForm row);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    List<RelationTravelReimbursementForm> selectByExample(RelationTravelReimbursementFormExample example);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    RelationTravelReimbursementForm selectByPrimaryKey(Long id);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int updateByExampleSelective(@Param("row") RelationTravelReimbursementForm row, @Param("example") RelationTravelReimbursementFormExample example);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int updateByExample(@Param("row") RelationTravelReimbursementForm row, @Param("example") RelationTravelReimbursementFormExample example);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int updateByPrimaryKeySelective(RelationTravelReimbursementForm row);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int updateByPrimaryKey(RelationTravelReimbursementForm row);

    void batchInsert(@Param("list") List<RelationTravelReimbursementForm> relationTravelReimbursementForms);

    List<String> selectTravelApplyNumsByFId(String formUuid);

    int batchInvalid(@Param("ids") List<Long> ids);
}