package com.xhs.reimburse.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xhs.reimburse.modal.entity.RelationExpenseInvoiceEntity;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName RelationExpenseInvoiceMapper.java
 * @createTime 2025年02月12日 18:21:00
 */
@Repository
public interface RelationExpenseInvoiceMapper extends BaseMapper<RelationExpenseInvoiceEntity> {
    void batchInsert(List<RelationExpenseInvoiceEntity> list);

    List<String> selectInvoiceIdsByEIds(List<String> eIds);
}
