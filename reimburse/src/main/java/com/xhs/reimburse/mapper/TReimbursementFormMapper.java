package com.xhs.reimburse.mapper;

import java.util.List;

import com.xhs.reimburse.modal.entity.ReimbursementFormEntity;
import com.xhs.reimburse.modal.entity.ReimbursementFormExample;
import com.xhs.reimburse.modal.request.PageQueryReimburseFormRequest;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
public interface TReimbursementFormMapper {
    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int deleteByPrimaryKey(Long id);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int insert(ReimbursementFormEntity row);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int insertSelective(ReimbursementFormEntity row);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    List<ReimbursementFormEntity> selectByExampleWithBLOBs(ReimbursementFormExample example);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    List<ReimbursementFormEntity> selectByExample(ReimbursementFormExample example);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    ReimbursementFormEntity selectByPrimaryKey(Long id);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int updateByExampleSelective(@Param("row") ReimbursementFormEntity row, @Param("example") ReimbursementFormExample example);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int updateByExampleWithBLOBs(@Param("row") ReimbursementFormEntity row, @Param("example") ReimbursementFormExample example);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int updateByExample(@Param("row") ReimbursementFormEntity row, @Param("example") ReimbursementFormExample example);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int updateByUuidSelective(ReimbursementFormEntity row);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int updateByPrimaryKeyWithBLOBs(ReimbursementFormEntity row);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int updateByPrimaryKey(ReimbursementFormEntity row);

    List<ReimbursementFormEntity> pageQueryReimbursementForm(PageQueryReimburseFormRequest request);

    int invalidReimbursementFormByFormNum(String formNum);

    int selectPendingCount(@Param("userId") String userId, @Param("pendingStatus") List<Integer> pendingStatusList);

    int invalidReimbursementFormByUuid(String uuid);
}