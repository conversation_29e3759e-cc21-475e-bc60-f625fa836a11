package com.xhs.reimburse.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xhs.reimburse.modal.entity.BankAccountEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date :2025/02/07 - 下午8:42
 * @description :
 */
@Mapper
public interface BankAccountMapper extends BaseMapper<BankAccountEntity> {

    List<BankAccountEntity> getUserBankAccountList(String userId);

    int addBankAccount(BankAccountEntity bankAccountEntity);

    //id = #{id,jdbcType=BIGINT} and user_id = #{userId,jdbcType=VARCHAR}
    int updateBankAccountById(BankAccountEntity bankAccountEntity);
}