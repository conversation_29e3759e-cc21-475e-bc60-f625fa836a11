package com.xhs.reimburse.mapper;

import java.util.List;

import com.xhs.reimburse.modal.entity.travel.TravelStandard;
import com.xhs.reimburse.modal.entity.travel.TravelStandardExample;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
public interface TravelStandardMapper {
    /**
     * @mbg.generated generated automatically, do not modify!
     */
    long countByExample(TravelStandardExample example);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int deleteByExample(TravelStandardExample example);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int deleteByPrimaryKey(Long id);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int insert(TravelStandard row);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int insertSelective(TravelStandard row);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    List<TravelStandard> selectByExample(TravelStandardExample example);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    TravelStandard selectByPrimaryKey(Long id);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int updateByExampleSelective(@Param("row") TravelStandard row, @Param("example") TravelStandardExample example);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int updateByExample(@Param("row") TravelStandard row, @Param("example") TravelStandardExample example);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int updateByPrimaryKeySelective(TravelStandard row);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int updateByPrimaryKey(TravelStandard row);
}