package com.xhs.reimburse.xhsoa.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TravelUserDto.java
 * @createTime 2025年03月13日 16:19:00
 */
@Data
public class TravelUserDto {
    @ApiModelProperty("员工编号，非员工代订为空")
    private String userId;

    @ApiModelProperty("出差人姓名")
    private String userName;

    @ApiModelProperty("工作地点")
    private String workingPlace;
}
