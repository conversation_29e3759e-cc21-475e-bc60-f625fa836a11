package com.xhs.reimburse.xhsoa.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TravelRouteDto.java
 * @createTime 2025年03月13日 16:16:00
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TravelRouteDto {
    @ApiModelProperty(value = "差旅行程类型code")
    private String travelRouteCode;

    @ApiModelProperty(value = "差旅行程类型name")
    private String travelRouteName;
}
