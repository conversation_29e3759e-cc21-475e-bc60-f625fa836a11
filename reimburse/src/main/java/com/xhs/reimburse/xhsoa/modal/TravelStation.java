package com.xhs.reimburse.xhsoa.modal;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TravelStation.java
 * @createTime 2025年03月03日 21:27:00
 */
@Data
public class TravelStation implements Serializable {

    private Long id;

    private String type;

    private String placeCode;

    private String stationId;

    private String stationName;

    private String stationEnName;

    private String countryName;

    private String countryId;

    /**
     * 是否有效，0：无效 1：有效
     */
    private Integer isValid;

    /**
     * 创建者id
     */
    private String creatorNo;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者id
     */
    private String updaterNo;

    /**
     * 更新时间
     */
    private Date updateTime;
}

