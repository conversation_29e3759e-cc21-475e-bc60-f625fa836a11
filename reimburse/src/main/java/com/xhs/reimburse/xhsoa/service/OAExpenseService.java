package com.xhs.reimburse.xhsoa.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.xhs.reimburse.xhsoa.mapper.CommonFormMapper;
import com.xhs.reimburse.xhsoa.mapper.TExpenseDetailMapper;
import com.xhs.reimburse.xhsoa.modal.CommonForm;
import com.xhs.reimburse.xhsoa.modal.TExpenseDetail;
import com.xiaohongshu.fls.rpc.oa.office.reimburse.entity.TravelFromInfo;
import com.xiaohongshu.fls.rpc.oa.office.reimburse.request.TravelFormQueryParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.ParseException;
import java.util.*;

@Service
@Slf4j
public class OAExpenseService {

    @Autowired
    private CommonFormMapper commonFormMapper;
    @Autowired
    private TExpenseDetailMapper tExpenseDetailMapper;

    // 查询业务招待报销单相关信息
    public void getMealBXDInfo(TravelFormQueryParam request, List<TravelFromInfo> travelFormInfos) {
        // 查询common_form表
        List<CommonForm> commonForms = queryCommonForms(request);
        log.info("[getMealBXDInfo] request:{}, response size:{}", JSON.toJSONString(request), commonForms.size());
        if (CollectionUtils.isEmpty(commonForms)) {
            return;
        }
        for (CommonForm form : commonForms) {
            log.info("[getMealBXDInfo] isMealExpenseForm, formNum:{}", form.getFormNum());
            if (isMealExpenseForm(form)) {
                TravelFromInfo info = new TravelFromInfo();
                info.setFormNum(form.getFormNum());
                travelFormInfos.add(info);
            }
        }
    }

    // 是否是业务招待
    private boolean isMealExpenseForm(CommonForm form) {
        String formType = form.getFormType();
        // 处理YBFYBXD类型：解析form_content中的expenseSubject
        if ("YBFYBXD".equals(formType)) {
            return isYBFYBXDMealExpense(form);
        }
        // 处理YBFY类型：查询关联费用的second_subject
        if ("YBFY".equals(formType)) {
            return isYBFYMealExpense(form);
        }
        return false;
    }

    private boolean isYBFYBXDMealExpense(CommonForm form) {
        if (StringUtils.isEmpty(form.getFormContent())) {
            return false;
        }
        try {
            JSONObject formContent = JSONObject.parseObject(form.getFormContent());
            JSONObject data = formContent.getJSONObject("data");
            if (data == null) {
                return false;
            }
            String expenseSubject = data.getString("expenseSubject");
            return "cy".equals(expenseSubject) || "qt".equals(expenseSubject);
        } catch (Exception e) {
            log.warn("解析YBFYBXD单据内容失败, formNum: {}", form.getFormNum(), e);
            return false;
        }
    }

    private boolean isYBFYMealExpense(CommonForm form) {
        try {
            // 直接根据form_num查询t_expense_detail表
            List<String> targetSubjects = Arrays.asList("cy", "qt");
            List<TExpenseDetail> expenseDetails = tExpenseDetailMapper.queryByFormNumAndSecondSubjects(form.getFormNum(), targetSubjects);

            return !CollectionUtils.isEmpty(expenseDetails);
        } catch (Exception e) {
            log.warn("查询YBFY单据费用明细失败, formNum: {}", form.getFormNum(), e);
            return false;
        }
    }

    private List<CommonForm> queryCommonForms(TravelFormQueryParam request) {
        Map<String, Object> params = new HashMap<>();
        params.put("creatorNo", request.getUserId());
        if (!CollectionUtils.isEmpty(request.getFromType())) {
            params.put("formType", request.getFromType());
        }
        if (request.getStartDateTime() != null) {
            params.put("startTime", request.getStartDateTime());
        }
        if (request.getEndDateTime() != null) {
            // 如果endTime是00:00:00，调整为当天的23:59:59
            String endTime = adjustEndTimeToEndOfDay(request.getEndDateTime());
            params.put("endTime", endTime);
        }
        return commonFormMapper.queryFormsByParam(params);
    }

    /**
     * 调整结束时间到当天的最后一分钟
     * 如果时间是00:00:00，则调整为23:59:59
     */
    private String adjustEndTimeToEndOfDay(String endDateTime) {
        if (StringUtils.isEmpty(endDateTime)) return endDateTime;
        try {
            Date date = DateUtils.parseDate(endDateTime, "yyyy-MM-dd");
            return DateUtils.addDays(date, 1).toString();
        } catch (ParseException e) {
            log.error("解析结束时间失败, endDateTime: {}", endDateTime, e);
            throw new RuntimeException(e);
        }
    }
}
