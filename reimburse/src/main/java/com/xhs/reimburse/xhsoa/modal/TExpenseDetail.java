package com.xhs.reimburse.xhsoa.modal;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * t_expense_detail 费用明细表实体
 */
@Data
public class TExpenseDetail implements Serializable {

    /**
     * 自增id
     */
    private Long id;

    /**
     * 单据类型
     */
    private String formType;

    /**
     * 单据号
     */
    private String formNum;

    /**
     * 一级科目
     */
    private String firstSubject;

    /**
     * 二级科目
     */
    private String secondSubject;

    /**
     * 日期
     */
    private String dateRange;

    /**
     * 费用详情
     */
    private String expenseAbstract;

    /**
     * 费用内容
     */
    private String expenseContent;

    /**
     * 报销金额
     */
    private BigDecimal amount;

    /**
     * 是否分摊费用
     */
    private Integer isSharingCost;

    /**
     * 是否超预算
     */
    private Integer isOverBudget;

    /**
     * 提报状态
     */
    private String submitStatus;

    /**
     * 是否从我的费用删除
     */
    private Integer isRemoved;

    /**
     * 票据批次号
     */
    private String batchId;

    /**
     * 创建人id
     */
    private String creatorNo;

    /**
     * 创建人名称
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人id
     */
    private String updatorNo;

    /**
     * 更新人名称
     */
    private String updator;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 父id
     */
    private Long parentId;

    /**
     * 拆分单号
     */
    private String splitFormNum;

    /**
     * 票据类型：0电子1纸质,空表示历史数据或者混合
     */
    private Integer ticketType;
}
