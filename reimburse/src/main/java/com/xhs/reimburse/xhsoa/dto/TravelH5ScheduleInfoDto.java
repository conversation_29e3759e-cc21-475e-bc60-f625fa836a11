package com.xhs.reimburse.xhsoa.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.joda.time.DateTime;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TravelH5ScheduleInfoDto.java
 * @createTime 2025年03月13日 16:27:00
 */
@Data
public class TravelH5ScheduleInfoDto {
    @ApiModelProperty(value = "行程编号", hidden = true)
    private Integer scheduleNum;

    @ApiModelProperty("行程类型，住宿：hotel，交通：traffic")
    private String travelRouteType;

    @ApiModelProperty("行程类型名称，住宿：hotel，交通：traffic")
    private String travelRouteTypeName;

    @ApiModelProperty("交通工具/住宿")
    private List<TravelRouteDto> travelRouteVoList;

    @ApiModelProperty("出发地点")
    private List<TravelRoutePlaceDto> depPlaceRouteVoList;

    @ApiModelProperty("到达地点")
    private List<TravelRoutePlaceDto> arrPlaceRouteVoList;

    @ApiModelProperty("出差类别code")
    private String travelTypeCode;

    @ApiModelProperty("单程/往返 true：单程 false：往返")
    private Boolean oneWay = false;

    /**
     * ---------原有字段-----------
     */
    @ApiModelProperty("行程起始时间，格式yyyy-MM-dd")
    private String startDate;

    @ApiModelProperty("行程结束时间，格式yyyy-MM-dd")
    private String endDate;

    @ApiModelProperty(value = "行程起始时间", hidden = true)
    private DateTime startDateTime;

    @ApiModelProperty(value = "行程结束时间", hidden = true)
    private DateTime endDateTime;

    @ApiModelProperty("时间展示信息")
    private String showTimePeriod;

    @ApiModelProperty("行程天数")
    private Integer stayPeriod;

    /**
     * ---------住宿类字段-----------
     */
    @ApiModelProperty("是否两人同住")
    private Boolean hasRoommate;

    @ApiModelProperty("同住人信息")
    private List<TravelUserDto> roommateVoList;

    private String exportRoommateVo;

    @ApiModelProperty("差标类型")
    private String rankStandard;

    @ApiModelProperty("住宿标准")
    private BigDecimal rankStandardAmount;
}
