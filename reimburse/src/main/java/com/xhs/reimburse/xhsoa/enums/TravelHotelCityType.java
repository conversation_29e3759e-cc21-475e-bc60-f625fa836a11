package com.xhs.reimburse.xhsoa.enums;

import com.xhs.oa.office.utils.AssertHelper;
import com.xhs.reimburse.enums.ExpenseSecondSubjectEnum;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TravelHotelCityType.java
 * @createTime 2025年03月13日 16:32:00
 */
@Getter
public enum TravelHotelCityType {
    /**
     * 国内一类城市：北上广深
     */
    DOMESTIC_LEVEL_ONE(1),

    /**
     * 国内其他城市
     */
    DOMESTIC_OTHER(2),

    /**
     * 亚洲国家及港澳台地区
     */
    INTERNATIONAL_ASIA(3),

    /**
     * 其他国家城市
     */
    INTERNATIONAL_OTHER(4);

    private int type;

    TravelHotelCityType(int type) {
        this.type = type;
    }

    /**
     * 大陆地区类型
     */
    public static final List<Integer> MAINLAND = Arrays.asList(DOMESTIC_LEVEL_ONE.getType(), DOMESTIC_OTHER.getType());

    public static int getTypeBySubjectCode(List<String> secondSubjects) {

        AssertHelper.notEmpty(secondSubjects, "无二级科目");
        if (secondSubjects.contains(ExpenseSecondSubjectEnum.BSGS.getSubjectCode())) {
            return DOMESTIC_LEVEL_ONE.getType();
        } else if (secondSubjects.contains(ExpenseSecondSubjectEnum.GNQTCS.getSubjectCode())) {
            return DOMESTIC_OTHER.getType();
        } else if (secondSubjects.contains(ExpenseSecondSubjectEnum.YZCS.getSubjectCode())) {
            return INTERNATIONAL_ASIA.getType();
        } else if (secondSubjects.contains(ExpenseSecondSubjectEnum.YZYWCS.getSubjectCode())) {
            return INTERNATIONAL_OTHER.getType();
        } else {
            return DOMESTIC_LEVEL_ONE.getType();
        }

    }
}
