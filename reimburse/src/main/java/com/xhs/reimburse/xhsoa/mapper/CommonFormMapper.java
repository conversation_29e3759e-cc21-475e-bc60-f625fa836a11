package com.xhs.reimburse.xhsoa.mapper;

import com.xhs.reimburse.xhsoa.modal.CommonForm;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName CommonFormMapper.java
 * @createTime 2025年03月13日 16:02:00
 */
@Mapper
public interface CommonFormMapper {

    List<CommonForm> batchForceSelectCommonFormByFormNums(@Param("formNumList") List<String> formNumList);

    List<CommonForm> queryEndCommonFrom(@Param("creatorNo") String creatorNo, @Param("auditStatus") Integer auditStatus, @Param("formType") String formType);

    List<CommonForm> queryTravelApplyForm(@Param("paramsMap") Map<String, Object> paramsMap);

    List<CommonForm> queryFormsByParam(@Param("paramsMap") Map<String, Object> paramsMap);
}
