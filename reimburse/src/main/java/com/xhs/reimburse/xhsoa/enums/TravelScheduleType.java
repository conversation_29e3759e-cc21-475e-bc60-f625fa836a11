package com.xhs.reimburse.xhsoa.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TravelScheduleType.java
 * @createTime 2025年03月13日 16:35:00
 */
@Getter
public enum TravelScheduleType {

    /**
     * 允许携程预定
     */
    HOTEL("hotel", "ctrip", "酒店住宿", "hotel"),

    FLIGHT("flight", "ctrip", "飞机", "traffic"),

    /**
     * 其他平台预定
     */
    TRAIN("train", "ctrip", "火车", "traffic"),

    BUS("bus", "other", "汽车", "traffic"),

    OTHER("other", "other", "其他", "traffic");

    public static final List<String> TYPES = Arrays.asList(HOTEL.getCode(), FLIGHT.getCode(), TRAIN.getCode());

    public static final String TYPE_CTRIP = "ctrip";

    public static final String TYPE_OTHER = "other";

    private String code;

    private String type;

    private String name;

    private String routeType;

    TravelScheduleType(String code, String type, String name, String routeType) {
        this.code = code;
        this.type = type;
        this.name = name;
        this.routeType = routeType;
    }

    public static TravelScheduleType getEnumFromCode(String code) {
        for (TravelScheduleType scheduleType : TravelScheduleType.values()) {
            if (StringUtils.equals(code, scheduleType.getCode())) {
                return scheduleType;
            }
        }

        return null;
    }
}

