package com.xhs.reimburse.xhsoa.modal;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName CommonForm.java
 * @createTime 2025年03月13日 16:03:00
 */
@Data
@SuppressWarnings("serial")
public class CommonForm implements Serializable {

    /**
     *
     */
    private Long id;

    /**
     * 单据编号
     */
    private String formNum;

    /**
     * 单据类型
     */
    private String formType;

    /**
     * 单据内容
     */
    private String formContent;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 当前节点
     */
    private String currentStep;

    /**
     * 当前审批人
     */
    private String currentAuditUser;

    /**
     * 审核状态 0: 草稿 1:审核中 2:审核结束 3:已拒绝
     */
    private Integer auditStatus;

    /**
     * 是否红冲 0：未红冲 1：已红冲
     */
    private Integer isWriteBack;

    /**
     * 备注
     */
    private String remark;

    /**
     * 流程实例id
     */
    private String processInstanceId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人id
     */
    private String creatorNo;

    /**
     * 创建人名称
     */
    private String creator;

    /**
     * 更新人id
     */
    private String updatorNo;

    /**
     * 更新人名称
     */
    private String updator;


    private String commonField;

    private Long departmentId;

    private String workingPlace;

    /**
     * 被委托id
     */
    private String beEntrustedId;

    /**
     * 被委托name
     */
    private String beEntrustedName;

    /**
     * 支付类型 -1 历史数据 0 无需支付 1 境内支付 2 境外支付
     */
    private Integer paymentType;

    /**
     * 支付状态
     */
    private String payStatus;

    private Integer isValid;

    //系统code
    private String sysCode;

    //业务编码
    private String businessId;

    //单据的当前节点
    private String currProcessNode;

    //审核类型
    private String auditType;

    //审核id
    private Long auditId;

    //审核类型名称
    private String auditTypeName;

    //是否是V1版本切换V2中间态流程
    private Boolean transFormType;

    //v2流程
    private Boolean newProcess;

    //xhsoa迁移v2流程
    private Boolean transXhsoaV2;

    @ApiModelProperty("v3版本表单")
    private Boolean v3Form;
    @ApiModelProperty("单据类型ID")
    private Long formTypeId;
}