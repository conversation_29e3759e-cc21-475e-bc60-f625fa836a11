package com.xhs.reimburse.xhsoa.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TravelRoutePlaceDto.java
 * @createTime 2025年03月13日 16:16:00
 */
@Data
public class TravelRoutePlaceDto {
    @ApiModelProperty("行程类型，住宿：hotel，交通：traffic")
    private String travelRouteType;

    @ApiModelProperty("地点名称")
    private String showPlaceName;

    @ApiModelProperty("地点列表，每种交通工具/住宿对应一个")
    private List<TravelPlaceDto> travelPlaceVoList;
}
