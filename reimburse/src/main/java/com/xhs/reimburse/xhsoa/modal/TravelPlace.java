package com.xhs.reimburse.xhsoa.modal;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TravelPlace.java
 * @createTime 2025年03月03日 21:26:00
 */

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 携程城市信息，包括航班城市，酒店城市，火车站城市
 *
 * <AUTHOR>
 * @date 2020/6/11
 **/
@Data
public class TravelPlace implements Serializable {

    private Long id;

    /**
     * 地点类型，hotel：住宿地点，flight：航班地点
     */
    private String type;

    /**
     * 是否国内
     */
    private Boolean domestic;

    /**
     * 地点类型，1：北上广深，2：国内其他，3：亚洲港澳台，4：其他国家
     */
    private Integer placeType;

    /**
     * 城市码
     */
    private String placeCode;

    /**
     * 城市名称
     */
    private String placeName;

    /**
     * 城市英文名称
     */
    private String placeEnName;

    /**
     * 城市拼音
     */
    private String placePinyin;

    /**
     * '国家名称'
     */
    private String countryName;

    /**
     * ''国家ID''
     */
    private String countryId;

    /**
     * 是否有效，0：无效 1：有效
     */
    private Integer isValid;

    /**
     * 创建者id
     */
    private String creatorNo;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者id
     */
    private String updaterNo;

    /**
     * 更新时间
     */
    private Date updateTime;
}

