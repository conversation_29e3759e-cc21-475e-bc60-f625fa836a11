package com.xhs.reimburse.xhsoa.service;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.xhs.reimburse.enums.ExpenseFirstSubjectEnum;
import com.xhs.reimburse.enums.travel.TravelScheduleStatus;
import com.xhs.reimburse.modal.request.QueryTravelCityRequest;
import com.xhs.reimburse.service.ReimburseCommonService;
import com.xhs.reimburse.xhsoa.dto.TravelApplyFormDetailDto;
import com.xhs.reimburse.xhsoa.dto.TravelH5ScheduleInfoDto;
import com.xhs.reimburse.xhsoa.dto.TravelPlaceDto;
import com.xhs.reimburse.xhsoa.dto.TravelRoutePlaceDto;
import com.xhs.reimburse.xhsoa.enums.TravelHotelCityType;
import com.xhs.reimburse.xhsoa.enums.TravelScheduleType;
import com.xhs.reimburse.xhsoa.mapper.CommonFormMapper;
import com.xhs.reimburse.xhsoa.modal.CommonForm;
import com.xiaohongshu.fls.rpc.finance.employee.enums.FormAuditStatusEnum;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TravelService.java
 * @createTime 2025年03月13日 16:10:00
 */
@Service
public class TravelService {

    @Resource
    private CommonFormMapper commonFormMapper;
    @Resource
    private ReimburseCommonService reimburseCommonService;

    private static final Pattern DIGIT_PATTERN = Pattern.compile("^\\d+$");

    public static final Integer ONE_NEGATIVE = -1;
    public static final Integer AUDIT_STATUS_SUCCESS = 2;
    public static final String FORM_TYPE_CLSQD = "CLSQD";


    public List<Map<String, String>> queryCityInfoByFormNums(QueryTravelCityRequest searchTravelCityRequest) {
        List<Map<String, String>> cityMaps = new ArrayList<>();
        Set<String> recovery = new HashSet<>();

        List<CommonForm> commonForms = new ArrayList<>();
        if (CollectionUtils.isEmpty(searchTravelCityRequest.getFormNums())) {
            // commonForms = commonFormMapper.queryEndCommonFrom(userId, AUDIT_STATUS_SUCCESS, FORM_TYPE_CLSQD);
            commonForms = queryTravelApplyForm(searchTravelCityRequest);
        } else {
            commonForms = commonFormMapper.batchForceSelectCommonFormByFormNums(searchTravelCityRequest.getFormNums());
        }

        for (CommonForm commonForm : commonForms) {
            queryCityInfoByFormNums(commonForm, recovery, cityMaps, searchTravelCityRequest);
        }

        // 去重复
        return removeDuplicateCities(cityMaps);
    }

    public List<Map<String, String>> removeDuplicateCities(List<Map<String, String>> cityMaps) {
        Map<String, String> cityMap = new HashMap<>();

        for (Map<String, String> city : cityMaps) {
            String cityName = city.get("cityName");
            String cityId = city.get("cityId");

            if (!cityMap.containsKey(cityName)) {
                cityMap.put(cityName, cityId);
            } else {
                // 如果当前 cityId 是数字，而原来的 cityId 不是数字，则替换
                if (isDigit(cityId) && !isDigit(cityMap.get(cityName))) {
                    cityMap.put(cityName, cityId);
                }
            }
        }

        // 转换回 List<Map<String, String>>
        return cityMap.entrySet().stream().map(entry -> {
            Map<String, String> map = new HashMap<>();
            map.put("cityName", entry.getKey());
            map.put("cityId", entry.getValue());
            return map;
        }).collect(Collectors.toList());
    }

    // 判断字符串是否是纯数字
    private static boolean isDigit(String str) {
        return DIGIT_PATTERN.matcher(str).matches();
    }

    public List<CommonForm> queryTravelApplyForm(QueryTravelCityRequest pageQueryParam) {
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("userId", pageQueryParam.getUserId());
        paramsMap.put("formType", "CLSQD");
        paramsMap.put("auditStatus", FormAuditStatusEnum.AUDIT_PASS.getValue());
        paramsMap.put("formNums", pageQueryParam.getFormNums());
        paramsMap.put("scheduleStatusList",
                Lists.newArrayList(TravelScheduleStatus.CANCELED.getCode(), TravelScheduleStatus.CANCELED_CONFIRMED.getCode(),
                        TravelScheduleStatus.CANCELED_REFUSE.getCode()));
        return commonFormMapper.queryTravelApplyForm(paramsMap);
    }

    public List<Map<String, String>> queryCityInfoByFormNums(CommonForm commonForm, Set<String> recovery, List<Map<String, String>> cityMaps, QueryTravelCityRequest queryParam) {
        TravelApplyFormDetailDto travelApplyFormDetailDto = JSON.parseObject(commonForm.getFormContent(), TravelApplyFormDetailDto.class);

        if (CollectionUtils.isEmpty(travelApplyFormDetailDto.getTravelH5ScheduleInfoVoList())) {
            return cityMaps;
        }

        for (TravelH5ScheduleInfoDto dto : travelApplyFormDetailDto.getTravelH5ScheduleInfoVoList()) {
            if (CollectionUtils.isNotEmpty(dto.getArrPlaceRouteVoList())) {
                getCity(dto.getArrPlaceRouteVoList(), recovery, cityMaps, queryParam);
            }
        }

        return cityMaps;
    }

    public void getCity(List<TravelRoutePlaceDto> vos, Set<String> recovery, List<Map<String, String>> cityMaps, QueryTravelCityRequest queryParam) {

        //限制二级科的城市
        int rankCode = TravelHotelCityType.getTypeBySubjectCode(Collections.singletonList(queryParam.getSecondSubject()));
        List<Integer> rankCodeList = ExpenseFirstSubjectEnum.CL_LONG_TRIP.getSubjectCode().equals(queryParam.getSubject()) ?
                Arrays.asList(rankCode, ONE_NEGATIVE) : Collections.singletonList(rankCode);

        //是否是住宿相关
        boolean hotel = ExpenseFirstSubjectEnum.CL_HOTEL.getSubjectCode().equals(queryParam.getSubject())
                || ExpenseFirstSubjectEnum.CL_LONG_TRIP.getSubjectCode().equals(queryParam.getSubject());

        //住宿相关的形成类型
        List<String> typeRouteList = hotel ? Arrays.asList(TravelScheduleType.HOTEL.getCode(), TravelScheduleType.OTHER.getCode()) : new ArrayList<>();

        for (TravelRoutePlaceDto TravelRoutePlaceDto : vos) {
            for (TravelPlaceDto travelPlaceVo : TravelRoutePlaceDto.getTravelPlaceVoList()) {
                Map<String, String> cityMap = new HashMap<>();
                //限制搜索的二级科目城市  不属于同一个级别跳过
                if (hotel && (!typeRouteList.contains(travelPlaceVo.getTravelRouteCode()) || !rankCodeList.contains(travelPlaceVo.getCityType()))) {
                    continue;
                }
                if (!recovery.contains(travelPlaceVo.getPlaceCode())) {
                    if (!Strings.isNullOrEmpty(queryParam.getKeyWord())) {
                        if (!Strings.isNullOrEmpty(travelPlaceVo.getPlaceName())
                                && travelPlaceVo.getPlaceName().contains(queryParam.getKeyWord())) {
                            recovery.add(travelPlaceVo.getPlaceCode());
                            cityMap.put("cityId", travelPlaceVo.getPlaceCode());
                            cityMap.put("cityName", travelPlaceVo.getPlaceName());
                            cityMaps.add(cityMap);
                        }
                    } else {
                        recovery.add(travelPlaceVo.getPlaceCode());
                        cityMap.put("cityId", travelPlaceVo.getPlaceCode());
                        cityMap.put("cityName", travelPlaceVo.getPlaceName());
                        cityMaps.add(cityMap);
                    }
                }
            }
        }
    }
}
