package com.xhs.reimburse.xhsoa.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TravelPlaceDto.java
 * @createTime 2025年03月13日 16:17:00
 */
@Data
public class TravelPlaceDto {
    @ApiModelProperty("行程类型code")
    private String travelRouteCode;

    @ApiModelProperty("是否国内")
    private Boolean domestic;

    @ApiModelProperty("城市类型，1：北上广深，2：国内其他，3：亚洲港澳台，4：其他国家，仅住宿需要，其他为0")
    private Integer cityType;

    @ApiModelProperty("地点code")
    private String placeCode;

    @ApiModelProperty("地点名称")
    private String placeName;
}
