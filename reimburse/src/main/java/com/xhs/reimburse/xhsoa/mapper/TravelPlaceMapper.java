package com.xhs.reimburse.xhsoa.mapper;

import com.xhs.reimburse.modal.dto.travel.SearchPlaceDto;
import com.xhs.reimburse.xhsoa.modal.TravelPlace;
import com.xhs.reimburse.xhsoa.modal.TravelStation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TravelPlaceMapper.java
 * @createTime 2025年03月03日 21:25:00
 */
@Mapper
public interface TravelPlaceMapper {

    /**
     * 批量插入城市记录
     *
     * @param travelPlaceList 城市记录
     * @return count
     */
    int batchInsertPlace(@Param("travelPlaceList") List<TravelPlace> travelPlaceList);


    /**
     * 修改机场城市信息，根据城市名称
     *
     * @param travelPlace
     * @return
     */
    int uqdatePlaceByNameAndType(TravelPlace travelPlace);

    int updateByPrimaryKeySelective(TravelPlace travelPlace);

    /**
     * @param type
     * @param code
     * @return
     */
    List<TravelPlace> selectPlaceByCodeAndType(@Param("type") String type, @Param("code") String code
            , @Param("name") String name, @Param("enName") String enName);

    /**
     * 批量插入站点记录
     *
     * @param travelStationList 站点记录
     * @return count
     */
    int batchInsertStation(@Param("travelStationList") List<TravelStation> travelStationList);

    /**
     * 修改站点信息，根据站点ID和站点名称
     *
     * @param travelStation
     * @return
     */
    int uqdateStationByIdAndName(TravelStation travelStation);

    /**
     * 模糊搜索站点信息
     *
     * @param type    地点类型
     * @param keyword 关键词
     * @param limit   搜索限制
     * @return 站点信息
     */
    List<TravelStation> searchStationByKeyword(@Param("type") String type, @Param("keyword") String keyword,
                                               @Param("limit") int limit);

    /**
     * 模糊搜索地点信息
     *
     * @param type    地点类型
     * @param keyword 关键词
     * @param limit   搜索限制
     * @return 地点信息
     */
    List<TravelPlace> searchPlaceByKeyword(@Param("type") String type, @Param("keyword") String keyword,
                                           @Param("limit") int limit);

    /**
     * 根据地点code查询地点名称集合
     *
     * @param type          类型，飞机or酒店
     * @param placeCode 地点code
     * @return 地点code、名称集合
     */
    List<TravelPlace> selectByPlaceCode(@Param("type") String type, @Param("placeCode")String  placeCode);

    /***
     * 根据地点code+行程类型查询地点名称集合
     */
    List<TravelPlace> searchPlaceMapByCodeAndType(@Param("types") List<String> types, @Param("placeCodeList") List<String> placeCodeList);

    /**
     * 模糊查询城市列表
     *
     * @param placeName 城市名称
     * @return 满足模糊查询的城市列表
     */
    List<String> searchPlaceByPlaceName(@Param("placeName") String placeName);

    List<String> searchPlaceNameByName(@Param("placeName") String placeName, @Param("domestic") Integer domestic);

    List<TravelPlace> searchPlaceNameByNameNotLike(@Param("placeName") String placeName);

    List<TravelPlace> selectPlaceListByName(@Param("placeName") String placeName);

    /**
     * 根据火车站点查询城市code
     * @param stationName
     * @return
     */
    List<TravelStation> selectPlaceListByTrainStationName(@Param("stationName") String stationName);

    int updateCountryInfoByPrimaryKey(TravelPlace travelPlace);

    List<TravelPlace> selectPlaceByCodeAndTypeAndCountryId(@Param("type") String type, @Param("code") String code, @Param("countryId") String countryId);

    int updatePlaceByNameAndTypeAndCountry(TravelPlace travelPlace);

    int updateStationByIdAndNameAndCountry(TravelStation travelStation);

    List<TravelPlace> queryTravelPlaceListByParam(SearchPlaceDto searchPlaceParam);
}
