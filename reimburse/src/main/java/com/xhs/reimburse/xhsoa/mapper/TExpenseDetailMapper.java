package com.xhs.reimburse.xhsoa.mapper;

import com.xhs.reimburse.xhsoa.modal.TExpenseDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * t_expense_detail 费用明细表 Mapper
 */
@Mapper
public interface TExpenseDetailMapper {

    /**
     * 根据单据号查询费用明细
     *
     * @param formNum 单据号
     * @return 费用明细列表
     */
    List<TExpenseDetail> queryByFormNum(@Param("formNum") String formNum);

    /**
     * 根据单据号和二级科目查询费用明细
     *
     * @param formNum 单据号
     * @param secondSubjects 二级科目列表
     * @return 费用明细列表
     */
    List<TExpenseDetail> queryByFormNumAndSecondSubjects(@Param("formNum") String formNum, 
                                                         @Param("secondSubjects") List<String> secondSubjects);
}
