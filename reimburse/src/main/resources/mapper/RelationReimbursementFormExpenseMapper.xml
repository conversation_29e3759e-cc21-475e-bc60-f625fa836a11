<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhs.reimburse.mapper.RelationReimbursementFormExpenseMapper">
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO relation_reimbursement_form_expense (reimbursement_form_uuid, expense_uuid)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.reimbursementFormUuid}, #{item.expenseUuid})
        </foreach>
    </insert>

    <delete id="deleteByExpenseUuid">
        DELETE
        FROM relation_reimbursement_form_expense
        where expense_uuid = #{expenseUuid}
    </delete>
</mapper>