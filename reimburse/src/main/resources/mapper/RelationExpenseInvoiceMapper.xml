<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhs.reimburse.mapper.RelationExpenseInvoiceMapper">
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO relation_expense_invoice (expense_uuid, invoice_uuid)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.expenseUuid}, #{item.invoiceUuid})
        </foreach>
    </insert>

    <select id="selectInvoiceIdsByEIds" resultType="java.lang.String">
        select invoice_uuid
        from relation_expense_invoice
        where expense_uuid in
        <foreach collection="list" item="eId" open="(" close=")" separator="," index="index">
            #{eId}
        </foreach>
    </select>
</mapper>