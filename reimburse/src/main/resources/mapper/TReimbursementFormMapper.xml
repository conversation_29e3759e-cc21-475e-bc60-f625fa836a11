<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhs.reimburse.mapper.TReimbursementFormMapper">
    <resultMap id="BaseResultMap" type="com.xhs.reimburse.modal.entity.ReimbursementFormEntity">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="uuid" jdbcType="VARCHAR" property="uuid"/>
        <result column="form_num" jdbcType="VARCHAR" property="formNum"/>
        <result column="form_type" jdbcType="VARCHAR" property="formType"/>
        <result column="amount" jdbcType="DECIMAL" property="amount"/>
        <result column="reimburse_status" jdbcType="INTEGER" property="reimburseStatus"/>
        <result column="submit_time" jdbcType="TIMESTAMP" property="submitTime" />
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="is_valid" jdbcType="INTEGER" property="isValid"/>
        <result column="creator_no" jdbcType="VARCHAR" property="creatorNo"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="updater_no" jdbcType="VARCHAR" property="updaterNo"/>
        <result column="updater" jdbcType="VARCHAR" property="updater"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="check_status" jdbcType="INTEGER" property="checkStatus"/>
    </resultMap>
    <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs"
               type="com.xhs.reimburse.modal.entity.ReimbursementFormEntity">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        <result column="form_content" jdbcType="LONGVARCHAR" property="formContent"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        id, uuid, form_num, form_type, amount, reimburse_status, submit_time, remark, is_valid, creator_no,
        creator, updater_no, updater, create_time, update_time, check_status
    </sql>
    <sql id="Blob_Column_List">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        form_content
    </sql>
    <select id="selectByExampleWithBLOBs" parameterType="com.xhs.reimburse.modal.entity.ReimbursementFormExample"
            resultMap="ResultMapWithBLOBs">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from reimbursement_form
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <select id="selectByExample" parameterType="com.xhs.reimburse.modal.entity.ReimbursementFormExample"
            resultMap="BaseResultMap">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from reimbursement_form
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from reimbursement_form
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        delete from reimbursement_form
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <delete id="deleteByExample" parameterType="com.xhs.reimburse.modal.entity.ReimbursementFormExample">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        delete from reimbursement_form
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.xhs.reimburse.modal.entity.ReimbursementFormEntity" useGeneratedKeys="true">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        insert into reimbursement_form (uuid, form_num, form_type,
        amount, reimburse_status, submit_time, remark,
        is_valid, creator_no, creator,
        updater_no, updater, create_time,
        update_time, check_status, form_content
        )
        values (#{uuid,jdbcType=VARCHAR}, #{formNum,jdbcType=VARCHAR}, #{formType,jdbcType=VARCHAR},
        #{amount,jdbcType=DECIMAL}, #{reimburseStatus,jdbcType=INTEGER}, #{submitTime,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR},
        #{isValid,jdbcType=INTEGER}, #{creatorNo,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR},
        #{updaterNo,jdbcType=VARCHAR}, #{updater,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP}, #{checkStatus,jdbcType=INTEGER}, #{formContent,jdbcType=LONGVARCHAR}
        )
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.xhs.reimburse.modal.entity.ReimbursementFormEntity" useGeneratedKeys="true">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        insert into reimbursement_form
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="uuid != null">
                uuid,
            </if>
            <if test="formNum != null">
                form_num,
            </if>
            <if test="formType != null">
                form_type,
            </if>
            <if test="amount != null">
                amount,
            </if>
            <if test="reimburseStatus != null">
                reimburse_status,
            </if>
            <if test="submitTime != null">
                submit_time,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="isValid != null">
                is_valid,
            </if>
            <if test="creatorNo != null">
                creator_no,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="updaterNo != null">
                updater_no,
            </if>
            <if test="updater != null">
                updater,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="checkStatus != null">
                check_status,
            </if>
            <if test="formContent != null">
                form_content,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="uuid != null">
                #{uuid,jdbcType=VARCHAR},
            </if>
            <if test="formNum != null">
                #{formNum,jdbcType=VARCHAR},
            </if>
            <if test="formType != null">
                #{formType,jdbcType=VARCHAR},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=DECIMAL},
            </if>
            <if test="reimburseStatus != null">
                #{reimburseStatus,jdbcType=INTEGER},
            </if>
            <if test="submitTime != null">
                #{submitTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="isValid != null">
                #{isValid,jdbcType=INTEGER},
            </if>
            <if test="creatorNo != null">
                #{creatorNo,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="updaterNo != null">
                #{updaterNo,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="checkStatus != null">
                #{checkStatus,jdbcType=INTEGER},
            </if>
            <if test="formContent != null">
                #{formContent,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="com.xhs.reimburse.modal.entity.ReimbursementFormExample"
            resultType="java.lang.Long">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        select count(*) from reimbursement_form
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        update reimbursement_form
        <set>
            <if test="row.id != null">
                id = #{row.id,jdbcType=BIGINT},
            </if>
            <if test="row.uuid != null">
                uuid = #{row.uuid,jdbcType=VARCHAR},
            </if>
            <if test="row.formNum != null">
                form_num = #{row.formNum,jdbcType=VARCHAR},
            </if>
            <if test="row.formType != null">
                form_type = #{row.formType,jdbcType=VARCHAR},
            </if>
            <if test="row.amount != null">
                amount = #{row.amount,jdbcType=DECIMAL},
            </if>
            <if test="row.reimburseStatus != null">
                reimburse_status = #{row.reimburseStatus,jdbcType=INTEGER},
            </if>
            <if test="row.submitTime != null">
                submit_time = #{row.submitTime,jdbcType=TIMESTAMP},
            </if>
            <if test="row.remark != null">
                remark = #{row.remark,jdbcType=VARCHAR},
            </if>
            <if test="row.isValid != null">
                is_valid = #{row.isValid,jdbcType=INTEGER},
            </if>
            <if test="row.creatorNo != null">
                creator_no = #{row.creatorNo,jdbcType=VARCHAR},
            </if>
            <if test="row.creator != null">
                creator = #{row.creator,jdbcType=VARCHAR},
            </if>
            <if test="row.updaterNo != null">
                updater_no = #{row.updaterNo,jdbcType=VARCHAR},
            </if>
            <if test="row.updater != null">
                updater = #{row.updater,jdbcType=VARCHAR},
            </if>
            <if test="row.createTime != null">
                create_time = #{row.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="row.updateTime != null">
                update_time = #{row.updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="row.checkStatus != null">
                check_status = #{row.checkStatus,jdbcType=INTEGER},
            </if>
            <if test="row.formContent != null">
                form_content = #{row.formContent,jdbcType=LONGVARCHAR},
            </if>
        </set>
        <if test="example != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExampleWithBLOBs" parameterType="map">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        update reimbursement_form
        set id = #{row.id,jdbcType=BIGINT},
        uuid = #{row.uuid,jdbcType=VARCHAR},
        form_num = #{row.formNum,jdbcType=VARCHAR},
        form_type = #{row.formType,jdbcType=VARCHAR},
        amount = #{row.amount,jdbcType=DECIMAL},
        reimburse_status = #{row.reimburseStatus,jdbcType=INTEGER},
        submit_time = #{row.submitTime,jdbcType=TIMESTAMP},
        remark = #{row.remark,jdbcType=VARCHAR},
        is_valid = #{row.isValid,jdbcType=INTEGER},
        creator_no = #{row.creatorNo,jdbcType=VARCHAR},
        creator = #{row.creator,jdbcType=VARCHAR},
        updater_no = #{row.updaterNo,jdbcType=VARCHAR},
        updater = #{row.updater,jdbcType=VARCHAR},
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
        check_status = #{row.checkStatus,jdbcType=INTEGER},
        form_content = #{row.formContent,jdbcType=LONGVARCHAR}
        <if test="example != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        update reimbursement_form
        set id = #{row.id,jdbcType=BIGINT},
        uuid = #{row.uuid,jdbcType=VARCHAR},
        form_num = #{row.formNum,jdbcType=VARCHAR},
        form_type = #{row.formType,jdbcType=VARCHAR},
        amount = #{row.amount,jdbcType=DECIMAL},
        reimburse_status = #{row.reimburseStatus,jdbcType=INTEGER},
        submit_time = #{row.submitTime,jdbcType=TIMESTAMP},
        remark = #{row.remark,jdbcType=VARCHAR},
        is_valid = #{row.isValid,jdbcType=INTEGER},
        creator_no = #{row.creatorNo,jdbcType=VARCHAR},
        creator = #{row.creator,jdbcType=VARCHAR},
        updater_no = #{row.updaterNo,jdbcType=VARCHAR},
        updater = #{row.updater,jdbcType=VARCHAR},
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
        check_status = #{row.checkStatus,jdbcType=INTEGER}
        <if test="example != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective" parameterType="com.xhs.reimburse.modal.entity.ReimbursementFormEntity">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        update reimbursement_form
        <set>
            <if test="uuid != null">
                uuid = #{uuid,jdbcType=VARCHAR},
            </if>
            <if test="formNum != null">
                form_num = #{formNum,jdbcType=VARCHAR},
            </if>
            <if test="formType != null">
                form_type = #{formType,jdbcType=VARCHAR},
            </if>
            <if test="amount != null">
                amount = #{amount,jdbcType=DECIMAL},
            </if>
            <if test="reimburseStatus != null">
                reimburse_status = #{reimburseStatus,jdbcType=INTEGER},
            </if>
            <if test="submitTime != null">
                submit_time = #{submitTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="isValid != null">
                is_valid = #{isValid,jdbcType=INTEGER},
            </if>
            <if test="creatorNo != null">
                creator_no = #{creatorNo,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=VARCHAR},
            </if>
            <if test="updaterNo != null">
                updater_no = #{updaterNo,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="checkStatus != null">
                check_status = #{checkStatus,jdbcType=INTEGER},
            </if>
            <if test="formContent != null">
                form_content = #{formContent,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>


    <select id="pageQueryReimbursementForm" resultType="com.xhs.reimburse.modal.entity.ReimbursementFormEntity">
        select
        <include refid="Base_Column_List"/>,
        <include refid="Blob_Column_List"/>
        from reimbursement_form
        where is_valid = 1
        and creator_no = #{userId}
        <if test="formType != null and formType != ''">
            and form_type = #{formType}
        </if>
        <if test="transferStatus != null and transferStatus.size() > 0">
            and reimburse_status in
            <foreach collection="transferStatus" item="formStatus" open="(" close=")" separator="," index="index">
                #{formStatus}
            </foreach>
        </if>
        order by id desc
    </select>
    <select id="selectPendingCount" resultType="java.lang.Integer">
        select
        count(id)
        from reimbursement_form
        where is_valid = 1
        and creator_no = #{userId}
        <if test="pendingStatus != null and pendingStatus.size() > 0">
            and reimburse_status in
            <foreach collection="pendingStatus" item="formStatus" open="(" close=")" separator="," index="index">
                #{formStatus}
            </foreach>
        </if>
    </select>
    <update id="updateByUuidSelective" parameterType="com.xhs.reimburse.modal.entity.ReimbursementFormEntity">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        update reimbursement_form
        <set>
            <if test="amount != null">
                amount = #{amount,jdbcType=DECIMAL},
            </if>
            <if test="checkStatus != null">
                check_status = #{checkStatus,jdbcType=INTEGER},
            </if>
            <if test="reimburseStatus != null">
                reimburse_status = #{reimburseStatus,jdbcType=INTEGER},
            </if>
            <if test="submitTime != null">
                submit_time = #{submitTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="isValid != null">
                is_valid = #{isValid,jdbcType=INTEGER},
            </if>
            <if test="updaterNo != null">
                updater_no = #{updaterNo,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=VARCHAR},
            </if>
            <if test="formContent != null">
                form_content = #{formContent,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where uuid = #{uuid,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.xhs.reimburse.modal.entity.ReimbursementFormEntity">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        update reimbursement_form
        set uuid = #{uuid,jdbcType=VARCHAR},
        form_num = #{formNum,jdbcType=VARCHAR},
        form_type = #{formType,jdbcType=VARCHAR},
        amount = #{amount,jdbcType=DECIMAL},
        check_status = #{checkStatus,jdbcType=INTEGER},
        reimburse_status = #{reimburseStatus,jdbcType=INTEGER},
        submit_time = #{submitTime,jdbcType=TIMESTAMP},
        remark = #{remark,jdbcType=VARCHAR},
        is_valid = #{isValid,jdbcType=INTEGER},
        creator_no = #{creatorNo,jdbcType=VARCHAR},
        creator = #{creator,jdbcType=VARCHAR},
        updater_no = #{updaterNo,jdbcType=VARCHAR},
        updater = #{updater,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        form_content = #{formContent,jdbcType=LONGVARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.xhs.reimburse.modal.entity.ReimbursementFormEntity">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        update reimbursement_form
        set uuid = #{uuid,jdbcType=VARCHAR},
        form_num = #{formNum,jdbcType=VARCHAR},
        form_type = #{formType,jdbcType=VARCHAR},
        amount = #{amount,jdbcType=DECIMAL},
        check_status = #{checkStatus,jdbcType=INTEGER},
        reimburse_status = #{reimburseStatus,jdbcType=INTEGER},
        submit_time = #{submitTime,jdbcType=TIMESTAMP},
        remark = #{remark,jdbcType=VARCHAR},
        is_valid = #{isValid,jdbcType=INTEGER},
        creator_no = #{creatorNo,jdbcType=VARCHAR},
        creator = #{creator,jdbcType=VARCHAR},
        updater_no = #{updaterNo,jdbcType=VARCHAR},
        updater = #{updater,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="invalidReimbursementFormByFormNum">
        update reimbursement_form
        set is_valid = 0
        where form_num = #{formNum}
    </update>

    <update id="invalidReimbursementFormByUuid">
        update reimbursement_form
        set is_valid = 0
        where uuid = #{uuid}
    </update>
</mapper>