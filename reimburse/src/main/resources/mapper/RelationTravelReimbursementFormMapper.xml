<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhs.reimburse.mapper.TRelationTravelReimbursementFormMapper">
  <resultMap id="BaseResultMap" type="com.xhs.reimburse.modal.entity.travel.RelationTravelReimbursementForm">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="travel_form_num" jdbcType="VARCHAR" property="travelFormNum" />
    <result column="reimbursement_id" jdbcType="VARCHAR" property="reimbursementId" />
    <result column="is_valid" jdbcType="TINYINT" property="isValid" />
    <result column="creator_no" jdbcType="VARCHAR" property="creatorNo" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updater_no" jdbcType="VARCHAR" property="updaterNo" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    id, travel_form_num, reimbursement_id, is_valid, creator_no, create_time, updater_no, 
    update_time
  </sql>
  <select id="selectByExample" parameterType="com.xhs.reimburse.modal.entity.travel.RelationTravelReimbursementFormExample" resultMap="BaseResultMap">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from relation_travel_reimbursement_form
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    select 
    <include refid="Base_Column_List" />
    from relation_travel_reimbursement_form
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    delete from relation_travel_reimbursement_form
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.xhs.reimburse.modal.entity.travel.RelationTravelReimbursementFormExample">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    delete from relation_travel_reimbursement_form
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.xhs.reimburse.modal.entity.travel.RelationTravelReimbursementForm" useGeneratedKeys="true">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    insert into relation_travel_reimbursement_form (travel_form_num, reimbursement_id, is_valid, 
      creator_no, create_time, updater_no, 
      update_time)
    values (#{travelFormNum,jdbcType=VARCHAR}, #{reimbursementId,jdbcType=VARCHAR}, #{isValid,jdbcType=TINYINT}, 
      #{creatorNo,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updaterNo,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.xhs.reimburse.modal.entity.travel.RelationTravelReimbursementForm" useGeneratedKeys="true">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    insert into relation_travel_reimbursement_form
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="travelFormNum != null">
        travel_form_num,
      </if>
      <if test="reimbursementId != null">
        reimbursement_id,
      </if>
      <if test="isValid != null">
        is_valid,
      </if>
      <if test="creatorNo != null">
        creator_no,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updaterNo != null">
        updater_no,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="travelFormNum != null">
        #{travelFormNum,jdbcType=VARCHAR},
      </if>
      <if test="reimbursementId != null">
        #{reimbursementId,jdbcType=VARCHAR},
      </if>
      <if test="isValid != null">
        #{isValid,jdbcType=TINYINT},
      </if>
      <if test="creatorNo != null">
        #{creatorNo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updaterNo != null">
        #{updaterNo,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <insert id="batchInsert">
    insert into relation_travel_reimbursement_form (travel_form_num, reimbursement_id,creator_no, updater_no)
    values
    <foreach collection="list" item="i" separator=",">
    (
     #{i.travelFormNum},
     #{i.reimbursementId},
     #{i.creatorNo},
     #{i.updaterNo}
    )
    </foreach>
  </insert>
  <select id="countByExample" parameterType="com.xhs.reimburse.modal.entity.travel.RelationTravelReimbursementFormExample" resultType="java.lang.Long">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    select count(*) from relation_travel_reimbursement_form
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <select id="selectTravelApplyNumsByFId" resultType="java.lang.String">
    select travel_form_num
    from relation_travel_reimbursement_form
    where is_valid = 1
      and reimbursement_id = #{formUuid}
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    update relation_travel_reimbursement_form
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.travelFormNum != null">
        travel_form_num = #{row.travelFormNum,jdbcType=VARCHAR},
      </if>
      <if test="row.reimbursementId != null">
        reimbursement_id = #{row.reimbursementId,jdbcType=VARCHAR},
      </if>
      <if test="row.isValid != null">
        is_valid = #{row.isValid,jdbcType=TINYINT},
      </if>
      <if test="row.creatorNo != null">
        creator_no = #{row.creatorNo,jdbcType=VARCHAR},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updaterNo != null">
        updater_no = #{row.updaterNo,jdbcType=VARCHAR},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    update relation_travel_reimbursement_form
    set id = #{row.id,jdbcType=BIGINT},
      travel_form_num = #{row.travelFormNum,jdbcType=VARCHAR},
      reimbursement_id = #{row.reimbursementId,jdbcType=VARCHAR},
      is_valid = #{row.isValid,jdbcType=TINYINT},
      creator_no = #{row.creatorNo,jdbcType=VARCHAR},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      updater_no = #{row.updaterNo,jdbcType=VARCHAR},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.xhs.reimburse.modal.entity.travel.RelationTravelReimbursementForm">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    update relation_travel_reimbursement_form
    <set>
      <if test="travelFormNum != null">
        travel_form_num = #{travelFormNum,jdbcType=VARCHAR},
      </if>
      <if test="reimbursementId != null">
        reimbursement_id = #{reimbursementId,jdbcType=VARCHAR},
      </if>
      <if test="isValid != null">
        is_valid = #{isValid,jdbcType=TINYINT},
      </if>
      <if test="creatorNo != null">
        creator_no = #{creatorNo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updaterNo != null">
        updater_no = #{updaterNo,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xhs.reimburse.modal.entity.travel.RelationTravelReimbursementForm">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    update relation_travel_reimbursement_form
    set travel_form_num = #{travelFormNum,jdbcType=VARCHAR},
      reimbursement_id = #{reimbursementId,jdbcType=VARCHAR},
      is_valid = #{isValid,jdbcType=TINYINT},
      creator_no = #{creatorNo,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      updater_no = #{updaterNo,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="batchInvalid">
    <if test="ids != null and ids.size() > 0">
      update relation_travel_reimbursement_form
      set is_valid = 0
      where is_valid = 1
      and id in
      <foreach collection="ids" item="id" separator="," open="(" close=")">
        #{id}
      </foreach>
    </if>
  </update>
</mapper>