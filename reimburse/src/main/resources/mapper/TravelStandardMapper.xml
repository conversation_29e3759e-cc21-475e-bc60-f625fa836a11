<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhs.reimburse.mapper.TravelStandardMapper">
  <resultMap id="BaseResultMap" type="com.xhs.reimburse.modal.entity.travel.TravelStandard">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="subject" jdbcType="VARCHAR" property="subject" />
    <result column="city_id" jdbcType="VARCHAR" property="cityId" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="country_id" jdbcType="VARCHAR" property="countryId" />
    <result column="country_name" jdbcType="VARCHAR" property="countryName" />
    <result column="time_type" jdbcType="VARCHAR" property="timeType" />
    <result column="is_together" jdbcType="VARCHAR" property="isTogether" />
    <result column="currency" jdbcType="VARCHAR" property="currency" />
    <result column="currency_name" jdbcType="VARCHAR" property="currencyName" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="is_valid" jdbcType="TINYINT" property="isValid" />
    <result column="creator_no" jdbcType="VARCHAR" property="creatorNo" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updater_no" jdbcType="VARCHAR" property="updaterNo" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    id, subject, city_id, city_name, country_id, country_name, time_type, is_together,
    currency, currency_name, amount, is_valid, creator_no, create_time, updater_no, update_time
  </sql>
  <select id="selectByExample" parameterType="com.xhs.reimburse.modal.entity.travel.TravelStandardExample" resultMap="BaseResultMap">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from travel_standard
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    select
    <include refid="Base_Column_List" />
    from travel_standard
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    delete from travel_standard
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.xhs.reimburse.modal.entity.travel.TravelStandardExample">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    delete from travel_standard
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.xhs.reimburse.modal.entity.travel.TravelStandard" useGeneratedKeys="true">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    insert into travel_standard (subject, city_id, city_name,
    country_id, country_name, time_type,
    is_together, currency, currency_name,
    amount, is_valid, creator_no,
    create_time, updater_no, update_time
    )
    values (#{subject,jdbcType=VARCHAR}, #{cityId,jdbcType=VARCHAR}, #{cityName,jdbcType=VARCHAR},
    #{countryId,jdbcType=VARCHAR}, #{countryName,jdbcType=VARCHAR}, #{timeType,jdbcType=VARCHAR},
    #{isTogether,jdbcType=VARCHAR}, #{currency,jdbcType=VARCHAR}, #{currencyName,jdbcType=VARCHAR},
    #{amount,jdbcType=DECIMAL}, #{isValid,jdbcType=TINYINT}, #{creatorNo,jdbcType=VARCHAR},
    #{createTime,jdbcType=TIMESTAMP}, #{updaterNo,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}
    )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.xhs.reimburse.modal.entity.travel.TravelStandard" useGeneratedKeys="true">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    insert into travel_standard
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="subject != null">
        subject,
      </if>
      <if test="cityId != null">
        city_id,
      </if>
      <if test="cityName != null">
        city_name,
      </if>
      <if test="countryId != null">
        country_id,
      </if>
      <if test="countryName != null">
        country_name,
      </if>
      <if test="timeType != null">
        time_type,
      </if>
      <if test="isTogether != null">
        is_together,
      </if>
      <if test="currency != null">
        currency,
      </if>
      <if test="currencyName != null">
        currency_name,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="isValid != null">
        is_valid,
      </if>
      <if test="creatorNo != null">
        creator_no,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updaterNo != null">
        updater_no,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="subject != null">
        #{subject,jdbcType=VARCHAR},
      </if>
      <if test="cityId != null">
        #{cityId,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null">
        #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="countryId != null">
        #{countryId,jdbcType=VARCHAR},
      </if>
      <if test="countryName != null">
        #{countryName,jdbcType=VARCHAR},
      </if>
      <if test="timeType != null">
        #{timeType,jdbcType=VARCHAR},
      </if>
      <if test="isTogether != null">
        #{isTogether,jdbcType=VARCHAR},
      </if>
      <if test="currency != null">
        #{currency,jdbcType=VARCHAR},
      </if>
      <if test="currencyName != null">
        #{currencyName,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="isValid != null">
        #{isValid,jdbcType=TINYINT},
      </if>
      <if test="creatorNo != null">
        #{creatorNo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updaterNo != null">
        #{updaterNo,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.xhs.reimburse.modal.entity.travel.TravelStandardExample" resultType="java.lang.Long">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    select count(*) from travel_standard
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    update travel_standard
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.subject != null">
        subject = #{row.subject,jdbcType=VARCHAR},
      </if>
      <if test="row.cityId != null">
        city_id = #{row.cityId,jdbcType=VARCHAR},
      </if>
      <if test="row.cityName != null">
        city_name = #{row.cityName,jdbcType=VARCHAR},
      </if>
      <if test="row.countryId != null">
        country_id = #{row.countryId,jdbcType=VARCHAR},
      </if>
      <if test="row.countryName != null">
        country_name = #{row.countryName,jdbcType=VARCHAR},
      </if>
      <if test="row.timeType != null">
        time_type = #{row.timeType,jdbcType=VARCHAR},
      </if>
      <if test="row.isTogether != null">
        is_together = #{row.isTogether,jdbcType=VARCHAR},
      </if>
      <if test="row.currency != null">
        currency = #{row.currency,jdbcType=VARCHAR},
      </if>
      <if test="row.currencyName != null">
        currency_name = #{row.currencyName,jdbcType=VARCHAR},
      </if>
      <if test="row.amount != null">
        amount = #{row.amount,jdbcType=DECIMAL},
      </if>
      <if test="row.isValid != null">
        is_valid = #{row.isValid,jdbcType=TINYINT},
      </if>
      <if test="row.creatorNo != null">
        creator_no = #{row.creatorNo,jdbcType=VARCHAR},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updaterNo != null">
        updater_no = #{row.updaterNo,jdbcType=VARCHAR},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    update travel_standard
    set id = #{row.id,jdbcType=BIGINT},
    subject = #{row.subject,jdbcType=VARCHAR},
    city_id = #{row.cityId,jdbcType=VARCHAR},
    city_name = #{row.cityName,jdbcType=VARCHAR},
    country_id = #{row.countryId,jdbcType=VARCHAR},
    country_name = #{row.countryName,jdbcType=VARCHAR},
    time_type = #{row.timeType,jdbcType=VARCHAR},
    is_together = #{row.isTogether,jdbcType=VARCHAR},
    currency = #{row.currency,jdbcType=VARCHAR},
    currency_name = #{row.currencyName,jdbcType=VARCHAR},
    amount = #{row.amount,jdbcType=DECIMAL},
    is_valid = #{row.isValid,jdbcType=TINYINT},
    creator_no = #{row.creatorNo,jdbcType=VARCHAR},
    create_time = #{row.createTime,jdbcType=TIMESTAMP},
    updater_no = #{row.updaterNo,jdbcType=VARCHAR},
    update_time = #{row.updateTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.xhs.reimburse.modal.entity.travel.TravelStandard">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    update travel_standard
    <set>
      <if test="subject != null">
        subject = #{subject,jdbcType=VARCHAR},
      </if>
      <if test="cityId != null">
        city_id = #{cityId,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null">
        city_name = #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="countryId != null">
        country_id = #{countryId,jdbcType=VARCHAR},
      </if>
      <if test="countryName != null">
        country_name = #{countryName,jdbcType=VARCHAR},
      </if>
      <if test="timeType != null">
        time_type = #{timeType,jdbcType=VARCHAR},
      </if>
      <if test="isTogether != null">
        is_together = #{isTogether,jdbcType=VARCHAR},
      </if>
      <if test="currency != null">
        currency = #{currency,jdbcType=VARCHAR},
      </if>
      <if test="currencyName != null">
        currency_name = #{currencyName,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="isValid != null">
        is_valid = #{isValid,jdbcType=TINYINT},
      </if>
      <if test="creatorNo != null">
        creator_no = #{creatorNo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updaterNo != null">
        updater_no = #{updaterNo,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xhs.reimburse.modal.entity.travel.TravelStandard">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    update travel_standard
    set subject = #{subject,jdbcType=VARCHAR},
    city_id = #{cityId,jdbcType=VARCHAR},
    city_name = #{cityName,jdbcType=VARCHAR},
    country_id = #{countryId,jdbcType=VARCHAR},
    country_name = #{countryName,jdbcType=VARCHAR},
    time_type = #{timeType,jdbcType=VARCHAR},
    is_together = #{isTogether,jdbcType=VARCHAR},
    currency = #{currency,jdbcType=VARCHAR},
    currency_name = #{currencyName,jdbcType=VARCHAR},
    amount = #{amount,jdbcType=DECIMAL},
    is_valid = #{isValid,jdbcType=TINYINT},
    creator_no = #{creatorNo,jdbcType=VARCHAR},
    create_time = #{createTime,jdbcType=TIMESTAMP},
    updater_no = #{updaterNo,jdbcType=VARCHAR},
    update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>