<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhs.reimburse.mapper.BankAccountMapper">
    <update id="updateBankAccountById">
        update reimburse_user_bank_account
        set bank_name    = #{bankName,jdbcType=VARCHAR},
            bank_code    = #{bankCode,jdbcType=VARCHAR},
            account_no   = #{accountNo,jdbcType=VARCHAR},
            account_name = #{accountName,jdbcType=VARCHAR},
            is_default   = #{isDefault,jdbcType=TINYINT},
            is_valid     = #{isValid,jdbcType=TINYINT}
        where id = #{id,jdbcType=BIGINT}
          and user_id = #{userId,jdbcType=VARCHAR}
    </update>
    <select id="getUserBankAccountList" resultType="com.xhs.reimburse.modal.entity.BankAccountEntity">
        SELECT id,
               user_id,
               bank_name,
               bank_code,
               account_no,
               account_name,
               is_default,
               is_valid,
               create_time,
               update_time
        FROM reimburse_user_bank_account
        WHERE user_id = #{userId}
    </select>

    <insert id="addBankAccount" useGeneratedKeys="true"
            parameterType="com.xhs.reimburse.modal.entity.BankAccountEntity" keyProperty="id">
        INSERT INTO reimburse_user_bank_account (user_id,
                                                 bank_name,
                                                 bank_code,
                                                 account_no,
                                                 account_name,
                                                 is_default)
            VALUE
            ( #{userId},
            #{bankName},
            #{bankCode},
            #{accountNo},
            #{accountName},
            #{isDefault}
            )
    </insert>
</mapper>