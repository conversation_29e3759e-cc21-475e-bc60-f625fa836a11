<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhs.reimburse.xhsoa.mapper.TravelPlaceMapper">

    <sql id="PlaceBaseColumn">
        tp
        .
        id
        id,
               tp.type          type,
               tp.domestic      domestic,
               tp.place_type    placeType,
               tp.place_code    placeCode,
               tp.place_name    placeName,
               tp.place_en_name placeEnName,
               tp.place_pinyin  placePinyin,
               tp.country_name  countryName,
               tp.country_id  countryId,
               tp.is_valid      isValid,
               tp.creator_no    creatorNo,
               tp.create_time   createTime,
               tp.updater_no    updaterNo,
               tp.update_time   updateTime
    </sql>

    <sql id="StationBaseColumn">
        ts
        .
        id
        id,
               ts.type            type,
               ts.place_code      placeCode,
               ts.station_id      stationId,
               ts.station_name    stationName,
               ts.station_en_name stationEnName,
               ts.country_name countryName,
               ts.country_id countryId,
               ts.is_valid        isValid,
               ts.creator_no      creatorNo,
               ts.create_time     createTime,
               ts.updater_no      updaterNo,
               ts.update_time     updateTime
    </sql>

    <insert id="batchInsertPlace" parameterType="com.xhs.reimburse.xhsoa.modal.TravelPlace"
            useGeneratedKeys="true"
            keyProperty="id">
        insert into travel_place(type,
        domestic,
        place_type,
        place_code,
        place_name,
        place_en_name,
        place_pinyin,
        country_name,
        country_id,
        is_valid,
        creator_no,
        updater_no)
        values
        <foreach collection="travelPlaceList" item="item" separator=",">
            (
            #{item.type},
            #{item.domestic},
            #{item.placeType},
            #{item.placeCode},
            #{item.placeName},
            #{item.placeEnName},
            #{item.placePinyin},
            #{item.countryName},
            #{item.countryId},
            1,
            #{item.creatorNo},
            #{item.updaterNo}
            )
        </foreach>
    </insert>

    <insert id="batchInsertStation" parameterType="com.xhs.reimburse.xhsoa.modal.TravelStation"
            useGeneratedKeys="true"
            keyProperty="id">
        insert into travel_station(type,
        place_code,
        station_id,
        station_name,
        station_en_name,
        country_name,
        country_id,
        is_valid,
        creator_no,
        updater_no)
        values
        <foreach collection="travelStationList" item="item" separator=",">
            (
            #{item.type},
            #{item.placeCode},
            #{item.stationId},
            #{item.stationName},
            #{item.stationEnName},
            #{item.countryName},
            #{item.countryId},
            1,
            #{item.creatorNo},
            #{item.updaterNo}
            )
        </foreach>
    </insert>
    <!-- 修改机场信息，根据城市名称-->
    <update id="uqdatePlaceByNameAndType"
            parameterType="com.xhs.reimburse.xhsoa.modal.TravelPlace">
        update `travel_place`
        <set>
            domestic=#{domestic},
            place_type=#{placeType},
            place_code=#{placeCode},
            place_en_name=#{placeEnName},
            place_pinyin=#{placePinyin},
            country_id=#{countryId},
            country_name=#{countryName},
            updater_no=#{updaterNo},
            update_time=now()
        </set>
        <where>
            `type`=#{type} and place_code = #{placeCode} and place_name = #{placeName} and is_valid = 1
        </where>
    </update>

    <!--修改站点信息，根据站点ID和站点名称-->
    <update id="uqdateStationByIdAndName"
            parameterType="com.xhs.reimburse.xhsoa.modal.TravelStation">
        update `travel_station`
        <set>
            place_code=#{placeCode},
            station_en_name=#{stationEnName},
            country_id=#{countryId},
            country_name=#{countryName},
            updater_no=#{updaterNo},
            update_time=now()
        </set>
        <where>
            `type`=#{type} and station_id=#{stationId} and station_name=#{stationName} and is_valid = 1
        </where>

    </update>

    <select id="searchPlaceByKeyword" resultType="com.xhs.reimburse.xhsoa.modal.TravelPlace">
        select
        <include refid="PlaceBaseColumn"/>
        from travel_place tp
        where tp.is_valid = 1
        and tp.type = #{type}
        and (tp.place_name like concat('%', #{keyword}, '%')
        or tp.place_en_name like concat('%', #{keyword}, '%')
        or tp.place_pinyin like concat('%', #{keyword}, '%'))
        limit #{limit}
    </select>

    <select id="searchStationByKeyword" resultType="com.xhs.reimburse.xhsoa.modal.TravelStation">
        select
        <include refid="StationBaseColumn"/>
        from travel_station ts
        where ts.is_valid = 1
        and ts.type = #{type}
        and (ts.station_id like concat('%', #{keyword}, '%')
        or ts.station_name like concat('%', #{keyword}, '%')
        or ts.station_en_name like concat('%', #{keyword}, '%'))
        limit #{limit}
    </select>

    <select id="selectByPlaceCode" resultType="com.xhs.reimburse.xhsoa.modal.TravelPlace">
        select
        <include refid="PlaceBaseColumn"/>
        from travel_place tp
        where is_valid = 1
        <trim prefix="AND" prefixOverrides="AND |OR">
            <if test="type != null and type != ''">
                type = #{type}
            </if>
            <if test="placeCode != null and placeCode != ''">
                and place_code = #{placeCode}
            </if>
        </trim>
    </select>

    <select id="searchPlaceMapByCodeAndType" resultType="com.xhs.reimburse.xhsoa.modal.TravelPlace">
        select
        <include refid="PlaceBaseColumn"/>
        from travel_place tp
        where 1=1
        and tp.is_valid = 1
        and type in
        <foreach collection="types" item="type" open="(" close=")" separator=",">
            #{type}
        </foreach>
        and place_code in
        <foreach collection="placeCodeList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="searchPlaceByPlaceName" resultType="java.lang.String">
        select distinct place_name
        from travel_place tp
        where tp.is_valid = 1
          and tp.place_type &lt; 3
          and tp.place_type &gt; 0
          and (tp.place_name like concat('%', #{placeName}, '%'))
    </select>
    <select id="selectPlaceByCodeAndType"
            resultType="com.xhs.reimburse.xhsoa.modal.TravelPlace">

        select
        <include refid="PlaceBaseColumn"/>
        from travel_place tp where type = #{type} and place_code = #{code} and place_name = #{name} and place_en_name =
        #{enName}
    </select>

    <select id="searchPlaceNameByName" resultType="java.lang.String">
        select distinct place_name
        from travel_place
        where is_valid = 1
          and domestic = #{domestic}
          and (place_name like concat('%', #{placeName}, '%'))
    </select>

    <select id="searchPlaceNameByNameNotLike" resultType="com.xhs.reimburse.xhsoa.modal.TravelPlace">
        select
        <include refid="PlaceBaseColumn"/>
        from travel_place
        where is_valid = 1 and place_name = #{placeName}
    </select>

    <select id="selectPlaceListByName" resultType="com.xhs.reimburse.xhsoa.modal.TravelPlace">
        select
        <include refid="PlaceBaseColumn"/>
        from travel_place tp
        where tp.is_valid = 1 and tp.place_name = #{placeName}
    </select>

    <select id="selectPlaceListByTrainStationName" resultType="com.xhs.reimburse.xhsoa.modal.TravelStation">
        select
        <include refid="StationBaseColumn"/>
        from travel_station ts
        where ts.`type` = 'train' and country_id = 1 and ts.is_valid = 1 and ts.station_name =#{stationName}
    </select>

    <select id="selectPlaceByCodeAndTypeAndCountryId"
            resultType="com.xhs.reimburse.xhsoa.modal.TravelPlace">
        select
        <include refid="PlaceBaseColumn"/>
        from travel_place tp where type = #{type} and country_id = #{countryId} and place_code = #{code}
    </select>

    <select id="queryTravelPlaceListByParam" resultType="com.xhs.reimburse.xhsoa.modal.TravelPlace">
        select
        <include refid="PlaceBaseColumn"/>
        from travel_place tp
        where tp.is_valid = 1
        <if test="type != null and type != ''">
            and tp.type = #{type}
        </if>
        <if test="countryId != null and countryId != ''">
            and tp.country_id = #{countryId}
        </if>
        <if test="cityName != null and cityName != ''">
            and tp.place_name = #{cityName}
        </if>
        <if test="countryName != null and countryName != ''">
            and tp.country_name = #{countryName}
        </if>
    </select>

    <update id="updateByPrimaryKeySelective" parameterType="com.xhs.reimburse.xhsoa.modal.TravelPlace">
        update travel_place
        <set>
            <if test="type != null">
                `type` = #{type},
            </if>
            <if test="domestic != null">
                domestic = #{domestic},
            </if>
            <if test="placeType != null">
                place_type = #{placeType},
            </if>
            <if test="placeCode != null">
                place_code = #{placeCode},
            </if>
            <if test="placeName != null">
                place_name = #{placeName},
            </if>
            <if test="placeEnName != null">
                place_en_name = #{placeEnName},
            </if>
            <if test="placePinyin != null">
                place_pinyin = #{placePinyin},
            </if>
            <if test="isValid != null">
                is_valid = #{isValid},
            </if>
            <if test="creatorNo != null">
                creator_no = #{creatorNo},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updaterNo != null">
                updater_no = #{updaterNo},
            </if>
            <if test="updateTime != null">
                update_time = now(),
            </if>
            <if test="countryName != null">
                country_name = #{countryName},
            </if>
            <if test="countryId != null">
                country_id = #{countryId},
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="updateCountryInfoByPrimaryKey">
        update travel_place
        set country_name = #{countryName},
            country_id   = #{countryId},
            update_time  = now()
        where id = #{id}
    </update>

    <update id="updatePlaceByNameAndTypeAndCountry">
        update `travel_place`
        <set>
            domestic=#{domestic},
            place_type=#{placeType},
            place_code=#{placeCode},
            place_en_name=#{placeEnName},
            place_pinyin=#{placePinyin},
            country_id=#{countryId},
            country_name=#{countryName},
            updater_no=#{updaterNo},
            update_time=now()
        </set>
        <where>
            `type`=#{type} and place_code = #{placeCode} and country_id = #{countryId} and is_valid = 1
        </where>
    </update>

    <update id="updateStationByIdAndNameAndCountry">
        update `travel_station`
        <set>
            place_code=#{placeCode},
            station_en_name=#{stationEnName},
            country_id=#{countryId},
            country_name=#{countryName},
            updater_no=#{updaterNo},
            update_time=now()
        </set>
        <where>
            `type`=#{type} and station_id=#{stationId} and country_id=#{countryId} and is_valid = 1
        </where>
    </update>

</mapper>
