<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhs.reimburse.xhsoa.mapper.TExpenseDetailMapper">

    <sql id="BaseColumn">
        id,
        form_type formType,
        form_num formNum,
        first_subject firstSubject,
        second_subject secondSubject,
        date_range dateRange,
        expense_abstract expenseAbstract,
        expense_content expenseContent,
        amount,
        is_sharing_cost isSharingCost,
        is_over_budget isOverBudget,
        submit_status submitStatus,
        is_removed isRemoved,
        batch_id batchId,
        creator_no creatorNo,
        creator,
        create_time createTime,
        updator_no updatorNo,
        updator,
        update_time updateTime,
        parent_id parentId,
        split_form_num splitFormNum,
        ticket_type ticketType
    </sql>

    <select id="queryByFormNum" resultType="com.xhs.reimburse.xhsoa.modal.TExpenseDetail">
        select
        <include refid="BaseColumn"/>
        from t_expense_detail
        where form_num = #{formNum}
        and is_removed = 0
    </select>

    <select id="queryByFormNumAndSecondSubjects" resultType="com.xhs.reimburse.xhsoa.modal.TExpenseDetail">
        select
        <include refid="BaseColumn"/>
        from t_expense_detail
        where form_num = #{formNum}
        and is_removed = 0
        <if test="secondSubjects != null and secondSubjects.size() > 0">
            and second_subject in
            <foreach collection="secondSubjects" item="secondSubject" separator="," open="(" close=")">
                #{secondSubject}
            </foreach>
        </if>
    </select>

</mapper>
