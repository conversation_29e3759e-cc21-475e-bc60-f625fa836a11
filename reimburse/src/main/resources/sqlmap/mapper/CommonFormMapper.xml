<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhs.reimburse.xhsoa.mapper.CommonFormMapper" >

    <sql id="BaseColumn">
        id
        id,
          form_num     formNum,
          form_type     formType,
          form_content     formContent,
          amount     amount,
          current_step     currentStep,
          current_audit_user     currentAuditUser,
          audit_status     auditStatus,
          is_write_back     isWriteBack,
          common_field   commonField,
          remark     remark,
          process_instance_id     processInstanceId,
          department_id   departmentId,
          working_place   workingPlace,
          is_valid        isValid,
          create_time     createTime,
          update_time     updateTime,
          creator_no     creatorNo,
          creator     creator,
          updator_no     updatorNo,
          updator     updator,
          be_entrusted_id beEntrustedId,
          be_entrusted_name beEntrustedName,
          payment_type   paymentType,
          pay_status     payStatus,
          sys_code       sysCode,
          business_id    businessId,
          curr_process_node currProcessNode,
          audit_type   auditType ,
          audit_id auditId,
          audit_type_name  auditTypeName,
          trans_form_type  transFormType,
          new_process  newProcess,
          v3_form  v3Form,
          form_type_id formTypeId,
          trans_xhsoa_v2  transXhsoaV2
    </sql>

    <sql id="AllBaseColumnWithAlias">
        cf.id     id,
          cf.form_num     formNum,
          cf.form_type     formType,
          cf.form_content     formContent,
          cf.amount     amount,
          cf.current_step     currentStep,
          cf.current_audit_user     currentAuditUser,
          cf.audit_status     auditStatus,
          cf.is_write_back     isWriteBack,
          cf.common_field   commonField,
          cf.remark     remark,
          cf.process_instance_id     processInstanceId,
          cf.department_id   departmentId,
          cf.working_place   workingPlace,
          cf.is_valid        isValid,
          cf.create_time     createTime,
          cf.update_time     updateTime,
          cf.creator_no     creatorNo,
          cf.creator     creator,
          cf.updator_no     updatorNo,
          cf.updator     updator,
          cf.be_entrusted_id beEntrustedId,
          cf.be_entrusted_name beEntrustedName,
          cf.payment_type   paymentType,
          cf.pay_status     payStatus
    </sql>

    <sql id="BaseColumnWithAlias">
        cf.id     id,
        cf.form_num     formNum,
        cf.form_type     formType,
        cf.form_content     formContent,
        cf.amount     amount,
        cf.current_step     currentStep,
        cf.current_audit_user     currentAuditUser,
        cf.audit_status     auditStatus,
        cf.is_write_back     isWriteBack,
        cf.common_field   commonField,
        cf.remark     remark,
        cf.process_instance_id     processInstanceId,
        cf.department_id   departmentId,
        cf.working_place   workingPlace,
        cf.create_time     createTime,
        cf.update_time     updateTime,
        cf.creator_no     creatorNo,
        cf.creator     creator,
        cf.updator_no     updatorNo,
        cf.updator     updator,
        cf.be_entrusted_id beEntrustedId,
        cf.be_entrusted_name beEntrustedName
    </sql>

    <sql id="baseSimpleFormColumn">
        cf.id           id,
          cf.form_num     formNum,
          cf.form_type     formType,
          cf.amount     amount,
          cf.current_step     currentStep,
          cf.current_audit_user     currentAuditUser,
          cf.audit_status     auditStatus,
          cf.remark     remark,
          cf.process_instance_id     processInstanceId,
          cf.department_id   departmentId,
          cf.working_place   workingPlace,
          cf.create_time     createTime,
          cf.update_time     auditReachTime,
          cf.creator_no     creatorNo,
          cf.creator     creator,
          cf.is_write_back     isWriteBack,
          cf.be_entrusted_id beEntrustedId,
          cf.be_entrusted_name beEntrustedName,
          cf.curr_process_node currProcessNode,
          cf.audit_type   auditType ,
          cf.audit_id auditId,
          cf.audit_type_name  auditTypeName,
          cf.common_field   commonField,
          cf.trans_form_type transFormType,
          cf.is_valid valid,
          cf.new_process newProcess
    </sql>

    <select id="batchForceSelectCommonFormByFormNums" resultType="com.xhs.reimburse.xhsoa.modal.CommonForm">
        select
        <include refid="BaseColumn"/>
        from common_form
        where form_num in
        <foreach collection="formNumList" open="(" close=")" separator="," item="formNum">
            #{formNum}
        </foreach>
    </select>


    <select id="queryEndCommonFrom" resultType="com.xhs.reimburse.xhsoa.modal.CommonForm">
        select
        <include refid="BaseColumn"/>
        from common_form
        <where>
            <if test="creatorNo != null">
                AND creator_no = #{creatorNo}
            </if>
            <if test="formType != null">
                AND form_type = #{formType}
            </if>
            <if test="auditStatus != null">
                AND audit_status = #{auditStatus}
            </if>
        </where>
    </select>

    <select id="queryTravelApplyForm" resultType="com.xhs.reimburse.xhsoa.modal.CommonForm">
        select <include refid="BaseColumnWithAlias"/>
        from common_form cf
        join travel_schedule_rel tsr on cf.form_num=tsr.form_num and tsr.is_valid=1
        join travel_schedule ts on tsr.schedule_form_num=ts.schedule_form_num
        where cf.is_valid = 1
        and cf.creator_no = #{paramsMap.userId}
        and cf.audit_status = #{paramsMap.auditStatus}
        and cf.form_type = #{paramsMap.formType}
        <if test="paramsMap.formNums!=null and paramsMap.formNums.size()>0">
            and cf.form_num in
            <foreach collection="paramsMap.formNums" item="formNum" separator="," open="(" close=")">
                #{formNum}
            </foreach>
        </if>
        and ts.schedule_status not in
        <foreach collection="paramsMap.scheduleStatusList" item="scheduleStatus" separator="," open="(" close=")">
            #{scheduleStatus}
        </foreach>

        UNION
        <!-- 可关联的单据包括「实际出行人」为该员工的单据 -->
        select <include refid="BaseColumnWithAlias"/>
        from common_form cf
        join travel_schedule_rel tsr on cf.form_num=tsr.form_num and tsr.is_valid=1
        join travel_schedule ts on tsr.schedule_form_num=ts.schedule_form_num
        join travel_schedule_passenger tsp on tsp.schedule_form_num = tsr.schedule_form_num and tsp.passenger_type=0 and tsp.is_valid = 1
        where cf.is_valid = 1
        and tsp.passenger_id = #{paramsMap.userId}
        and cf.audit_status = #{paramsMap.auditStatus}
        and cf.form_type = #{paramsMap.formType}
        <if test="paramsMap.formNums!=null and paramsMap.formNums.size()>0">
            and cf.form_num in
            <foreach collection="paramsMap.formNums" item="formNum" separator="," open="(" close=")">
                #{formNum}
            </foreach>
        </if>
        and ts.schedule_status not in
        <foreach collection="paramsMap.scheduleStatusList" item="scheduleStatus" separator="," open="(" close=")">
            #{scheduleStatus}
        </foreach>
        order by id desc
    </select>

    <select id="queryFormsByParam" resultType="com.xhs.reimburse.xhsoa.modal.CommonForm">
        select
        <include refid="BaseColumn"/>
        from common_form
        where is_valid = 1
        <if test="paramsMap.creatorNo != null">
            and creator_no = #{paramsMap.creatorNo}
        </if>
        <if test="paramsMap.formType != null and paramsMap.formType.size() > 0">
            and form_type in
            <foreach collection="paramsMap.formType" item="formType" separator="," open="(" close=")">
                #{formType}
            </foreach>
        </if>
        <if test="paramsMap.startTime != null">
            and create_time >= #{paramsMap.startTime}
        </if>
        <if test="paramsMap.endTime != null">
            and create_time &lt; #{paramsMap.endTime}
        </if>
        order by create_time desc
    </select>

</mapper>