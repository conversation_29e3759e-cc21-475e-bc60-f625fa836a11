package com.xhs.reimburse.assembler.mapper;

import com.xhs.reimburse.modal.dto.ReimbursementFormDto;
import com.xhs.reimburse.modal.entity.ReimbursementFormEntity;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-03-03T14:42:46+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 11.0.23 (Oracle Corporation)"
)
@Component
public class ReimbursementFormDtoMapperImpl implements ReimbursementFormDtoMapper {

    @Override
    public ReimbursementFormDto toDto(ReimbursementFormEntity entity) {
        if ( entity == null ) {
            return null;
        }

        ReimbursementFormDto reimbursementFormDto = new ReimbursementFormDto();

        reimbursementFormDto.setUuid( entity.getUuid() );
        reimbursementFormDto.setFormNum( entity.getFormNum() );
        reimbursementFormDto.setFormType( entity.getFormType() );
        reimbursementFormDto.setAmount( entity.getAmount() );
        reimbursementFormDto.setReimburseStatus( entity.getReimburseStatus() );
        reimbursementFormDto.setRemark( entity.getRemark() );
        reimbursementFormDto.setIsValid( entity.getIsValid() );
        reimbursementFormDto.setCreatorNo( entity.getCreatorNo() );
        reimbursementFormDto.setCreator( entity.getCreator() );
        reimbursementFormDto.setUpdaterNo( entity.getUpdaterNo() );
        reimbursementFormDto.setUpdater( entity.getUpdater() );
        reimbursementFormDto.setFormContent( entity.getFormContent() );

        return reimbursementFormDto;
    }

    @Override
    public List<ReimbursementFormDto> toDtoList(List<ReimbursementFormEntity> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ReimbursementFormDto> list = new ArrayList<ReimbursementFormDto>( entityList.size() );
        for ( ReimbursementFormEntity reimbursementFormEntity : entityList ) {
            list.add( toDto( reimbursementFormEntity ) );
        }

        return list;
    }

    @Override
    public List<ReimbursementFormEntity> toEntityList(List<ReimbursementFormDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ReimbursementFormEntity> list = new ArrayList<ReimbursementFormEntity>( dtoList.size() );
        for ( ReimbursementFormDto reimbursementFormDto : dtoList ) {
            list.add( toEntity( reimbursementFormDto ) );
        }

        return list;
    }

    @Override
    public ReimbursementFormEntity toEntity(ReimbursementFormDto dto) {
        if ( dto == null ) {
            return null;
        }

        ReimbursementFormEntity reimbursementFormEntity = new ReimbursementFormEntity();

        reimbursementFormEntity.setUuid( dto.getUuid() );
        reimbursementFormEntity.setFormNum( dto.getFormNum() );
        reimbursementFormEntity.setFormType( dto.getFormType() );
        reimbursementFormEntity.setAmount( dto.getAmount() );
        reimbursementFormEntity.setReimburseStatus( dto.getReimburseStatus() );
        reimbursementFormEntity.setRemark( dto.getRemark() );
        reimbursementFormEntity.setIsValid( dto.getIsValid() );
        reimbursementFormEntity.setCreatorNo( dto.getCreatorNo() );
        reimbursementFormEntity.setCreator( dto.getCreator() );
        reimbursementFormEntity.setUpdaterNo( dto.getUpdaterNo() );
        reimbursementFormEntity.setUpdater( dto.getUpdater() );
        reimbursementFormEntity.setFormContent( dto.getFormContent() );

        return reimbursementFormEntity;
    }
}
