package com.xhs.reimburse.assembler.mapper;

import com.xhs.reimburse.modal.dto.ExpenseDto;
import com.xhs.reimburse.modal.entity.ExpenseEntity;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-03-03T14:42:46+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 11.0.23 (Oracle Corporation)"
)
@Component
public class ExpenseDtoMapperImpl implements ExpenseDtoMapper {

    @Override
    public ExpenseDto toDto(ExpenseEntity entity) {
        if ( entity == null ) {
            return null;
        }

        ExpenseDto expenseDto = new ExpenseDto();

        expenseDto.setId( entity.getId() );
        expenseDto.setUuid( entity.getUuid() );
        expenseDto.setFormType( entity.getFormType() );
        expenseDto.setFirstSubject( entity.getFirstSubject() );
        expenseDto.setSecondSubject( entity.getSecondSubject() );
        expenseDto.setCreatorUserId( entity.getCreatorUserId() );
        expenseDto.setStatus( entity.getStatus() );
        expenseDto.setExpenseStatus( entity.getExpenseStatus() );
        expenseDto.setExpenseSource( entity.getExpenseSource() );
        expenseDto.setCreateTime( entity.getCreateTime() );
        expenseDto.setUpdateTime( entity.getUpdateTime() );

        return expenseDto;
    }

    @Override
    public List<ExpenseDto> toDtoList(List<ExpenseEntity> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ExpenseDto> list = new ArrayList<ExpenseDto>( entityList.size() );
        for ( ExpenseEntity expenseEntity : entityList ) {
            list.add( toDto( expenseEntity ) );
        }

        return list;
    }

    @Override
    public ExpenseEntity toEntity(ExpenseDto dto) {
        if ( dto == null ) {
            return null;
        }

        ExpenseEntity expenseEntity = new ExpenseEntity();

        expenseEntity.setId( dto.getId() );
        expenseEntity.setUuid( dto.getUuid() );
        expenseEntity.setFormType( dto.getFormType() );
        expenseEntity.setFirstSubject( dto.getFirstSubject() );
        expenseEntity.setSecondSubject( dto.getSecondSubject() );
        expenseEntity.setCreatorUserId( dto.getCreatorUserId() );
        expenseEntity.setCreateTime( dto.getCreateTime() );
        expenseEntity.setUpdateTime( dto.getUpdateTime() );
        expenseEntity.setStatus( dto.getStatus() );
        expenseEntity.setExpenseStatus( dto.getExpenseStatus() );
        expenseEntity.setExpenseSource( dto.getExpenseSource() );

        return expenseEntity;
    }

    @Override
    public List<ExpenseEntity> toEntityList(List<ExpenseDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ExpenseEntity> list = new ArrayList<ExpenseEntity>( dtoList.size() );
        for ( ExpenseDto expenseDto : dtoList ) {
            list.add( toEntity( expenseDto ) );
        }

        return list;
    }
}
