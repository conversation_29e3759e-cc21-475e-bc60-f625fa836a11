package com.xhs.reimburse.assembler.mapper;

import com.xhs.reimburse.modal.dto.InvoiceDto;
import com.xhs.reimburse.modal.entity.InvoiceEntity;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-03-03T14:42:46+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 11.0.23 (Oracle Corporation)"
)
@Component
public class InvoiceDtoMapperImpl implements InvoiceDtoMapper {

    @Override
    public InvoiceDto toDto(InvoiceEntity entity) {
        if ( entity == null ) {
            return null;
        }

        InvoiceDto invoiceDto = new InvoiceDto();

        invoiceDto.setId( entity.getId() );
        invoiceDto.setUuid( entity.getUuid() );
        invoiceDto.setTicketType( entity.getTicketType() );
        invoiceDto.setInvoiceNo( entity.getInvoiceNo() );
        invoiceDto.setInvoiceCode( entity.getInvoiceCode() );
        invoiceDto.setInvoiceType( entity.getInvoiceType() );
        invoiceDto.setInvoiceValidateResult( entity.getInvoiceValidateResult() );
        invoiceDto.setInvoiceValidateFailReason( entity.getInvoiceValidateFailReason() );
        invoiceDto.setInvoiceStatus( entity.getInvoiceStatus() );
        invoiceDto.setStatus( entity.getStatus() );
        invoiceDto.setCreatorUserId( entity.getCreatorUserId() );
        invoiceDto.setInvoiceSource( entity.getInvoiceSource() );
        invoiceDto.setOcrContentModified( entity.getOcrContentModified() );
        invoiceDto.setCreateTime( entity.getCreateTime() );
        invoiceDto.setUpdateTime( entity.getUpdateTime() );
        invoiceDto.setOcrSourceContent( entity.getOcrSourceContent() );

        return invoiceDto;
    }

    @Override
    public List<InvoiceDto> toDtoList(List<InvoiceEntity> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<InvoiceDto> list = new ArrayList<InvoiceDto>( entityList.size() );
        for ( InvoiceEntity invoiceEntity : entityList ) {
            list.add( toDto( invoiceEntity ) );
        }

        return list;
    }

    @Override
    public InvoiceEntity toEntity(InvoiceDto dto) {
        if ( dto == null ) {
            return null;
        }

        InvoiceEntity invoiceEntity = new InvoiceEntity();

        invoiceEntity.setId( dto.getId() );
        invoiceEntity.setUuid( dto.getUuid() );
        invoiceEntity.setInvoiceType( dto.getInvoiceType() );
        invoiceEntity.setInvoiceCode( dto.getInvoiceCode() );
        invoiceEntity.setInvoiceNo( dto.getInvoiceNo() );
        invoiceEntity.setTicketType( dto.getTicketType() );
        invoiceEntity.setInvoiceValidateResult( dto.getInvoiceValidateResult() );
        invoiceEntity.setInvoiceValidateFailReason( dto.getInvoiceValidateFailReason() );
        invoiceEntity.setCreatorUserId( dto.getCreatorUserId() );
        invoiceEntity.setCreateTime( dto.getCreateTime() );
        invoiceEntity.setUpdateTime( dto.getUpdateTime() );
        invoiceEntity.setStatus( dto.getStatus() );
        invoiceEntity.setInvoiceStatus( dto.getInvoiceStatus() );
        invoiceEntity.setOcrSourceContent( dto.getOcrSourceContent() );
        invoiceEntity.setOcrContentModified( dto.getOcrContentModified() );
        invoiceEntity.setInvoiceSource( dto.getInvoiceSource() );

        return invoiceEntity;
    }

    @Override
    public List<InvoiceEntity> toEntityList(List<InvoiceDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<InvoiceEntity> list = new ArrayList<InvoiceEntity>( dtoList.size() );
        for ( InvoiceDto invoiceDto : dtoList ) {
            list.add( toEntity( invoiceDto ) );
        }

        return list;
    }
}
