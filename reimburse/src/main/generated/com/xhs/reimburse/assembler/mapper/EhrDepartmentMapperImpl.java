package com.xhs.reimburse.assembler.mapper;

import com.xhs.ehr.rpc.response.DepartmentInfo;
import com.xhs.reimburse.modal.entity.EhrDepartmentEntity;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-03-03T14:42:46+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 11.0.23 (Oracle Corporation)"
)
@Component
public class EhrDepartmentMapperImpl implements EhrDepartmentMapper {

    @Override
    public EhrDepartmentEntity baseToEntity(DepartmentInfo departmentInfo) {
        if ( departmentInfo == null ) {
            return null;
        }

        EhrDepartmentEntity ehrDepartmentEntity = new EhrDepartmentEntity();

        ehrDepartmentEntity.setDepartmentId( departmentInfo.getDepartmentId() );
        ehrDepartmentEntity.setParentId( departmentInfo.getParentId() );
        ehrDepartmentEntity.setDepartmentCode( departmentInfo.getDepartmentCode() );
        ehrDepartmentEntity.setDepartmentName( departmentInfo.getDepartmentName() );
        ehrDepartmentEntity.setLevel( departmentInfo.getLevel() );
        ehrDepartmentEntity.setIsLeaf( departmentInfo.getIsLeaf() );
        ehrDepartmentEntity.setDepartmentStatus( departmentInfo.getDepartmentStatus() );
        ehrDepartmentEntity.setDepartmentIdPath( departmentInfo.getDepartmentIdPath() );
        ehrDepartmentEntity.setDepartmentNamePath( departmentInfo.getDepartmentNamePath() );
        ehrDepartmentEntity.setDepartmentLeader( departmentInfo.getDepartmentLeader() );
        ehrDepartmentEntity.setFirstLevelDepartmentId( departmentInfo.getFirstLevelDepartmentId() );
        ehrDepartmentEntity.setSecondLevelDepartmentId( departmentInfo.getSecondLevelDepartmentId() );
        ehrDepartmentEntity.setHrbp( departmentInfo.getHrbp() );
        ehrDepartmentEntity.setHrbpLeader( departmentInfo.getHrbpLeader() );
        ehrDepartmentEntity.setSalaryLeader( departmentInfo.getSalaryLeader() );
        ehrDepartmentEntity.setHrManager( departmentInfo.getHrManager() );
        ehrDepartmentEntity.setIsValid( departmentInfo.getIsValid() );

        return ehrDepartmentEntity;
    }

    @Override
    public List<EhrDepartmentEntity> baseToEntity(List<DepartmentInfo> departmentInfos) {
        if ( departmentInfos == null ) {
            return null;
        }

        List<EhrDepartmentEntity> list = new ArrayList<EhrDepartmentEntity>( departmentInfos.size() );
        for ( DepartmentInfo departmentInfo : departmentInfos ) {
            list.add( baseToEntity( departmentInfo ) );
        }

        return list;
    }
}
