package com.xhs.reimburse.assembler.mapper;

import com.xhs.reimburse.modal.dto.BankAccountDto;
import com.xhs.reimburse.modal.entity.BankAccountEntity;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-03-03T14:42:46+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 11.0.23 (Oracle Corporation)"
)
@Component
public class BankAccountDtoMapperImpl implements BankAccountDtoMapper {

    @Override
    public BankAccountDto toDto(BankAccountEntity entity) {
        if ( entity == null ) {
            return null;
        }

        BankAccountDto bankAccountDto = new BankAccountDto();

        bankAccountDto.setBankName( entity.getBankName() );
        bankAccountDto.setAccountName( entity.getAccountName() );
        bankAccountDto.setAccountNo( entity.getAccountNo() );
        bankAccountDto.setId( entity.getId() );
        bankAccountDto.setBankCode( entity.getBankCode() );
        bankAccountDto.setIsDefault( entity.getIsDefault() );

        return bankAccountDto;
    }

    @Override
    public List<BankAccountDto> toDtoList(List<BankAccountEntity> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<BankAccountDto> list = new ArrayList<BankAccountDto>( entityList.size() );
        for ( BankAccountEntity bankAccountEntity : entityList ) {
            list.add( toDto( bankAccountEntity ) );
        }

        return list;
    }

    @Override
    public BankAccountEntity toEntity(BankAccountDto dto) {
        if ( dto == null ) {
            return null;
        }

        BankAccountEntity bankAccountEntity = new BankAccountEntity();

        bankAccountEntity.setId( dto.getId() );
        bankAccountEntity.setBankName( dto.getBankName() );
        bankAccountEntity.setBankCode( dto.getBankCode() );
        bankAccountEntity.setAccountName( dto.getAccountName() );
        bankAccountEntity.setAccountNo( dto.getAccountNo() );
        bankAccountEntity.setIsDefault( dto.getIsDefault() );

        return bankAccountEntity;
    }

    @Override
    public List<BankAccountEntity> toEntityList(List<BankAccountDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<BankAccountEntity> list = new ArrayList<BankAccountEntity>( dtoList.size() );
        for ( BankAccountDto bankAccountDto : dtoList ) {
            list.add( toEntity( bankAccountDto ) );
        }

        return list;
    }
}
