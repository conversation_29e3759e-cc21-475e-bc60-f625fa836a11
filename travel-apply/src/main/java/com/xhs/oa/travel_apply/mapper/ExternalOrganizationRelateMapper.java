package com.xhs.oa.travel_apply.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xhs.oa.office.exception.BusinessException;
import com.xhs.oa.travel_apply.model.entity.ExternalOrganizationRelateEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

import static com.xhs.oa.travel_apply.constant.DidiConstant.*;

/**
 * <AUTHOR>
 * @date :2025/04/16 - 上午11:42
 * @description :
 */
@Mapper
public interface ExternalOrganizationRelateMapper extends BaseMapper<ExternalOrganizationRelateEntity> {
    
    /**
     * 查询所有有效的滴滴用户关联数据
     * @param busType 业务类型
     * @param relateType 关联类型
     * @return 关联数据列表
     */
    default List<ExternalOrganizationRelateEntity> queryAllSourceIdByType(String busType, String relateType) {
        return selectList(new LambdaQueryWrapper<ExternalOrganizationRelateEntity>()
                .eq(ExternalOrganizationRelateEntity::getBusType, busType)
                .eq(ExternalOrganizationRelateEntity::getRelateType, relateType)
                .eq(ExternalOrganizationRelateEntity::getIsValid, 1));
    }

    /**
     * 逻辑删除关联数据
     * @param id 关联ID
     * @return 影响行数
     */
    default int updateDeleteById(Long id) {
        ExternalOrganizationRelateEntity entity = new ExternalOrganizationRelateEntity();
        entity.setId(id);
        entity.setIsValid(0);
        return updateById(entity);
    }


    default ExternalOrganizationRelateEntity queryRelation(String userId) {
        LambdaQueryWrapper<ExternalOrganizationRelateEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ExternalOrganizationRelateEntity::getSourceId, userId)
                .eq(ExternalOrganizationRelateEntity::getIsValid, EXTERNAL_DIDI_VALID)
                .eq(ExternalOrganizationRelateEntity::getRelateType, EXTERNAL_DIDI_RELATE_TYPE)
                .eq(ExternalOrganizationRelateEntity::getBusType, EXTERNAL_DIDI_BUS_TYPE);
        return selectOne(queryWrapper);
    }


    default void deleteRelation(String userId) {
        ExternalOrganizationRelateEntity entity = queryRelation(userId);
        if (entity == null) {
            throw new BusinessException("未找到对应关联关系");
        }

        entity.setIsValid(EXTERNAL_DIDI_INVALID);
        updateById(entity);
    }


    default void addRelation(String userId, String didiUserId) {
        ExternalOrganizationRelateEntity entity = queryRelation(userId);
        if (entity != null) {
            throw new BusinessException("对应关联关系已存在");
        }

        entity = new ExternalOrganizationRelateEntity();
        entity.setSourceId(userId);
        entity.setRelateType(EXTERNAL_DIDI_RELATE_TYPE);
        entity.setTargetId(didiUserId);
        entity.setBusType(EXTERNAL_DIDI_BUS_TYPE);
        entity.setIsValid(EXTERNAL_DIDI_VALID);
        insert(entity);
    }
}