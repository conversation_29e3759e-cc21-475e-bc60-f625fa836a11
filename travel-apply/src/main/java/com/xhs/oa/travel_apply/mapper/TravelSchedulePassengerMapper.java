package com.xhs.oa.travel_apply.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xhs.oa.travel_apply.model.entity.TravelSchedulePassengerEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 差旅行程出行人Mapper
 */
@Mapper
public interface TravelSchedulePassengerMapper extends BaseMapper<TravelSchedulePassengerEntity> {

    /**
     * 根据行程单号查询出行人列表
     *
     * @param scheduleFormNumList 行程单号
     * @param passengerType
     * @return 出行人列表
     */
    default List<TravelSchedulePassengerEntity> queryByScheFormNumPassengerType(String scheduleFormNumList, int passengerType) {
        return selectList(new LambdaQueryWrapper<TravelSchedulePassengerEntity>()
                .eq(TravelSchedulePassengerEntity::getScheduleFormNum, scheduleFormNumList)
                .eq(TravelSchedulePassengerEntity::getPassengerType, passengerType)
                .eq(TravelSchedulePassengerEntity::getIsValid, 1));
    }
}
