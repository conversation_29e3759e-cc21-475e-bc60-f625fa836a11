package com.xhs.oa.travel_apply.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date :2025/04/16 - 上午11:56
 * @description :
 */
@Getter
@AllArgsConstructor
public enum EmployTypeEnum {
    INNER_EMP("0","正式员工"),
    OUTTER_EMP("1","供应商外包"),
    INTERNSHIP_EMP("2","实习生"),
    DISPATCHED_EMP("3","编外人员"),
    OUTER_BPO("5","BPO"),
    ;


    private String code;
    private String desc;
}
