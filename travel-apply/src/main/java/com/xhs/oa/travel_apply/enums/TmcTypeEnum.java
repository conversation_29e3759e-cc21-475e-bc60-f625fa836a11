package com.xhs.oa.travel_apply.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * TMC类型枚举
 */
@Getter
@AllArgsConstructor
public enum TmcTypeEnum {
    
    CTRIP(1, "携程"),
    DIDI(2, "滴滴"),
    OTHER(3, "其他");
    
    private final Integer code;
    private final String description;
    
    /**
     * 根据code获取枚举
     */
    public static TmcTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (TmcTypeEnum tmcType : values()) {
            if (tmcType.getCode().equals(code)) {
                return tmcType;
            }
        }
        return null;
    }
}
