package com.xhs.oa.travel_apply.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date :2025/04/16 - 上午11:56
 * @description :
 */
@Getter
@AllArgsConstructor
public enum EmployeeChangeEnum {
    CREATE_EMPLOYEE("E_CREATE_EMPLOYEE" , "创建账号"),
    MERGE_EMPLOYEE("E_MERGE_EMPLOYEE" , "账号合并"),
    UPDATE_EMPLOYEE_INFO("E_UPDATE_EMPLOYEE_INFO" , "修改员工信息"),
    CONFIRM_ENTRY("E_CONFIRM_ENTRY" , "确认入职"),
    CANCEL_ENTRY("E_CANCEL_ENTRY" , "取消入职"),
    E_DIMISSION("E_DIMISSION" , "离职");

    private String code;
    private String desc;
}
