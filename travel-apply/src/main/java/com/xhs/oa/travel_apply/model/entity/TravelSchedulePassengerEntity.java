package com.xhs.oa.travel_apply.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 差旅行程出行人实体
 */
@Data
@TableName("travel_schedule_passenger")
public class TravelSchedulePassengerEntity {

    /**
     * 主键，自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 行程单号
     */
    private String scheduleFormNum;

    /**
     * 出行人类型 0：出行人 1：同住人
     */
    private Integer passengerType;

    /**
     * 出行人id
     */
    private String passengerId;

    /**
     * 出行人姓名
     */
    private String passengerName;

    /**
     * 是否生效 0：无效 1：有效
     */
    private Integer isValid;

    /**
     * 创建人id
     */
    private String creatorNo;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人id
     */
    private String updaterNo;

    /**
     * 更新时间
     */
    private Date updateTime;
}
