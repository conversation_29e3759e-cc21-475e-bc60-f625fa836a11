package com.xhs.oa.travel_apply.model.vo;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class UserVo {

    /**
     * 人员根节点id
     */
    public static final String ROOT_LEADER_ID = "*********";

    @ApiModelProperty(notes = "用户id")
    private String userID;

    @ApiModelProperty(notes = "姓名")
    private String userName;

    @ApiModelProperty(notes = "薯名")
    private String redName;

    @ApiModelProperty(notes = "邮箱")
    private String email;

    @ApiModelProperty(notes = "薯名邮箱")
    private String redEmail;

    @ApiModelProperty(notes = "部门")
    private String department;

    @ApiModelProperty(notes = "部门Id")
    private Long departmentId;

    @ApiModelProperty(notes = "工作地点")
    private String workingPlace;

    @ApiModelProperty(notes = "工作地点Code")
    private String workingPlaceCode;

    @ApiModelProperty(notes = "员工状态")
    private Integer accountStatus;

    @ApiModelProperty(notes = "员工状态描述")
    private String accountStatusDesc;

    @ApiModelProperty(notes = "直属上级")
    private String leader;

    @ApiModelProperty(notes = "直属上级Id")
    private Long leaderId;

    @ApiModelProperty(notes = "手机号码")
    private String mobilePhone;
    @ApiModelProperty(notes = "身份证号")
    private String idNumber;
    @ApiModelProperty(notes = "分公司code")
    private String companyCode;

    @ApiModelProperty("string类型部门路径")
    private String deptIdsPath;

    @ApiModelProperty(notes = "部门id路径")
    private String[] departmentIdsPath;

    @ApiModelProperty(notes = "部门名称路径")
    private String[] departmentNamesPath;

    @ApiModelProperty(notes = "leadId路径")
    private String leaderIdsPath;

    @ApiModelProperty(notes = "公司名称")
    private String companyName;

    @ApiModelProperty(notes = "0 内部员工 1 外部人员 2 实习生 默认查询内部员工")
    private Integer employType;

    @ApiModelProperty("滴滴制度ID列表")
    private List<String> regulationIdList;

    @ApiModelProperty("竞业状态")
    private Byte competeFlag;
}
