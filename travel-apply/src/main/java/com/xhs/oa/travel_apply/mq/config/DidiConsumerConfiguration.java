package com.xhs.oa.travel_apply.mq.config;

import com.xhs.oa.travel_apply.mq.consumer.EhrEmployeeChangeProcessor;
import com.xiaohongshu.events.client.consumer.EventsPushConsumer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date :2025/02/25 - 下午9:09
 * @description :
 */
@Slf4j
@Configuration
public class DidiConsumerConfiguration {
    // 生产者所属topic
    private static final String EHR_EMPLOYEE_CHANGE_TOPIC = "ehr-employee-change-notice";
    // 消费者所属group
    private static final String EHR_EMPLOYEE_CHANGE_GROUP = "ehr-employee-change-notice-oaoffice-consumer";
    // 已在event平台指定tags

    @Bean(name = "EhrEmployeeChangeConsumer")
    public EventsPushConsumer EhrEmployeeChangeConsumer(EhrEmployeeChangeProcessor ehrEmployeeChangeProcessor) {
        EventsPushConsumer consumer = new EventsPushConsumer();
        consumer.setTopic(EHR_EMPLOYEE_CHANGE_TOPIC);
        consumer.setGroup(EHR_EMPLOYEE_CHANGE_GROUP);
        consumer.setMessageProcessor(ehrEmployeeChangeProcessor);
        consumer.start();
        log.info("------------------ EhrEmployeeChangeConsumer MQ启动成功！------------------");
        return consumer;
    }
}
