package com.xhs.oa.travel_apply.job;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.xhs.ehr.rpc.response.EmployeeInfo;
import com.xhs.ehr.rpc.response.EmployeeRecordInfo;
import com.xhs.ehr_public.rpc.request.QueryEmployeePrivateInfoByEmailsRequest;
import com.xhs.ehr_public.rpc.response.BatchQueryEmployeePrivateInfoResponse;
import com.xhs.ehr_public.rpc.response.EmployeePrivateInfo;
import com.xhs.ehr_public.rpc.service.EhrEmployeePrivateInfoService;
import com.xhs.oa.office.rpc.EmployeeCommonService;
import com.xhs.oa.office.rpc.EmployeeRecordService;
import com.xhs.oa.travel_apply.constant.CommonConstant;
import com.xhs.oa.travel_apply.enums.RelateTypeEnum;
import com.xhs.oa.travel_apply.mapper.ExternalOrganizationRelateMapper;
import com.xhs.oa.travel_apply.mapper.OaUserMapper;
import com.xhs.oa.travel_apply.model.entity.ExternalOrganizationRelateEntity;
import com.xhs.oa.travel_apply.model.entity.OaUserEntity;
import com.xhs.oa.travel_apply.model.vo.DidiProperty;
import com.xhs.oa.travel_apply.model.vo.UserPageQuery;
import com.xhs.oa.travel_apply.model.vo.UserVo;
import com.xhs.oa.travel_apply.rpc.DidiRpc;
import com.xhs.oa.travel_apply.utils.DidiSyncSwitch;
import com.xhs.oa.travel_apply.utils.EncryptUtil;
import com.xiaohongshu.infra.concurrent.PlatformExecutors;
import com.xiaohongshu.infra.redschedule.api.Parameter;
import com.xiaohongshu.infra.redschedule.api.RedSchedule;
import com.xiaohongshu.infra.rpc.base.Context;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.testng.util.Strings;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

import static com.xhs.oa.travel_apply.constant.DidiConstant.EXTERNAL_DIDI_BUS_TYPE;
import static com.xhs.oa.travel_apply.enums.EmployTypeEnum.INNER_EMP;
import static com.xhs.oa.travel_apply.enums.EmployTypeEnum.INTERNSHIP_EMP;

/**
 * 滴滴用户数据同步任务
 * 负责将内部员工数据同步到滴滴系统
 */
@Component
@Slf4j
public class DidiUserSyncJob {
    private static final Executor executor = PlatformExecutors.dynamicExecutor("didi-user-sync-", 2, 2, 60, true, new ThreadPoolExecutor.CallerRunsPolicy());
    public static final String INTERN_NAME = "（实习）";
    @Autowired
    private DidiRpc didiRpc;
    @Autowired
    private ExternalOrganizationRelateMapper externalOrganizationRelateMapper;
    @Autowired
    private OaUserMapper oaUserMapper;
    @Autowired
    private DidiProperty didiProperty;
    @Autowired
    private EmployeeRecordService employeeRecordService;
    @Autowired
    private EhrEmployeePrivateInfoService.Iface ehrIface;
    @Autowired
    private EmployeeCommonService employeeCommonService;


    private static void setSyncSwitch(Parameter parameter) {
        // 检查是否设置了允许真实同步的参数
        String enableRealSync = parameter.getString("enableRealSync");
        if (enableRealSync != null && Objects.equals("true", enableRealSync)) {
            DidiSyncSwitch.setSyncEnabled(true);
        } else {
            DidiSyncSwitch.setSyncEnabled(false);
        }
    }

    /**
     * 执行滴滴用户数据同步
     * 每天执行一次，同步内部员工数据到滴滴系统
     */
    @RedSchedule(value = "oaoffice.didiUserSyncJob", cron = "0 0 * * * ?", desc = "滴滴对接的用户数据同步 {\"userIds\":\"\", \"enableRealSync\":\"\"}")
    public void execute(Parameter parameter) {
        try {
            setSyncSwitch(parameter);

            String userIds = parameter.getString("userIds");
            List<UserVo> syncedUserInfos;
            if (!Strings.isNullOrEmpty(userIds)) {
                String[] split = userIds.split(",");
                List<OaUserEntity> oaUserEntities = oaUserMapper.queryAvailableUserListByIds(Arrays.asList(split));
                syncedUserInfos = oaUserEntities.stream().map(OaUserEntity::convertToUserVo).collect(Collectors.toList());
                synByUserVo(syncedUserInfos);
                return;
            }
            syncedUserInfos = queryEmployees();
            if (CollectionUtils.isEmpty(syncedUserInfos)) {
                return;
            }
            syncedUserInfos = synByUserVo(syncedUserInfos);
            handleUserDeletion(syncedUserInfos);
        } finally {
            // 任务执行完毕后清除ThreadLocal，避免内存泄漏
            DidiSyncSwitch.clear();
        }
    }

    // 根据用户信息同步
    public List<UserVo> synByUserVo(List<UserVo> syncedUserInfos) {
        if (CollectionUtils.isEmpty(syncedUserInfos)) {
            return syncedUserInfos;
        }
        syncedUserInfos.forEach(vo -> {
            String userName = vo.getUserName();
            if (userName.contains(INTERN_NAME)) { // 实习生DB中存储的名字带有实习两个字 同步时需要去掉
                vo.setUserName(userName.replace(INTERN_NAME, ""));
            }
        });
        List<List<UserVo>> partitions = Lists.partition(syncedUserInfos, 50);
        CompletableFuture<Void> allTasks = CompletableFuture.allOf(partitions.stream()
                .map(partUsers -> CompletableFuture.runAsync(() -> {
                    try {
                        // 设置竞业标志
                        List<UserVo> processedUsers = setCompeteFlag(partUsers);
                        List<UserVo> phoneUsers = setMobilePhone(processedUsers);
                        // 同步到滴滴
                        didiRpc.syncUsersToDidi(phoneUsers);
                        log.info("成功同步 {} 个用户到滴滴", phoneUsers.size());
                    } catch (Exception e) {
                        log.error("批次用户同步失败，批次大小: {}, 错误: {}", partUsers.size(), e.getMessage(), e);
                    }
                }, executor)).toArray(CompletableFuture[]::new));
        try {
            allTasks.join(); // 等待所有任务完成
            log.info("所有用户同步任务完成，总用户数: {}", syncedUserInfos.size());
        } catch (Exception e) {
            log.error("等待用户同步任务完成时发生异常: {}", e.getMessage(), e);
        }
        return syncedUserInfos;
    }


    private List<UserVo> setMobilePhone(List<UserVo> userVos) {
        List<String> userEmails = userVos.stream().map(UserVo::getEmail).collect(Collectors.toList());
        try {
            QueryEmployeePrivateInfoByEmailsRequest request = new QueryEmployeePrivateInfoByEmailsRequest();
            request.setEmployeeEmailList(userEmails);
            request.setEnterpriseId(10000L);
            BatchQueryEmployeePrivateInfoResponse response = ehrIface.batch_query_employee_private_info_by_emails(new Context(), request);
            if (!response.isSuccess()) {
                log.error("批量查询员工信息失败,req:{}, resp:{}", JSON.toJSONString(request), JSON.toJSONString(request));
                return userVos;
            }
            List<EmployeePrivateInfo> employeeInfoList = response.getEmployeeInfoList();
            employeeInfoList.forEach(employeePrivateInfo -> {
                String userId = String.valueOf(employeePrivateInfo.getEmployeeId());
                for (UserVo userVo : userVos) {
                    if (!userId.equals(userVo.getUserID())) continue;
                    String phoneNationCode = employeePrivateInfo.getPhoneNationCode();
                    log.info("user id:{}, phone nation code:{}", userId, phoneNationCode);
                    if (!Strings.isNullOrEmpty(phoneNationCode) && !phoneNationCode.contains("86")) { // 为了减少影响范围非86的才处理
                        if (!phoneNationCode.contains("+")) {
                            phoneNationCode = "+" + phoneNationCode;
                        }
                        String mobilePhone = phoneNationCode + " " + employeePrivateInfo.getMobilePhone();
                        String encrypt = EncryptUtil.encrypt(mobilePhone, CommonConstant.USER_INFO_SECRET_KEY);
                        userVo.setMobilePhone(encrypt);
                    }
                }
            });
        } catch (TException e) {
            log.error("批量查询员工信息失败,req:{}", JSON.toJSONString(userEmails), e);
        }
        return userVos;
    }

    // 过滤掉竞业员工
    private List<UserVo> setCompeteFlag(List<UserVo> syncedUser) {
        List<String> userIdList = syncedUser.stream().map(UserVo::getUserID).collect(Collectors.toList());
        List<EmployeeRecordInfo> employeeRecordInfos = employeeRecordService.queryCurrentEmployeeRecordByStrIds(userIdList);
        List<EmployeeInfo> employeeInfos = employeeCommonService.batchQueryEmployee(userIdList, false);
        Map<Long, EmployeeInfo> employeeInfoMap = employeeInfos.stream()
                .collect(Collectors.toMap(
                        EmployeeInfo::getEmployeeId,
                        employeeInfo -> employeeInfo
                ));
        // employeeRecordInfos competeFlag 转换为map，key为userId，value为competeFlag
        Map<Long, Byte> userCompeteMap = employeeRecordInfos.stream()
                .collect(Collectors.toMap(
                        EmployeeRecordInfo::getEmployeeId,
                        EmployeeRecordInfo::getCompeteFlag,
                        (v1, v2) -> v1
                ));

        // 循环syncedUser如果competeFlag==1为竞业员工
        syncedUser.forEach(user -> {
            String userID = user.getUserID();
            if (!EmployeeCommonService.str2Long(userID)) {
                return;
            }
            long key = Long.parseLong(userID);
            Byte competeFlag = userCompeteMap.get(key);
            if (competeFlag != null) {
                user.setCompeteFlag(competeFlag);
            }
            EmployeeInfo employeeInfo = employeeInfoMap.get(key);
            if(employeeInfo==null){
                log.error("employee info is null, user id:{}", userID);
                return;
            }
            user.setUserName(employeeInfo.getUserName());
        });
        return syncedUser;
    }

    /**
     * 同步员工数据: 包含正式员工、实习生以及白名单编外人员
     *
     * @return 已同步的用户ID列表
     */
    private List<UserVo> queryEmployees() {
        List<UserVo> syncedUser = new ArrayList<>();

        // 1. 同步正式员工和实习生
        syncInternalEmployees(syncedUser);

        // 2. 同步白名单内的编外人员
        syncWhitelistExternalEmployees(syncedUser);

        return syncedUser;
    }

    /**
     * 同步正式员工和实习生数据
     */
    private void syncInternalEmployees(List<UserVo> syncedUserIds) {
        UserPageQuery query = buildUserQuery();
        int pageNum = 1;
        List<UserVo> userVoList;

        do {
            query.setPageNum(pageNum);
            userVoList = oaUserMapper.queryUserVoInfoByPage(query);
            syncedUserIds.addAll(userVoList);
            pageNum++;
        } while (CollectionUtils.isNotEmpty(userVoList));
    }

    /**
     * 同步白名单内的编外人员
     */
    private void syncWhitelistExternalEmployees(List<UserVo> syncedUserIds) {
        if (CollectionUtils.isEmpty(didiProperty.getTravelDidiTrainDispatchEmp())) {
            return;
        }

        List<OaUserEntity> externalUsers = oaUserMapper.queryAvailableUserListByIds(didiProperty.getTravelDidiTrainDispatchEmp());
        if (CollectionUtils.isEmpty(externalUsers)) {
            return;
        }
        List<UserVo> collect = externalUsers.stream().map(OaUserEntity::convertToUserVo).collect(Collectors.toList());
        syncedUserIds.addAll(collect);
    }

    /**
     * 构建用户查询条件
     */
    private UserPageQuery buildUserQuery() {
        UserPageQuery query = new UserPageQuery();
        query.setAccountStatus(1);
        // 正式员工、实习生
        query.setEmployTypes(Lists.newArrayList(
                Integer.valueOf(INNER_EMP.getCode()),
                Integer.valueOf(INTERNSHIP_EMP.getCode())
        ));
        query.setPageSize(100);
        return query;
    }

    /**
     * 处理需要删除的用户数据
     */
    private void handleUserDeletion(List<UserVo> syncedUserInfos) {
        List<ExternalOrganizationRelateEntity> needDeleteList = findUsersToDelete(syncedUserInfos);
        if (CollectionUtils.isEmpty(needDeleteList)) {
            return;
        }
        List<String> needDelUserList = needDeleteList.stream().map(ExternalOrganizationRelateEntity::getSourceId).collect(Collectors.toList());
        // 在职的正式员工不删除
        Set<String> cannotDel = oaUserMapper.queryAvailableUserListByIds(needDelUserList)
                .stream().filter(user -> Objects.equals(user.getEmployType(), INNER_EMP.getCode()))
                .map(OaUserEntity::getUserId)
                .collect(Collectors.toSet());
        for (ExternalOrganizationRelateEntity relate : needDeleteList) {
            if (cannotDel.contains(relate.getSourceId())) {
                continue;
            }
            try {
                log.info("需要删除的滴滴用户数据:{}", relate.getSourceId());
                didiRpc.deleteUser(relate.getSourceId());
                externalOrganizationRelateMapper.updateDeleteById(relate.getId());
            } catch (Exception e) {
                log.error("删除滴滴用户数据失败:", e);
            }
        }
    }

    /**
     * 查找需要删除的用户数据
     */
    private List<ExternalOrganizationRelateEntity> findUsersToDelete(List<UserVo> syncedUserInfos) {
        List<ExternalOrganizationRelateEntity> relateList = externalOrganizationRelateMapper
                .queryAllSourceIdByType(EXTERNAL_DIDI_BUS_TYPE, RelateTypeEnum.USER.getType());
        List<String> userIdList = syncedUserInfos.stream().map(UserVo::getUserID).collect(Collectors.toList());
        return relateList.stream()
                .filter(v -> !userIdList.contains(v.getSourceId()))
                .collect(Collectors.toList());
    }


}
