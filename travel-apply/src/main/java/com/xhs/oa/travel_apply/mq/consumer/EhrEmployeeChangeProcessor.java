package com.xhs.oa.travel_apply.mq.consumer;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xhs.oa.travel_apply.job.DidiUserSyncJob;
import com.xhs.oa.travel_apply.mapper.OaUserMapper;
import com.xhs.oa.travel_apply.model.entity.OaUserEntity;
import com.xhs.oa.travel_apply.model.vo.UserVo;
import com.xhs.oa.travel_apply.rpc.DidiRpc;
import com.xiaohongshu.events.client.MessageExt;
import com.xiaohongshu.events.client.api.MessageProcessor;
import com.xiaohongshu.events.client.consumer.ConsumeContext;
import com.xiaohongshu.events.client.consumer.ConsumeStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.testng.collections.Lists;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.List;

import static com.xhs.oa.travel_apply.constant.EhrConstant.*;
import static com.xhs.oa.travel_apply.enums.EmployeeChangeEnum.*;

/**
 * <AUTHOR>
 * @date :2025/04/16 - 下午7:22
 * @description :
 */
@Slf4j
@Component
public class EhrEmployeeChangeProcessor implements MessageProcessor {
    @Resource
    private DidiRpc didiRpc;
    @Value("${close_didi_user_sync:true}")
    boolean closeDidiUserSync;
    @Autowired
    private DidiUserSyncJob didiUserSyncJob;
    @Autowired
    private OaUserMapper oaUserMapper;

    @Override
    public ConsumeStatus process(MessageExt msg, ConsumeContext context) {
        // [sit, beta]默认关闭滴滴用户同步, 只有pro打开
        if (closeDidiUserSync) {
            return ConsumeStatus.SUCCESS;
        }

        JSONObject body = JSON.parseObject(new String(msg.getBody(), StandardCharsets.UTF_8));
        String employeeChangeEvent = body.getString(EMPLOYEE_CHANGE_EVENT_KEY);
        JSONArray employeeIds = body.getJSONObject(DATA_KEY).getJSONArray(EMPLOYEE_IDS_KEY);
        String userId = employeeIds.getString(0);
        log.info("EhrEmployeeChangeProcessor - process - employeeChangeEvent = {}, userId = {}", employeeChangeEvent, userId);
        try {
            // 入职  || 修改员工信息
            if (CONFIRM_ENTRY.getCode().equals(employeeChangeEvent) || UPDATE_EMPLOYEE_INFO.getCode().equals(employeeChangeEvent)) {
                didiUserSyncJob.synByUserVo(getUserListById(userId));
            }
            // 离职
            else if (E_DIMISSION.getCode().equals(employeeChangeEvent)) {
                didiRpc.deleteUser(userId);
            }
        } catch (Exception e) {
            log.error("EhrEmployeeChangeProcessor - process - userId = {}, {}", userId, e.getMessage(), e);
            return ConsumeStatus.SUCCESS;
        }
        return ConsumeStatus.SUCCESS;
    }

    private List<UserVo> getUserListById(String userId) {
        OaUserEntity oaUserEntity = oaUserMapper.selectOne(new LambdaQueryWrapper<OaUserEntity>().eq(OaUserEntity::getUserId, userId));
        return Lists.newArrayList(OaUserEntity.convertToUserVo(oaUserEntity));
    }
}
