package com.xhs.oa.travel_apply.model.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 滴滴订单状态推送响应
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("滴滴订单状态推送响应")
public class DidiOrderStatusResponse {

    @ApiModelProperty(value = "错误码，若不为0则视为回调失败", required = true, example = "0")
    private Integer errno;

    @ApiModelProperty(value = "错误信息", required = true, example = "")
    private String errmsg;

    /**
     * 成功响应
     */
    public static DidiOrderStatusResponse success() {
        return new DidiOrderStatusResponse(0, "");
    }

    /**
     * 失败响应
     */
    public static DidiOrderStatusResponse error(Integer errorCode, String errorMessage) {
        return new DidiOrderStatusResponse(errorCode, errorMessage);
    }
} 