package com.xhs.oa.travel_apply.controller;

import com.alibaba.fastjson.JSON;
import com.xhs.oa.travel_apply.model.request.CtripOrderStatusRequest;
import com.xhs.oa.travel_apply.model.response.CtripOrderStatusResponse;
import com.xhs.oa.travel_apply.service.CtripOrderStatusService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 携程回调接口控制器
 */
@Slf4j
@Api(value = "/oa-office/ctrip", description = "携程回调相关接口")
@RestController
@RequestMapping("/oa-office/ctrip")
public class CtripCallbackController {

    @Resource
    private CtripOrderStatusService ctripOrderStatusService;

    @ApiOperation(notes = "/orderStatusCallback", value = "携程订单状态变化回调接口")
    @PostMapping(value = "/orderStatusCallback")
    public CtripOrderStatusResponse orderStatusCallback(@RequestBody CtripOrderStatusRequest request) {
        log.info("接收到携程订单状态变化回调请求：{}", JSON.toJSONString(request));
        return ctripOrderStatusService.handleOrderStatusChange(request);
    }
}
