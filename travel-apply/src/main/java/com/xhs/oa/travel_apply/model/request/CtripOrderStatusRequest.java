package com.xhs.oa.travel_apply.model.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 携程订单状态推送请求
 */
@Data
@ApiModel("携程订单状态推送请求")
public class CtripOrderStatusRequest {

    @ApiModelProperty(value = "产品类型", required = true, example = "FlightInternational")
    private String productType;

    @ApiModelProperty(value = "订单号", required = true, example = "2160463435")
    private String orderId;

    @ApiModelProperty(value = "订单状态和授权状态", required = true, example = "Submitted")
    private String orderStatus;

    @ApiModelProperty(value = "公司编号", required = true, example = "5566")
    private String corpId;

    @ApiModelProperty(value = "平台ID", required = false)
    private String platformID;

    @ApiModelProperty(value = "来源ID", required = false)
    private String sourceID;

    @ApiModelProperty(value = "状态流水号", required = false)
    private String statusIDs;

    @ApiModelProperty(value = "退款类型", required = false)
    private String refundType;

    @ApiModelProperty(value = "授权场景", required = false)
    private String approveScenario;

    @ApiModelProperty(value = "签名", required = true)
    private String sign;

    @ApiModelProperty(value = "酒店修改单ID", required = false)
    private String modifyApplyID;

    @ApiModelProperty(value = "酒店修改单类型", required = false)
    private String modifyType;

    @ApiModelProperty(value = "酒店修改单状态", required = false)
    private String modifyFormStatus;

    @ApiModelProperty(value = "酒店取消单状态", required = false)
    private String cancelFormStatus;

    @ApiModelProperty(value = "平台订单号", required = false)
    private String platformOrderID;

    @ApiModelProperty(value = "关联订单号", required = false)
    private String relatedOrderIDs;
}
