package com.xhs.oa.travel_apply.model.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 携程订单状态推送响应
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("携程订单状态推送响应")
public class CtripOrderStatusResponse {

    @ApiModelProperty(value = "错误码，只能是数字，0代表成功，其他数字代表失败的错误码", required = true, example = "0")
    private String errno;

    @ApiModelProperty(value = "错误信息，失败的情况下才需要", required = true, example = "")
    private String errmsg;

    /**
     * 成功响应
     */
    public static CtripOrderStatusResponse success() {
        return new CtripOrderStatusResponse("0", "");
    }

    /**
     * 失败响应
     */
    public static CtripOrderStatusResponse error(String errorCode, String errorMessage) {
        return new CtripOrderStatusResponse(errorCode, errorMessage);
    }
}
