package com.xhs.oa.travel_apply.utils;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * 携程签名验证工具类
 */
public class CtripSignUtils {

    /**
     * 生成签名
     * 计算规则：将productType、orderId、orderStatus、corpId和secret按照参数名升序排列,
     * 把参数名和值用"="号拼接，然后再用"&"拼接，最后对字符串进行sha1，然后转换为大写即可。
     * 注意：字段为null时要转化成空字符串处理。
     */
    public static String generateSign(String productType, String orderId, String orderStatus, 
                                    String corpId, String secret) {
        // 构造字典
        Map<String, String> params = new HashMap<>();
        params.put("productType", StringUtils.defaultString(productType));
        params.put("orderId", StringUtils.defaultString(orderId));
        params.put("orderStatus", StringUtils.defaultString(orderStatus));
        params.put("corpId", StringUtils.defaultString(corpId));
        params.put("secret", StringUtils.defaultString(secret));

        // 排序
        List<String> keys = new ArrayList<>(params.keySet());
        Collections.sort(keys);

        // 拼接
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            sb.append(key).append("=").append(params.get(key));
            if (i != keys.size() - 1) {
                sb.append("&");
            }
        }

        // SHA1加密并转换为大写
        return DigestUtils.sha1Hex(sb.toString()).toUpperCase();
    }

    /**
     * 验证签名
     */
    public static boolean verifySign(String productType, String orderId, String orderStatus, 
                                   String corpId, String secret, String sign) {
        if (StringUtils.isBlank(sign)) {
            return false;
        }
        
        String expectedSign = generateSign(productType, orderId, orderStatus, corpId, secret);
        return sign.equals(expectedSign);
    }
}
