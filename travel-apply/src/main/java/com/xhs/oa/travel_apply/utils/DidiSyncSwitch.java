package com.xhs.oa.travel_apply.utils;

import com.xhs.oa.office.utils.Env;
import org.springframework.stereotype.Component;

/**
 * 滴滴同步控制工具类
 * 用于控制是否允许向滴滴发送真实请求
 */
@Component
public class DidiSyncSwitch {

    private static final ThreadLocal<Boolean> SYNC_ENABLED = ThreadLocal.withInitial(() -> Boolean.FALSE);

    /**
     * 设置当前线程是否允许向滴滴同步数据
     *
     * @param enabled 是否允许同步
     */
    public static void setSyncEnabled(boolean enabled) {
        SYNC_ENABLED.set(enabled);
    }

    /**
     * 清除当前线程的同步状态
     * 使用完后应调用此方法清除ThreadLocal，避免内存泄漏
     */
    public static void clear() {
        SYNC_ENABLED.remove();
    }

    /**
     * 检查是否允许同步
     * 在生产环境下总是允许同步
     * 在非生产环境下，只有显式启用时才允许同步
     *
     * @return 是否允许同步
     */
    public static boolean isSyncAllowed() {
        // 生产环境总是允许同步
        if (Env.PROD.equals(Env.getCurrentEnv()) || Env.BETA.equals(Env.getCurrentEnv())) {
            return true;
        }

        // 非生产环境需要显式启用
        return SYNC_ENABLED.get() != null && SYNC_ENABLED.get();
    }
}
