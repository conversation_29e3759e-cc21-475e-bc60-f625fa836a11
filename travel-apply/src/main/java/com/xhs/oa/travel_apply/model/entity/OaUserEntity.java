package com.xhs.oa.travel_apply.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xhs.oa.travel_apply.model.vo.UserVo;
import lombok.Data;

/**
 * <AUTHOR>
 * @date :2025/04/16 - 上午11:17
 * @description : OA中的用户列表
 */
@Data
@TableName("ACT_ID_USER")
public class OaUserEntity {
    @TableId(value = "ID_", type = IdType.AUTO)
    private String userId;

    private String mobilePhone;

    private String workingPlace;

    private String employType;

    @TableField("EMAIL_")
    private String email;

    // 真名
    @TableField("FIRST_")
    private String realName;

    // 薯名
    @TableField("RED_NAME")
    private String redName;


    // 员工状态 0：停用 1：开通
    @TableField("ACCOUNT_STATUS")
    private Integer accountStatus;

    @TableField("FIRST_")
    private String first;

    @TableField("RED_MAIL")
    private String redMail;

    @TableField("LEADER_ID")
    private Long leaderId;

    @TableField("DEPARTMENT_ID")
    private Long departmentId;

    @TableField("COMPANY_NAME")
    private String companyName;

    /**
     * 将 OaUserEntity 转换为 UserVo
     *
     * @param entity 用户实体
     * @return 用户VO对象
     */
    public static UserVo convertToUserVo(OaUserEntity entity) {
        if (entity == null) {
            return null;
        }
        UserVo vo = new UserVo();
        UserVo userVo = new UserVo();
        userVo.setUserID(entity.getUserId());
        userVo.setUserName(entity.getFirst());
        userVo.setRedName(entity.getRedName());
        userVo.setMobilePhone(entity.getMobilePhone());
        userVo.setEmail(entity.getEmail());
        userVo.setRedEmail(entity.getRedMail());
        userVo.setLeaderId(entity.getLeaderId());
        userVo.setAccountStatus(entity.getAccountStatus());
        userVo.setDepartmentId(entity.getDepartmentId());
        userVo.setWorkingPlaceCode(entity.getWorkingPlace());
        userVo.setCompanyName(entity.getCompanyName());
        userVo.setEmployType(Integer.parseInt(entity.getEmployType()));
        return userVo;
    }

}
