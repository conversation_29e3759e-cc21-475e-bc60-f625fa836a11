package com.xhs.oa.travel_apply.controller;

import com.xhs.oa.travel_apply.model.dto.DidiUserDto;
import com.xhs.oa.travel_apply.model.request.DidiBaseRequest;
import com.alibaba.fastjson.JSON;
import com.xhs.oa.travel_apply.model.request.DidiOrderStatusRequest;
import com.xhs.oa.travel_apply.model.response.AddDidiUserResponse;
import com.xhs.oa.travel_apply.model.response.DidiLoginResponse;
import com.xhs.oa.travel_apply.model.response.DidiOrderStatusResponse;
import com.xhs.oa.travel_apply.model.response.QueryDidiUserResponse;
import com.xhs.oa.travel_apply.rpc.DidiRpc;
import com.xhs.oa.travel_apply.service.DidiLoginService;
import com.xhs.oa.travel_apply.service.DidiOrderStatusService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date :2025/03/22 - 上午11:01
 * @description :
 */
@Api(value = "/oa-office/didi", description = "滴滴相关接口")
@RestController
@RequestMapping("/oa-office/didi")
@Slf4j
public class DidiBaseController {
    @Resource
    private DidiRpc didiRpc;
    @Resource
    private DidiLoginService didiLoginService;
    @Resource
    private DidiOrderStatusService didiOrderStatusService;

    @ApiOperation(notes = "/didiLogin", value = "滴滴登录")
    @PostMapping(value = "/didiLogin")
    public DidiLoginResponse didiLogin() {
        String loginUrl = didiLoginService.didiLogin();
        return new DidiLoginResponse(loginUrl);
    }

    @ApiOperation(notes = "/queryDidiUser", value = "查询滴滴用户")
    @PostMapping(value = "/queryDidiUser")
    public QueryDidiUserResponse queryDidiUser(@RequestBody DidiBaseRequest request) {
        String userId = request.getUserId();
        DidiUserDto didiUserDto = didiRpc.queryDidiUser(userId);
        return new QueryDidiUserResponse(didiUserDto);
    }

    @ApiOperation(notes = "/addDidiUser", value = "新增滴滴用户")
    @PostMapping(value = "/addDidiUser")
    public AddDidiUserResponse addDidiUser(@RequestBody DidiBaseRequest request) {
        String userId = request.getUserId();
        return new AddDidiUserResponse(didiRpc.addUserById(userId));
    }

    @ApiOperation(notes = "/deleteDidiUser", value = "删除滴滴用户")
    @PostMapping(value = "/deleteDidiUser")
    public void deleteDidiUser(@RequestBody DidiBaseRequest request) {
        String userId = request.getUserId();
        didiRpc.deleteDidiUser(userId);
    }

    @ApiOperation(notes = "/addUser", value = "新增用户")
    @PostMapping(value = "/addUser")
    public void addUser(@RequestBody DidiBaseRequest request) {
        String userId = request.getUserId();
        didiRpc.addUserById(userId);
    }

    @ApiOperation(notes = "/deleteUser", value = "删除用户")
    @PostMapping(value = "/deleteUser")
    public void deleteUser(@RequestBody DidiBaseRequest request) {
        String userId = request.getUserId();
        didiRpc.deleteUser(userId);
    }

    @ApiOperation(notes = "/updateUser", value = "更新用户")
    @PostMapping(value = "/updateUser")
    public void updateUser(@RequestBody DidiBaseRequest request) {
        String userId = request.getUserId();
        didiRpc.upsertUserDataByUserId(userId);
    }

    @ApiOperation(notes = "/orderStatusCallback", value = "滴滴订单状态回调")
    @PostMapping(value = "/orderStatusCallback")
    public DidiOrderStatusResponse orderStatusCallback(@RequestBody DidiOrderStatusRequest request) {
        log.info("接收到didi订单回调消息：{}", JSON.toJSONString(request));
        return didiOrderStatusService.handleOrderStatusChange(request);
    }
}