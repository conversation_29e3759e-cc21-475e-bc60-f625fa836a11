package com.xhs.oa.travel_apply.office_mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xhs.oa.travel_apply.model.entity.EmployeeTravelTripEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * 员工差旅行程Mapper
 */
@Mapper
public interface EmployeeTravelTripMapper extends BaseMapper<EmployeeTravelTripEntity> {

    /**
     * 根据外部系统订单ID查询有效的行程记录（带行锁防止并发问题）
     *
     * @param externalSystemId 外部系统订单ID
     * @return 行程记录列表
     */
    @Select("SELECT * FROM employee_travel_trip WHERE external_system_id = #{externalSystemId} AND is_valid = 1 FOR UPDATE")
    List<EmployeeTravelTripEntity> queryByExternalSystemId(String externalSystemId);

    /**
     * 根据携程订单ID逻辑删除行程记录
     *
     * @param ctripOrderId 携程订单ID
     * @return 影响行数
     */
    default int deleteByExternalSystemId(String ctripOrderId) {
        EmployeeTravelTripEntity updateEntity = new EmployeeTravelTripEntity();
        updateEntity.setIsValid(0);
        updateEntity.setUpdateTime(new Date());

        return update(updateEntity, new LambdaQueryWrapper<EmployeeTravelTripEntity>()
                .eq(EmployeeTravelTripEntity::getExternalSystemId, ctripOrderId)
                .eq(EmployeeTravelTripEntity::getIsValid, 1));
    }
}
