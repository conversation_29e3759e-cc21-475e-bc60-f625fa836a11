package com.xhs.oa.travel_apply.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date :2025/04/16 - 下午8:58
 * @description :
 */
@Data
@TableName("external_organization_relate")
public class ExternalOrganizationRelateEntity {
    /**
     * 主键，自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 此处为红书系统里的userId
     */
    private String sourceId;

    /**
     * 关联的类型 如：user /department
     */
    private String relateType;

    /**
     * 此处为滴滴系统里的userId
     */
    private String targetId;

    /**
     * 关联的业务类型 如：didi
     */
    private String busType;

    //是否有效
    private Integer isValid;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
