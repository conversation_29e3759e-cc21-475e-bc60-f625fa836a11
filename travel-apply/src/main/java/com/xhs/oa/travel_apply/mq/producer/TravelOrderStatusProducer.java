package com.xhs.oa.travel_apply.mq.producer;

import com.alibaba.fastjson2.JSON;
import com.xhs.oa.travel_apply.model.entity.EmployeeTravelTripEntity;
import com.xiaohongshu.events.client.Message;
import com.xiaohongshu.events.client.producer.EventsProducer;
import com.xiaohongshu.events.client.producer.SendResult;
import com.xiaohongshu.events.client.producer.SendStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

import static com.xiaohongshu.events.common.PartitionSelect.PARTITION_HASH_ID;

/**
 * 差旅订单状态变化消息生产者
 */
@Component
@Slf4j
public class TravelOrderStatusProducer {
    
    private EventsProducer eventsProducer;
    
    private static final String TRAVEL_ORDER_STATUS_CHANGE = "oa_office_travel_order_status_change";
    
    public TravelOrderStatusProducer() {
        eventsProducer = new EventsProducer(TRAVEL_ORDER_STATUS_CHANGE);
        eventsProducer.start();
    }

    /**
     * 发送差旅订单状态变化消息 - 删除事件
     *
     * @param entity 被删除的实体对象
     * @return 发送结果
     */
    public boolean sendMessage(EmployeeTravelTripEntity entity) {
        try {
            String messageContent = JSON.toJSONString(entity);
            log.info("发送差旅订单消息：{}", messageContent);
            Message message = new Message();
            message.setTopic(TRAVEL_ORDER_STATUS_CHANGE);
            message.setBody(messageContent.getBytes(StandardCharsets.UTF_8));
            message.setPartitionId(PARTITION_HASH_ID.getValue());
            message.setHashId(entity.getExternalSystemId().hashCode());
            SendResult result = eventsProducer.send(TRAVEL_ORDER_STATUS_CHANGE, messageContent);
            if (SendStatus.SUCCESS.equals(result.getSendStatus())) {
                log.info("差旅订单消息发送成功");
                return true;
            } else {
                log.error("差旅订单消息发送失败，错误信息：{}", JSON.toJSONString(result));
                return false;
            }
        } catch (Exception e) {
            log.error("发送差旅订单消息异常，错误信息：", e);
            return false;
        }
    }

}
