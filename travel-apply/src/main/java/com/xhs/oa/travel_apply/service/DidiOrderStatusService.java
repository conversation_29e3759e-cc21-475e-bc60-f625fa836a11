package com.xhs.oa.travel_apply.service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.xhs.oa.office.enums.ValidEnum;
import com.xhs.oa.travel_apply.enums.PassengerType;
import com.xhs.oa.travel_apply.enums.TmcTypeEnum;
import com.xhs.oa.travel_apply.mapper.TravelSchedulePassengerMapper;
import com.xhs.oa.travel_apply.model.entity.EmployeeTravelTripEntity;
import com.xhs.oa.travel_apply.model.entity.TravelSchedulePassengerEntity;
import com.xhs.oa.travel_apply.model.request.DidiOrderStatusRequest;
import com.xhs.oa.travel_apply.model.response.DidiOrderStatusResponse;
import com.xhs.oa.travel_apply.mq.producer.TravelOrderStatusProducer;
import com.xhs.oa.travel_apply.office_mapper.EmployeeTravelTripMapper;
import com.xhs.oa.travel_apply.rpc.DidiRpc;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 滴滴订单状态处理服务实现
 */
@Slf4j
@Service
public class DidiOrderStatusService {

    /**
     * 火车票订单类型
     */
    private static final Integer TRAIN_ORDER_TYPE = 3;

    /**
     * 滴滴火车票票状态常量
     */
    private static final String TICKET_STATUS_OPENFORUSE = "Openforuse"; // 新增行程信息
    private static final String TICKET_STATUS_CHANGED = "Changed"; // 已改签则新增行程信息
    private static final String TICKET_STATUS_REFUNDED = "Refunded"; // 退票

    private final DidiRpc didiRpc;

    @Resource
    private TravelSchedulePassengerMapper travelSchedulePassengerMapper;

    @Resource
    private EmployeeTravelTripMapper employeeTravelTripMapper;

    @Resource
    private TravelOrderStatusProducer travelOrderStatusProducer;

    public DidiOrderStatusService(DidiRpc didiRpc) {
        this.didiRpc = didiRpc;
    }

    /**
     * 处理滴滴订单状态变化回调
     */
    public DidiOrderStatusResponse handleOrderStatusChange(DidiOrderStatusRequest request) {
        log.info("收到滴滴订单状态变化回调，请求参数：{}", JSON.toJSONString(request));

        try {
            // 1. 参数校验
            DidiOrderStatusResponse validationResult = validateRequest(request);
            if (validationResult != null) {
                return validationResult;
            }

            // 2. 只处理火车票类型订单
            if (!TRAIN_ORDER_TYPE.equals(request.getOrderType())) {
                log.info("订单类型非火车票，跳过处理，订单号：{}，订单类型：{}", request.getOrderId(), request.getOrderType());
                return DidiOrderStatusResponse.success();
            }
            String notifyDesc = request.getNotifyDesc();
            if (Objects.equals(notifyDesc, "待支付") || Objects.equals(notifyDesc, "发起支付申请")|| Objects.equals(notifyDesc, "订单已提交")) {
                log.info("订单状态待支付或发起支付申请，跳过处理，订单号：{}，状态：{}", request.getOrderId(), request.getNotifyDesc());
                return DidiOrderStatusResponse.success();
            }

            // 4. 处理火车票订单状态变化
            processTrainOrderStatusChange(request);

            log.info("滴滴火车票订单状态变化处理成功，orderId：{}", request.getOrderId());
            return DidiOrderStatusResponse.success();

        } catch (Exception e) {
            log.error("处理滴滴订单状态变化失败，orderId：{}，错误信息：", request.getOrderId(), e);
            return DidiOrderStatusResponse.error(1000, "系统内部错误");
        }
    }

    /**
     * 参数校验
     */
    private DidiOrderStatusResponse validateRequest(DidiOrderStatusRequest request) {
        if (request == null) {
            log.error("滴滴订单状态回调请求参数为空");
            return DidiOrderStatusResponse.error(1002, "请求参数为空");
        }

        if (StringUtils.isBlank(request.getClientId())) {
            log.error("滴滴订单状态回调应用ID为空");
            return DidiOrderStatusResponse.error(1003, "应用ID不能为空");
        }

        if (StringUtils.isBlank(request.getCompanyId())) {
            log.error("滴滴订单状态回调公司ID为空");
            return DidiOrderStatusResponse.error(1004, "公司ID不能为空");
        }

        if (request.getTimestamp() == null) {
            log.error("滴滴订单状态回调时间戳为空");
            return DidiOrderStatusResponse.error(1005, "时间戳不能为空");
        }

        if (StringUtils.isBlank(request.getOrderId())) {
            log.error("滴滴订单状态回调订单号为空");
            return DidiOrderStatusResponse.error(1006, "订单号不能为空");
        }

        if (request.getOrderType() == null) {
            log.error("滴滴订单状态回调订单类型为空");
            return DidiOrderStatusResponse.error(1007, "订单类型不能为空");
        }

        if (request.getNotifyType() == null) {
            log.error("滴滴订单状态回调通知类型为空");
            return DidiOrderStatusResponse.error(1008, "通知类型不能为空");
        }

        if (StringUtils.isBlank(request.getNotifyDesc())) {
            log.error("滴滴订单状态回调通知说明为空");
            return DidiOrderStatusResponse.error(1009, "通知说明不能为空");
        }

        if (StringUtils.isBlank(request.getUniqId())) {
            log.error("滴滴订单状态回调唯一key为空");
            return DidiOrderStatusResponse.error(1010, "唯一key不能为空");
        }

        if (StringUtils.isBlank(request.getSign())) {
            log.error("滴滴订单状态回调签名为空");
            return DidiOrderStatusResponse.error(1011, "签名不能为空");
        }

        return null;
    }

    /**
     * 处理火车票订单状态变化
     */
    private void processTrainOrderStatusChange(DidiOrderStatusRequest request) {
        log.info("开始处理火车票订单状态变化，订单号：{}，通知类型：{}，状态描述：{}", request.getOrderId(), request.getNotifyType(), request.getNotifyDesc());

        try {
            // 调用滴滴获取火车票订单详情
            List<JSONObject> trainOrderDetails = didiRpc.getTrainOrderDetails(Collections.singletonList(request.getOrderId()));
            if (CollectionUtils.isEmpty(trainOrderDetails)) {
                log.warn("未获取到滴滴火车票订单详情，订单号：{}", request.getOrderId());
                return;
            }
            for (JSONObject orderDetailResponse : trainOrderDetails) {
                log.info("火车票订单详细信息获取完成，订单号：{}", request.getOrderId());

                // 解析订单详情并提取车票信息
                List<JSONObject> ticketInfoList = extractTicketInfoList(orderDetailResponse);
                if (CollectionUtils.isEmpty(ticketInfoList)) {
                    log.warn("未能提取到车票信息，订单号：{}", request.getOrderId());
                    return;
                }
                String scheduleFormNum = orderDetailResponse.getJSONObject("order_info").getString("out_approval_id");
                List<TravelSchedulePassengerEntity> passengers = travelSchedulePassengerMapper.queryByScheFormNumPassengerType(scheduleFormNum, PassengerType.PASSENGER.getCode());
                if (CollectionUtils.isEmpty(passengers)) {
                    log.warn("未找到对应的出行人信息，out_approval_id：{}", scheduleFormNum);
                    return;
                }
                TravelSchedulePassengerEntity passengerEntity = passengers.get(0);
                // 处理员工差旅行程记录
                ((DidiOrderStatusService) AopContext.currentProxy()).processDidiEmployeeTravelTrip(request, passengerEntity, ticketInfoList);
            }
        } catch (Exception e) {
            log.error("处理火车票订单状态变化异常，订单号：{}，错误信息：", request.getOrderId(), e);
            throw e;
        }
    }

    /**
     * 从滴滴订单详情响应中提取车票信息列表
     */
    private List<JSONObject> extractTicketInfoList(JSONObject orderDetailResponse) {
        try {
            JSONArray ticketList = orderDetailResponse.getJSONArray("ticket_list");
            if (CollectionUtils.isEmpty(ticketList)) {
                return Collections.emptyList();
            }

            // 提取所有 ticket_info
            List<JSONObject> ticketInfoList = Collections.emptyList();
            for (int i = 0; i < ticketList.size(); i++) {
                JSONObject ticketItem = ticketList.getJSONObject(i);
                if (ticketItem != null) {
                    JSONObject ticketInfo = ticketItem.getJSONObject("ticket_info");
                    if (ticketInfo != null) {
                        if (ticketInfoList.isEmpty()) {
                            ticketInfoList = new java.util.ArrayList<>();
                        }
                        ticketInfoList.add(ticketInfo);
                    }
                }
            }

            return ticketInfoList;
        } catch (Exception e) {
            log.error("提取车票信息异常：", e);
            return Collections.emptyList();
        }
    }

    /**
     * 创建模拟的出行人实体（简化处理）
     */
    private TravelSchedulePassengerEntity createMockPassengerEntity(String employeeNumber) {
        TravelSchedulePassengerEntity entity = new TravelSchedulePassengerEntity();
        entity.setPassengerId(employeeNumber);
        entity.setPassengerType(PassengerType.PASSENGER.getCode());
        entity.setPassengerName("滴滴用户"); // 占位符，实际应从用户系统查询
        return entity;
    }

    /**
     * 处理滴滴员工差旅行程记录
     */
    @Transactional(rollbackFor = Exception.class, transactionManager = "oaOfficeTransactionManager")
    public void processDidiEmployeeTravelTrip(DidiOrderStatusRequest request, TravelSchedulePassengerEntity passenger, List<JSONObject> ticketInfoList) {
        try {
            log.info("开始处理滴滴员工差旅行程记录，订单号：{}，用户ID：{}", request.getOrderId(), passenger.getPassengerId());
            boolean needProcess = ticketInfoList.stream().anyMatch(ticket -> {
                String ticketStatus = ticket.getString("ticket_status");
                return TICKET_STATUS_OPENFORUSE.equals(ticketStatus) || TICKET_STATUS_CHANGED.equals(ticketStatus) || TICKET_STATUS_REFUNDED.equals(ticketStatus);
            });
            if (!needProcess) {
                log.info("无需处理，订单号：{}，用户ID：{}", request.getOrderId(), passenger.getPassengerId());
                return;
            }
            // 1. 根据orderId删除现有记录 - 将isValid字段设置为无效
            List<EmployeeTravelTripEntity> existingRecords = employeeTravelTripMapper.queryByExternalSystemId(request.getOrderId());
            int deletedCount = 0;
            if (!CollectionUtils.isEmpty(existingRecords)) {
                deletedCount = employeeTravelTripMapper.deleteByExternalSystemId(request.getOrderId());
                // 2. 为每个删除的记录发送删除事件
                for (EmployeeTravelTripEntity deletedRecord : existingRecords) {
                    deletedRecord.setIsValid(0);
                    travelOrderStatusProducer.sendMessage(deletedRecord);
                }
            }
            log.info("删除现有记录数量：{}，订单号：{}", deletedCount, request.getOrderId());

            // 3. 根据车票状态判断是否需要新增行程信息
            for (JSONObject ticketInfo : ticketInfoList) {
                String ticketStatus = ticketInfo.getString("ticket_status");
                log.info("处理车票，状态：{}，订单号：{}", ticketStatus, request.getOrderId());

                if (TICKET_STATUS_OPENFORUSE.equals(ticketStatus) || TICKET_STATUS_CHANGED.equals(ticketStatus)) {
                    // Openforuse（可使用）或 Changed（已改签）状态需要新增行程信息
                    EmployeeTravelTripEntity inserted = insertDidiEmployeeTravelTrip(request, passenger, ticketInfo);
                    // 4. 发送插入消息
                    if (inserted != null) {
                        travelOrderStatusProducer.sendMessage(inserted);
                    }
                } else {
                    // 其他状态均不做任何处理
                    log.info("车票状态为{}，不做处理，订单号：{}", ticketStatus, request.getOrderId());
                }
            }
            log.info("处理滴滴员工差旅行程记录完成，订单号：{}，用户ID：{}", request.getOrderId(), passenger.getPassengerId());
        } catch (Exception e) {
            log.error("处理滴滴员工差旅行程记录异常，订单号：{}，用户ID：{}，错误信息：", request.getOrderId(), passenger.getPassengerId(), e);
            throw e; // 重新抛出异常，让上层处理
        }
    }

    /**
     * 插入滴滴员工差旅行程记录
     */
    private EmployeeTravelTripEntity insertDidiEmployeeTravelTrip(DidiOrderStatusRequest request, TravelSchedulePassengerEntity passenger, JSONObject ticketInfo) {
        try {
            EmployeeTravelTripEntity newTrip = new EmployeeTravelTripEntity();
            newTrip.setTmcType(TmcTypeEnum.DIDI.getCode());
            newTrip.setUserId(Long.parseLong(passenger.getPassengerId()));
            newTrip.setTripType("train");
            newTrip.setExternalSystemId(request.getOrderId());
            newTrip.setExternalStatus(request.getNotifyDesc());

            // 设置火车票信息
            newTrip.setDeparturePlace(ticketInfo.getString("departure_station_name")); // 出发站
            newTrip.setArrivalPlace(ticketInfo.getString("arrival_station_name")); // 到达站

            // 解析时间（根据滴滴API文档，时间格式为：2023-11-07 07:00:00）
            String startTime = ticketInfo.getString("start_time");
            if (StringUtils.isNotBlank(startTime)) {
                newTrip.setDepartureTime(DateUtils.parseDate(startTime, "yyyy-MM-dd HH:mm:ss"));
            }

            String arriveTime = ticketInfo.getString("arrive_time");
            if (StringUtils.isNotBlank(arriveTime)) {
                newTrip.setArrivalTime(DateUtils.parseDate(arriveTime, "yyyy-MM-dd HH:mm:ss"));
            }

            newTrip.setIsValid(ValidEnum.VALID.getCode());
            newTrip.setCreateTime(new Date());
            newTrip.setUpdateTime(new Date());

            employeeTravelTripMapper.insert(newTrip);

            String trainCode = ticketInfo.getString("train_code");
            log.info("插入滴滴员工差旅行程记录成功，订单号：{}，用户ID：{}，车次：{}", request.getOrderId(), passenger.getPassengerId(), trainCode);

            return newTrip;
        } catch (Exception e) {
            log.error("插入滴滴员工差旅行程记录失败，订单号：{}，用户ID：{}，错误信息：",
                    request.getOrderId(), passenger.getPassengerId(), e);
            return null;
        }
    }
}
