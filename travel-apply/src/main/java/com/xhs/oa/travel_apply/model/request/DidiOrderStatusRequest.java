package com.xhs.oa.travel_apply.model.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 滴滴订单状态推送请求
 */
@Data
@ApiModel("滴滴订单状态推送请求")
public class DidiOrderStatusRequest {

    @JsonProperty("client_id")
    @ApiModelProperty(value = "应用ID", required = true, example = "8898bc1874633e00d04acf5b6abca42a")
    private String clientId;

    @JsonProperty("company_id")
    @ApiModelProperty(value = "滴滴侧公司ID", required = true, example = "1125910633379176")
    private String companyId;

    @ApiModelProperty(value = "回调时间戳(秒级)", required = true, example = "1694586605")
    private Long timestamp;

    @ApiModelProperty(value = "签名", required = true, example = "d881c57b95c11fbcea7706d0d23070bb")
    private String sign;

    @JsonProperty("order_id")
    @ApiModelProperty(value = "订单号", required = true, example = "1125924397469443")
    private String orderId;

    @JsonProperty("order_type")
    @ApiModelProperty(value = "订单类型，1国内酒店，2国内机票，3火车票，4国际机票，5国际酒店", required = true, example = "3")
    private Integer orderType;

    @JsonProperty("notify_type")
    @ApiModelProperty(value = "通知类型：1-订单中间状态流转，2-订单终态通知，4-订单退款通知", required = true, example = "2")
    private Integer notifyType;

    @JsonProperty("notify_desc")
    @ApiModelProperty(value = "订单状态说明", required = true, example = "订单已取消")
    private String notifyDesc;

    @JsonProperty("uniq_id")
    @ApiModelProperty(value = "回调唯一key", required = true, example = "ba4974868fb0e5a68b1e211230e2b14b")
    private String uniqId;

    @JsonProperty("extra_info")
    @ApiModelProperty(value = "扩展数据", required = false)
    private ExtraInfo extraInfo;

    /**
     * 扩展数据
     */
    @Data
    @ApiModel("扩展数据")
    public static class ExtraInfo {
        @JsonProperty("group_code")
        @ApiModelProperty(value = "下单人成本中心ID(客户侧成本中心ID)")
        private String groupCode;

        @JsonProperty("budget_center_codes")
        @ApiModelProperty(value = "乘客成本中心ID集合(客户侧成本中心ID)")
        private String[] budgetCenterCodes;

        @JsonProperty("employee_number")
        @ApiModelProperty(value = "下单人工号")
        private String employeeNumber;

        @JsonProperty("company_pay")
        @ApiModelProperty(value = "企业支付金额，支付时有值，单位：元")
        private String companyPay;

        @JsonProperty("company_refund")
        @ApiModelProperty(value = "企业退款金额，退款时有值，单位：元")
        private String companyRefund;

        @JsonProperty("order_change_time")
        @ApiModelProperty(value = "订单变更时间戳")
        private Integer orderChangeTime;

        @JsonProperty("able_approval")
        @ApiModelProperty(value = "是否需审批，0-无需审批，1-需审批")
        private Integer ableApproval;

        @JsonProperty("approval_id")
        @ApiModelProperty(value = "滴滴审批单号")
        private String approvalId;
    }
} 