package com.xhs.oa.travel_apply.rpc;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.google.common.collect.Lists;
import com.xhs.finance.utils.HttpClientUtils;
import com.xhs.oa.office.exception.BusinessException;
import com.xhs.oa.office.rpc.CompanyRpcService;
import com.xhs.oa.travel_apply.constant.CommonConstant;
import com.xhs.oa.travel_apply.enums.EmployTypeEnum;
import com.xhs.oa.travel_apply.mapper.ExternalOrganizationRelateMapper;
import com.xhs.oa.travel_apply.mapper.OaUserMapper;
import com.xhs.oa.travel_apply.model.dto.DidiUserDto;
import com.xhs.oa.travel_apply.model.entity.ExternalOrganizationRelateEntity;
import com.xhs.oa.travel_apply.model.entity.OaUserEntity;
import com.xhs.oa.travel_apply.model.vo.DidiProperty;
import com.xhs.oa.travel_apply.model.vo.UserVo;
import com.xhs.oa.travel_apply.service.HandleErrorService;
import com.xhs.oa.travel_apply.utils.DidiSyncSwitch;
import com.xhs.oa.travel_apply.utils.DidiUtils;
import com.xhs.oa.travel_apply.utils.EncryptUtil;
import com.xiaohongshu.fls.rpc.oacommon.company.response.ExpenseInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.testng.util.Strings;
import org.yaml.snakeyaml.util.UriEncoder;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

import static com.xhs.oa.travel_apply.constant.DidiConstant.*;
import static com.xhs.oa.travel_apply.utils.DidiUtils.genSign;

/**
 * <AUTHOR>
 * @date :2025/03/22 - 上午11:28
 * @description :
 */
@Slf4j
@Service
public class DidiRpc {
    public static final String GET_ORDER_DETAIL_API = "https://api.es.xiaojukeji.com/api-gateway/g/train/orderDetail";
    public static final String DIDI_NOT_EXISTS = "50222";
    @Resource
    private HandleErrorService handleErrorService;
    @Value("${didi_client_id}")
    String clientId;
    @Value("${didi_client_secret}")
    String clientSecret;
    @Value("${didi_sign_key}")
    String signKey;
    @Value("${didi_company_id}")
    String companyId;
    @ApolloJsonValue("${workingPlaceMap:{}}")
    Map<String, String> workingPlaceMap;
    @Autowired
    private DidiProperty didiProperty;
    @Autowired
    private ExternalOrganizationRelateMapper externalOrganizationRelateMapper;
    @Autowired
    private CompanyRpcService companyRpcService;
    @Autowired
    private OaUserMapper oaUserMapper;

    /**
     * 请求滴滴授权认证接口，获取accessToken
     *
     * @return accessToken
     */
    private String getAccessToken() {
        // 构造请求体
        HashMap<String, String> body = new HashMap<>();
        body.put("client_id", clientId);
        body.put("client_secret", clientSecret);
        body.put("grant_type", DIDI_GRANT_TYPE_KEY);
        body.put("timestamp", getTimestamp());
        body.put("sign", DidiUtils.genSign(body, signKey));

        // 构造请求头
        HashMap<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");

        try {
            // 请求滴滴
            JSONObject response = DidiUtils.sendPostRequest(DIDI_GET_ACCESS_TOKEN_URL, headers, body);
            String errorCode = response.getString(DIDI_ERROR_CODE_KEY);
            String errorMessage = response.getString(DIDI_ERROR_MESSAGE_KEY);

            // 请求滴滴成功 但是参数不对
            if (StringUtils.isNotBlank(errorCode)) {
                throw new BusinessException("getAccessToken - 参数错误 - " + errorMessage);
            }
            return response.getString(DIDI_ACCESS_TOKEN_KEY);
        } catch (Exception e) {
            log.error("getAccessToken - 请求失败 - {}", e.getMessage(), e);
            throw new BusinessException(e.getMessage());
        }
    }


    private static DidiUserDto getDidiUserDto(HashMap<String, String> params) {
        // 构造请求头
        HashMap<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/x-www-form-urlencoded");

        try {
            // 请求滴滴
            JSONObject response = DidiUtils.sendGetRequest(DIDI_QUERY_USER_URL, headers, params);
            String errorCode = response.getString(DIDI_ERROR_CODE_KEY);
            String errorMessage = response.getString(DIDI_ERROR_MESSAGE_KEY);
            // 没找到
            if (!DIDI_SUCCESS_CODE.equals(errorCode)) {
                log.error("queryUser - 参数错误 - " + errorMessage);
                return new DidiUserDto();
            }
            JSONObject jsonUser = response.getJSONObject(DIDI_LOGIN_DATA_KEY);
            return JSONObject.parseObject(jsonUser.toJSONString(), DidiUserDto.class);
        } catch (Exception e) {
            log.error("queryUser - 请求失败 - {}", e.getMessage(), e);
            throw new BusinessException(e.getMessage());
        }
    }

    public DidiUserDto queryDidiUser(String userId) {
        ExternalOrganizationRelateEntity entity = externalOrganizationRelateMapper.queryRelation(userId);
        if (entity == null) {
            return null;
        }

        // 构造请求参数
        HashMap<String, String> params = getCommonParam();
        params.put("member_id", entity.getTargetId());
        params.put("sign", DidiUtils.genSign(params, signKey));

        return getDidiUserDto(params);
    }

    /**
     * 根据手机号查询滴滴用户信息
     *
     * @param phone 手机号
     * @return 滴滴用户信息
     */
    public DidiUserDto queryDidiUserByPhone(String phone) {
        if (StringUtils.isBlank(phone)) {
            return null;
        }
        // 构造请求参数
        HashMap<String, String> params = getCommonParam();
        params.put("phone", phone);
        params.put("sign", DidiUtils.genSign(params, signKey));

        return getDidiUserDto(params);
    }

    /**
     * 生成当前时间戳
     *
     * @return 当前时间戳
     */
    private String getTimestamp() {
        Instant now = Instant.now();
        return Long.toString(now.getEpochSecond());
    }

    /**
     * 生成通用参数Map
     *
     * @return 通用参数Map
     */
    public HashMap<String, String> getCommonParam() {
        HashMap<String, String> params = new HashMap<>();
        params.put("client_id", clientId);
        params.put("access_token", getAccessToken());
        params.put("company_id", companyId);
        params.put("timestamp", getTimestamp());
        return params;
    }


    public void deleteDidiUser(String userId) {
        ExternalOrganizationRelateEntity entity = externalOrganizationRelateMapper.queryRelation(userId);
        if (entity == null) {
            throw new BusinessException("deleteDidiUser - {} 不存在", userId);
        }

        // 构造请求参数
        HashMap<String, String> body = getCommonParam();
        body.put("member_id", getDeleteDidiUserParam(entity.getTargetId()));
        body.put("sign", DidiUtils.genSign(body, signKey));

        // 构造请求头
        HashMap<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");

        try {
            // 请求滴滴
            JSONObject response = DidiUtils.sendPostRequest(DIDI_DELETE_USER_URL, headers, body);
            String errorCode = response.getString(DIDI_ERROR_CODE_KEY);
            String errorMessage = response.getString(DIDI_ERROR_MESSAGE_KEY);

            // 请求滴滴成功 但是参数不对
            if (!DIDI_SUCCESS_CODE.equals(errorCode)) {
                throw new BusinessException("deleteDidiUser - 参数错误 - " + errorMessage);
            }
        } catch (Exception e) {
            log.error("deleteDidiUser - 请求失败 - {}", e.getMessage(), e);
            throw new BusinessException(e.getMessage());
        }
    }

    /**
     * 构造删除用户的data参数字符串
     *
     * @param userId 待删除用户ID
     * @return 待删除用户ID JSON字符串
     */
    private String getDeleteDidiUserParam(String userId) {
        List<String> userIdList = Lists.newArrayList(userId);
        return String.join("_", userIdList);
    }

    /**
     * 处理已存在的用户，通过手机号查询用户信息并建立关联关系
     *
     * @param userVo 用户信息
     * @return 滴滴用户ID，如果处理失败返回null
     */
    private String handleExistingUser(UserVo userVo) {
        try {
            // 获取用户手机号
            String mobilePhone = userVo.getMobilePhone();
            if (StringUtils.isBlank(mobilePhone)) {
                log.warn("handleExistingUser - 用户手机号为空，无法查询滴滴用户信息 - user id:{}", userVo.getUserID());
                return null;
            }
            // 解密手机号
            String decryptedPhone = EncryptUtil.decrypt(mobilePhone, CommonConstant.USER_INFO_SECRET_KEY);
            if (StringUtils.isBlank(decryptedPhone)) {
                log.warn("handleExistingUser - 手机号解密失败 - user id:{}", userVo.getUserID());
                return null;
            }
            // 通过手机号查询滴滴用户信息
            DidiUserDto didiUserDto = queryDidiUserByPhone(decryptedPhone);
            if (didiUserDto == null || StringUtils.isBlank(didiUserDto.getMember_id())) {
                log.warn("handleExistingUser - 通过手机号未找到滴滴用户 - user id:{}, phone:{}", userVo.getUserID(), decryptedPhone);
                return null;
            }
            // 建立关联关系
            externalOrganizationRelateMapper.addRelation(userVo.getUserID(), didiUserDto.getMember_id());
            log.info("handleExistingUser - 成功建立用户关联关系 - user id:{}, didi member id:{}", userVo.getUserID(), didiUserDto.getMember_id());
            return didiUserDto.getMember_id();
        } catch (Exception e) {
            log.error("handleExistingUser - 处理已存在用户失败 - user id:{}, error:{}", userVo.getUserID(), e.getMessage(), e);
            return null;
        }
    }

    public String addUserById(String userId) {
        if (Strings.isNullOrEmpty(userId)) {
            throw new BusinessException("userId 不能为空");
        }
        OaUserEntity oaUserEntity = oaUserMapper.selectOne(new LambdaQueryWrapper<OaUserEntity>().eq(OaUserEntity::getUserId, userId));
        return addUser(OaUserEntity.convertToUserVo(oaUserEntity));
    }

    public String addUser(UserVo userVo) {
        if (!DidiSyncSwitch.isSyncAllowed()) {
            return "";
        }
        try {
            // 滴滴侧增加员工
            String didiUserId;
            // 构造请求参数
            HashMap<String, String> body = getCommonParam();
            body.put("data", buildDidiUserParams(userVo));
            body.put("sign", DidiUtils.genSign(body, signKey));

            // 构造请求头
            HashMap<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json; charset=UTF-8");

            try {
                // 请求滴滴
                JSONObject response = DidiUtils.sendPostRequest(DIDI_ADD_USER_URL, headers, body);
                String errorCode = response.getString(DIDI_ERROR_CODE_KEY);
                String errorMessage = response.getString(DIDI_ERROR_MESSAGE_KEY);

                // 请求滴滴成功 但是参数不对
                if (!DIDI_SUCCESS_CODE.equals(errorCode)) {
                    if ("50202".equals(errorCode)) {
                        log.info("addDidiUser - 用户已存在 - {},user id:{}", JSON.toJSONString(response), userVo.getUserID());
                        // 用户已存在，尝试通过手机号查询用户信息并建立关联
                        String memberId = handleExistingUser(userVo);
                        if (!Strings.isNullOrEmpty(memberId)) {
                            return memberId;
                        }
                    }
                    log.error("addDidiUser - 参数错误 -{} ", JSON.toJSONString(response));
                    throw new BusinessException("addDidiUser - 参数错误 - " + errorMessage);
                }
                didiUserId = response.getJSONObject(DIDI_ADD_USER_KEY).getString(DIDI_ADD_USER_ID);
            } catch (Exception e) {
                log.error("addDidiUser - 请求失败 - {}", e.getMessage(), e);
                throw e;
            }

            // 红书侧关联表增加员工
            externalOrganizationRelateMapper.addRelation(userVo.getUserID(), didiUserId);
            return didiUserId;
        } catch (Exception e) {
            log.info("addDidiUser error. user name:{}", userVo.getUserName(), e);
        }
        return "";
    }


    public void deleteUser(String userId) {
        try {
            // 滴滴侧删除员工
            deleteDidiUser(userId);
            // 红书侧关联表删除员工
            externalOrganizationRelateMapper.deleteRelation(userId);
        } catch (Exception e) {
            // 同步失败发送通知
            handleErrorService.didiUserSyncErrorNotify(userId, e.getMessage());
        }
    }


    public void upsertUserDataByUserId(String userId) {
        if (Strings.isNullOrEmpty(userId)) {
            throw new BusinessException("userId 不能为空");
        }
        OaUserEntity oaUserEntity = oaUserMapper.selectOne(new LambdaQueryWrapper<OaUserEntity>().eq(OaUserEntity::getUserId, userId));
        upsertUserDataByUser(OaUserEntity.convertToUserVo(oaUserEntity));
    }

    public void upsertUserDataByUser(UserVo userVo) {
        try {
            // 滴滴侧更新员工
            ExternalOrganizationRelateEntity entity = externalOrganizationRelateMapper.queryRelation(userVo.getUserID());
            if (entity == null) {
                addUser(userVo);
            } else {// 构造请求参数
                updateUser(userVo, entity);
            }
        } catch (Exception e) {
            log.info("upsertUserDataByUser error, user name: {}", userVo.getUserName(), e);
        }
    }

    // 更新用户信息
    private void updateUser(UserVo userVo, ExternalOrganizationRelateEntity entity) {
        if (!DidiSyncSwitch.isSyncAllowed()) {
            return;
        }
        HashMap<String, String> body = getCommonParam();
        body.put("member_id", entity.getTargetId());
        body.put("data", buildDidiUserParams(userVo));
        body.put("sign", DidiUtils.genSign(body, signKey));// 构造请求头
        HashMap<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json; charset=UTF-8");
        try {
            // 请求滴滴
            JSONObject response = DidiUtils.sendPostRequest(DIDI_UPDATE_USER_URL, headers, body);
            String errorCode = response.getString(DIDI_ERROR_CODE_KEY);
            String errorMessage = response.getString(DIDI_ERROR_MESSAGE_KEY);

            // 请求滴滴成功 但是参数不对
            if (!DIDI_SUCCESS_CODE.equals(errorCode)) {
                log.error("updateDidiUser - 请求失败 - {}", JSON.toJSONString(response));
                //如果OA系统存在关联，但滴滴后台已不存在，则删除关联，走新增
                if (DIDI_NOT_EXISTS.equals(errorCode)) {
                    externalOrganizationRelateMapper.updateDeleteById(entity.getId());
                    addUser(userVo);
                    return;
                }
                throw new BusinessException("updateDidiUser - 参数错误 - " + errorMessage);
            }
        } catch (Exception e) {
            log.error("updateDidiUser error, user name:{}", userVo.getUserName(), e);
            throw new BusinessException(e.getMessage());
        }
    }

    /**
     * 批量分配regulation_id给用户
     *
     * @param users 用户列表
     */
    public void batchAssignRegulationIds(List<UserVo> users) {
        if (CollectionUtils.isEmpty(users)) {
            return;
        }

        Map<String, List<String>> userRegulationMap = new HashMap<>();

        // 批量获取用户当前在滴滴系统中的regulation_id列表
        Map<String, List<String>> currentRegulationMap = getDidiExistRegulationIds(users);

        // 根据用户类型分配对应的regulation_id
        for (UserVo user : users) {
            if (user == null) {
                continue;
            }
            // 获取当前用户已有的regulation_id列表
            List<String> currentRegulationList = currentRegulationMap.getOrDefault(user.getUserID(), new ArrayList<>());
            // 根据用户类型获取应该分配的regulation_id列表
            List<String> regulationList = getRegulationIdsForUser(user, currentRegulationList);
            userRegulationMap.put(user.getUserID(), regulationList);
        }

        // 将regulation_id设置回用户对象
        for (UserVo user : users) {
            List<String> regulationIds = userRegulationMap.get(user.getUserID());
            if (CollectionUtils.isNotEmpty(regulationIds)) {
                user.setRegulationIdList(regulationIds);
            }
        }
    }

    /**
     * 批量获取用户当前在滴滴系统中的regulation_id列表
     */
    private Map<String, List<String>> getDidiExistRegulationIds(List<UserVo> users) {
        Map<String, List<String>> result = new HashMap<>();

        for (UserVo user : users) {
            try {
                DidiUserDto didiUserDto = queryDidiUser(user.getUserID());
                if (didiUserDto != null) {
                    result.put(user.getUserID(), didiUserDto.getRegulation_id());
                }
            } catch (Exception e) {
                log.error("获取用户{}当前regulation_id失败: {}", user.getUserID(), e.getMessage());
            }
        }

        return result;
    }

    // 判断是否为竞业员工
    private static boolean isCompeteUser(UserVo userVo) {
        if (userVo.getCompeteFlag() == null) {
            return false;
        }
        return Objects.equals(userVo.getCompeteFlag().intValue(), 1);
    }

    /**
     * 根据用户类型获取应该分配的regulation_id列表
     *
     * @param userVo                用户实体
     * @param currentRegulationList 当前用户已有的regulation_id列表
     * @return 应该分配的regulation_id列表
     */
    private List<String> getRegulationIdsForUser(UserVo userVo, List<String> currentRegulationList) {
        if (userVo == null) {
            return currentRegulationList;
        }
        List<String> regulationIds = new ArrayList<>();
        // 保留用户现有的regulation IDs
        if (CollectionUtils.isNotEmpty(currentRegulationList)) {
            regulationIds.addAll(currentRegulationList);
        }

        // 1. 根据用户类型添加对应的用车制度ID
        Integer employType = userVo.getEmployType();
        if (employType != null) {
            if (Objects.equals(EmployTypeEnum.INNER_EMP.getCode(), employType.toString())) {
                regulationIds.add(didiProperty.getDidiCarRegulationOvertimeId());
                regulationIds.add(didiProperty.getDidiCarRegulationTravelId());
            } else if (Objects.equals(EmployTypeEnum.INTERNSHIP_EMP.getCode(), employType.toString())) {
                regulationIds.add(didiProperty.getDidiCarRegulationTravelId());
            }
        }

        boolean competeUser = isCompeteUser(userVo);
        if (!competeUser) {
            // 2. 火车票相关制度分配
            // 在差标白名单中的用户，分配差标白名单制度；不在白名单中的，分配普通票制度
            if (didiProperty.getTravelDidiTrainHighLevelUser().contains(userVo.getUserID())) {
                regulationIds.add(didiProperty.getDidiTrainHighStandardId());
            } else {
                regulationIds.add(didiProperty.getDidiTrainNormalStandardId());
            }
        }

        // 3. 去重
        return regulationIds.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 构造滴滴用户参数（合并原有的更新和新增参数方法）
     *
     * @param userVo 用户
     * @return 参数JSON字符串
     */
    private String buildDidiUserParams(UserVo userVo) {
        Map<String, String> data = new HashMap<>();

        boolean isCompeteUser = isCompeteUser(userVo); // 是否竞业员工
        // 基础字段
        // 手机号
        String mobilePhone = userVo.getMobilePhone();
        if (StringUtils.isNotBlank(mobilePhone)) {
            data.put("phone", EncryptUtil.decrypt(mobilePhone, CommonConstant.USER_INFO_SECRET_KEY));
        }
        // 工作地点
        String workingPlace = workingPlaceMap.getOrDefault(userVo.getWorkingPlace(), "");
        if (StringUtils.isNotBlank(workingPlace)) {
            data.put("residentsname", workingPlace);
        }
        // 员工ID
        data.put("employee_number", userVo.getUserID());
        // 制度列表
        String regulationIdList = userVo.getRegulationIdList().stream().filter(StringUtils::isNotBlank).collect(Collectors.joining("_"));
        if (StringUtils.isNotBlank(regulationIdList)) {
            data.put("regulation_id", regulationIdList);
        }
        // 区分新增和更新的特殊处理
        String realName = userVo.getUserName();
        if (StringUtils.isNotBlank(realName) && !isCompeteUser) {
            data.put("realname", UriEncoder.encode(realName));
        } else {
            data.put("realname", UriEncoder.encode("小红书员工"));
        }
        data.put("email", userVo.getEmail());
        data.put("use_company_money", "1");
        data.put("budget_center_id", INIT_DEPARTMENT_ID);
        // 获得公司主体ID
        if (!isCompeteUser) {
            ExpenseInfo expenseInfo = companyRpcService.queryExpenseInfos(userVo.getUserID());
            data.put("out_legal_entity_id", expenseInfo.getCompanyCode());
        }

        return JSON.toJSONString(data);
    }

    /**
     * 同步用户列表到滴滴
     *
     * @param users 需要同步的用户列表
     */
    public void syncUsersToDidi(List<UserVo> users) {
        if (CollectionUtils.isEmpty(users)) {
            return;
        }
        // 分配regulation_id
        batchAssignRegulationIds(users);
        for (UserVo user : users) {
            try {
                log.info("开始同步用户数据到滴滴：{}", user.getUserName());
                // 调用现有方法同步到滴滴
                upsertUserDataByUser(user);
            } catch (Exception e) {
                log.error("同步用户数据到滴滴失败：{}, 错误: {}", user.getUserName(), e.getMessage(), e);
            }
        }
    }

    /**
     * 根据火车票订单ID列表获取订单详情
     * 调用滴滴API：<a href="https://opendocs.xiaojukeji.com/version2024/10907">...</a>
     */
    public List<JSONObject> getTrainOrderDetails(List<String> orderIds) {
        try {
            HashMap<String, String> paramMap = getCommonParam();
            paramMap.put("order_ids", String.join(",", orderIds));
            paramMap.put("sign", genSign(paramMap, signKey));

            String response = HttpClientUtils.doPost(GET_ORDER_DETAIL_API, JSON.toJSONString(paramMap));
            log.info("获取火车票订单详情返回，订单IDs：{}，响应：{}", String.join(",", orderIds), response);

            JSONObject resultObject = JSON.parseObject(response);
            String errno = resultObject.getString("errno");
            if (!"0".equals(errno)) {
                log.error("获取火车票订单详情失败，订单IDs：{}，错误信息：{}", String.join(",", orderIds), resultObject.getString("errmsg"));
                return new ArrayList<>();
            }

            JSONObject data = resultObject.getJSONObject("data");
            if (data == null) {
                return new ArrayList<>();
            }

            JSONObject domesticTrainData = data.getJSONObject("domestictrain_data");
            if (domesticTrainData == null) {
                return new ArrayList<>();
            }

            JSONArray orderList = domesticTrainData.getJSONArray("order_list");
            if (CollectionUtils.isEmpty(orderList)) {
                return new ArrayList<>();
            }

            List<JSONObject> result = new ArrayList<>();
            for (Object order : orderList) {
                result.add((JSONObject) order);
            }

            return result;

        } catch (Exception e) {
            log.error("获取火车票订单详情异常，订单IDs：{}", String.join(",", orderIds), e);
            return new ArrayList<>();
        }
    }
}
