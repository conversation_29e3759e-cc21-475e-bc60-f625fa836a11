package com.xhs.oa.travel_apply.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 携程订单状态枚举
 */
@Getter
@AllArgsConstructor
public enum CtripOrderStatusEnum {

    // 需要处理的状态
    Dealt("Dealt", "已经成交"),
    Rebooked("Rebooked", "改签成功"),
    RefundSuccess("RefundSuccess", "退票成功");

    private final String code;
    private final String description;


    public static boolean getNeedProcessStatus(String status) {
        return Arrays.stream(values()).map(CtripOrderStatusEnum::getCode).anyMatch(c -> c.equals(status));
    }
}
