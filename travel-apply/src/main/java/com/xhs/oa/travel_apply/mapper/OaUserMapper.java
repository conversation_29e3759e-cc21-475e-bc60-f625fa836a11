package com.xhs.oa.travel_apply.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xhs.oa.travel_apply.model.entity.OaUserEntity;
import com.xhs.oa.travel_apply.model.vo.UserPageQuery;
import com.xhs.oa.travel_apply.model.vo.UserVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date :2025/04/16 - 上午11:42
 * @description :
 */
@Mapper
public interface OaUserMapper extends BaseMapper<OaUserEntity> {

    /**
     * 查询指定ID列表中的在职员工
     *
     * @param ids 用户ID列表
     * @return 在职员工列表
     */
    default List<OaUserEntity> queryAvailableUserListByIds(List<String> ids) {
        return selectList(new LambdaQueryWrapper<OaUserEntity>()
                .eq(OaUserEntity::getAccountStatus, 1)
                .in(OaUserEntity::getUserId, ids));
    }

    /**
     * 分页查询用户信息
     *
     * @param query 查询条件
     * @return 用户信息列表
     */
    default List<UserVo> queryUserVoInfoByPage(UserPageQuery query) {
        // 构建查询条件
        LambdaQueryWrapper<OaUserEntity> wrapper = new LambdaQueryWrapper<OaUserEntity>()
                .in(query.getAccountStatus() != null, OaUserEntity::getAccountStatus, query.getAccountStatus())
                .in(query.getEmployTypes() != null && !query.getEmployTypes().isEmpty(), OaUserEntity::getEmployType, query.getEmployTypes())
                .orderByAsc(OaUserEntity::getUserId);

        // 执行分页查询
        Page<OaUserEntity> page = new Page<>(query.getPageNum(), query.getPageSize());
        Page<OaUserEntity> resultPage = selectPage(page, wrapper);

        // 转换为 UserVo 对象
        return resultPage.getRecords().stream()
                .map(OaUserEntity::convertToUserVo)
                .collect(Collectors.toList());
    }


}