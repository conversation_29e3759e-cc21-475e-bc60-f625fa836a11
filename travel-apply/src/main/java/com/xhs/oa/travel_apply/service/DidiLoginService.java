package com.xhs.oa.travel_apply.service;

import com.alibaba.fastjson2.JSONObject;
import com.xhs.finance.sso.UserInfoBag;
import com.xhs.oa.travel_apply.rpc.DidiRpc;
import com.xhs.oa.travel_apply.utils.DidiUtils;
import com.xhs.oa.office.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;

import static com.xhs.oa.travel_apply.constant.DidiConstant.*;

/**
 * <AUTHOR>
 * @date :2025/04/15 - 下午7:26
 * @description :
 */
@Slf4j
@Service
public class DidiLoginService {
    @Value("${didi_sign_key}")
    String signKey;
    @Value("${close_didi_sso_login:false}")
    boolean closeDidiSsoLogin;

    @Resource
    private DidiRpc didiRpc;
    @Resource
    private HandleErrorService handleErrorService;


    public String didiLogin() {
        if (closeDidiSsoLogin) {
            return DIDI_DEFAULT_LOGIN_URL;
        }

        // 拿userId
        String userId = UserInfoBag.get().getUserId();

        // 构造请求参数
        HashMap<String, String> params = didiRpc.getCommonParam();
        params.put("app_type", DIDI_APP_TYPE_H5);
        params.put("employee_number", userId);
        params.put("sign", DidiUtils.genSign(params, signKey));

        // 构造请求头
        HashMap<String, String> headers = new HashMap<>();

        try {
            // 请求滴滴
            JSONObject response = DidiUtils.sendGetRequest(DIDI_LOGIN_URL, headers, params);
            String errorCode = response.getString(DIDI_ERROR_CODE_KEY);
            String errorMessage = response.getString(DIDI_ERROR_MESSAGE_KEY);

            // 请求滴滴成功 但是参数不对
            if (!DIDI_SUCCESS_CODE.equals(errorCode)) {
                throw new BusinessException("login - 参数错误 - " + errorMessage);
            }
            return response.getJSONObject(DIDI_LOGIN_DATA_KEY).getString(DIDI_LOGIN_ENCRYPT_STR_KEY);
        } catch (Exception e) {
            log.error("login - 请求失败{}", e.getMessage(), e);
            handleErrorService.didiUserSyncErrorNotify(userId, e.getMessage());
        }
        return DIDI_DEFAULT_LOGIN_URL;
    }
}
