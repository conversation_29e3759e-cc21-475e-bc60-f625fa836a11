package com.xhs.oa.travel_apply.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 员工差旅行程实体
 */
@Data
@TableName("employee_travel_trip")
public class EmployeeTravelTripEntity {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 员工ID
     */
    private Long userId;

    /**
     * TMC类型
     */
    private Integer tmcType;

    /**
     * 行程类型
     */
    private String tripType;

    /**
     * 外部系统订单ID(携程ID或滴滴ID等)
     */
    private String externalSystemId;

    /**
     * 外部订单状态
     */
    private String externalStatus;

    /**
     * 出发时间
     */
    private Date departureTime;

    /**
     * 出发地点
     */
    private String departurePlace;

    /**
     * 到达时间
     */
    private Date arrivalTime;

    /**
     * 到达地点
     */
    private String arrivalPlace;

    /**
     * 是否有效(1:有效,0:无效)
     */
    private Integer isValid;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
