package com.xhs.oa.travel_apply.constant;

/**
 * <AUTHOR>
 * @date :2025/03/22 - 上午11:41
 * @description :
 */
public class DidiConstant {
    // 滴滴请求URL
    public static final String DIDI_GET_ACCESS_TOKEN_URL = "https://api.es.xiaojukeji.com/river/Auth/authorize";
    public static final String DIDI_LOGIN_URL = "https://api.es.xiaojukeji.com/river/Login/getLoginEncryptStr";
    public static final String DIDI_QUERY_USER_URL = "https://api.es.xiaojukeji.com/river/Member/detail";
    public static final String DIDI_ADD_USER_URL = "https://api.es.xiaojukeji.com/river/Member/single";
    public static final String DIDI_DELETE_USER_URL = "https://api.es.xiaojukeji.com/river/Member/del";
    public static final String DIDI_UPDATE_USER_URL = "https://api.es.xiaojukeji.com/river/Member/edit";
    public static final String DIDI_QUERY_REGULATION_LIST_URL = "https://api.es.xiaojukeji.com/river/Regulation/get";

    // 滴滴兜底登录链接
    public static final String DIDI_DEFAULT_LOGIN_URL = "https://open.es.xiaojukeji.com/webapp/feESWebapp/home";

    // 滴滴常量
    public static final String DIDI_SUCCESS_CODE = "0";
    public static final String DIDI_APP_TYPE_H5 = "2";
    public static final String DIDI_APP_PRODUCT_TYPE = "0";
    public static final String INIT_DEPARTMENT_ID = "1125909355515032";
    public static final String COMMON_NAME = "小红书员工";

    // 滴滴的key值
    public static final String DIDI_GRANT_TYPE_KEY = "client_credentials";
    public static final String DIDI_ERROR_CODE_KEY = "errno";
    public static final String DIDI_ERROR_MESSAGE_KEY = "errmsg";
    public static final String DIDI_ACCESS_TOKEN_KEY = "access_token";
    public static final String DIDI_LOGIN_DATA_KEY = "data";
    public static final String DIDI_LOGIN_ENCRYPT_STR_KEY = "encrypt_str";
    public static final String DIDI_ADD_USER_ID = "id";
    public static final String DIDI_ADD_USER_KEY = "data";

    // 外部关联表的常量
    public static final String EXTERNAL_DIDI_RELATE_TYPE = "user";
    public static final String EXTERNAL_DIDI_BUS_TYPE = "didi";
    public static final Integer EXTERNAL_DIDI_VALID = 1;
    public static final Integer EXTERNAL_DIDI_INVALID = 0;
}
