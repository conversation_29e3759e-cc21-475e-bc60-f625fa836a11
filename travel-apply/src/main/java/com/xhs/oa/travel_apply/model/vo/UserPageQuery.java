package com.xhs.oa.travel_apply.model.vo;

import com.xhs.finance.framework.page.PageQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper=true)
public class UserPageQuery extends PageQuery{

    @ApiModelProperty(notes = "用户id")
    private String id;
    @ApiModelProperty(notes = "员工状态 0：停用 1：开通")
    private Integer accountStatus;
    @ApiModelProperty(notes = "员工类型 0内部 1外部 2实习")
    private List<Integer> employTypes;
    @ApiModelProperty(notes = "单据所属部门")
    private List<Long> departmentIds;
    @ApiModelProperty(notes = "工作地点")
    private String workingPlace;

    @ApiModelProperty(notes = "人员类别 1:正式员工 2:实习生 3:编外人员 4:供应商派遣")
    private List<Integer> employmentTypeList;

    @ApiModelProperty(notes = "更新开始时间")
    private Date updateTimeStart;

    @ApiModelProperty(notes = "更新结束时间")
    private Date updateTimeEnd;
}
