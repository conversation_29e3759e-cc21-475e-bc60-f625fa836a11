package com.xhs.oa.travel_apply.rpc;

import com.xhs.finance.exception.BusinessException;
import corp.openapicalls.contract.Authentification;
import corp.openapicalls.contract.ordercontroller.SearchOrderRequest;
import corp.openapicalls.contract.ordercontroller.StandardSearchOrderResponse;
import corp.openapicalls.service.orderinfo.OrderInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class CtripOrderRpc extends AbstractCtripService {

    @Resource
    private OrderInfoService orderInfoService;

    /**
     * 查询携程订单信息
     * searchType: 1：包含所有产线 2：机票 3：国内火车票 4：酒店
     */
    public StandardSearchOrderResponse queryCtripOrderInfo(String orderId) {
        log.info("查询携程订单信息开始,order id:{}", orderId);
        String ticket = getOrderTicket().orElseThrow(() -> new BusinessException("获取携程请求token失败"));
        SearchOrderRequest request = new SearchOrderRequest();
        Authentification authInfo = new Authentification(appKey, ticket);
        request.setAuth(authInfo);
        request.setOrderID(orderId);
        StandardSearchOrderResponse response = orderInfoService.SearchOrder(request);
        if (!response.getStatus().getSuccess()) {
            log.error("查询携程订单信息失败:{}, request:{}", response.getStatus().getMessage(), orderId);
            throw new BusinessException("查询携程订单信息失败" + response.getStatus().getMessage());
        }
        return response;
    }
}
