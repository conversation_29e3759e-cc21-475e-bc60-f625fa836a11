package com.xhs.oa.travel_apply.model.vo;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Data
public class DidiProperty {

    // 「加班用车」制度ID
    @Value("${didi_car_regulation_overtime_id:}")
    String didiCarRegulationOvertimeId;

    // 「差旅用车」制度ID
    @Value("${didi_car_regulation_travel_id:}")
    String didiCarRegulationTravelId;

    // 「火车差标白名单」制度ID
    @Value("${didi_train_high_standard_id:}")
    String didiTrainHighStandardId;

    // 「火车非差标白名单」制度ID
    @Value("${didi_train_normal_standard_id:}")
    String didiTrainNormalStandardId;

    // 编外人员白名单
    @ApolloJsonValue("${travel_didi_train_dispatch_emp:[]}")
    private List<String> travelDidiTrainDispatchEmp;

    // 差标白名单，配置出行人
    @Value("${travel_didi_train_high_level_user:}")
    String travelDidiTrainHighLevelUser;

}
