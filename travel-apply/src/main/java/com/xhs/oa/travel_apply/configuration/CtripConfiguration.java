package com.xhs.oa.travel_apply.configuration;

import corp.openapicalls.service.orderinfo.OrderInfoService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class CtripConfiguration {

    @Bean(name = "orderInfoService")
    public OrderInfoService getOrderInfoService() {
        return new OrderInfoService();
    }

}
