package com.xhs.oa.travel_apply.service;

import com.alibaba.fastjson2.JSONObject;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.xhs.oa.travel_apply.mapper.OaUserMapper;
import com.xhs.rbac.client.common.BusinessException;
import com.xiaohongshu.fls.rpc.oamiddle.msg.request.SendMsgRequest;
import com.xiaohongshu.fls.rpc.oamiddle.msg.response.MsgInfoResponse;
import com.xiaohongshu.fls.rpc.oamiddle.msg.service.MsgMiddleRpcService;
import com.xiaohongshu.infra.rpc.base.Context;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static com.xhs.oa.travel_apply.constant.CommonConstant.DIDI_USER_SYNC_ERROR_NOTIFY_MESSAGE_CODE;
import static com.xhs.oa.travel_apply.constant.CommonConstant.WX_MESSAGE_appName;

/**
 * <AUTHOR>
 * @date :2025/04/17 - 下午5:44
 * @description :
 */
@Slf4j
@Service
public class HandleErrorService {
    // 滴滴用户同步报警接收者列表
    @ApolloJsonValue("${didi_userSync_error_notify_userList:[]}")
    List<String> didiUserSyncErrorNotifyUserList;

    @Resource
    private OaUserMapper oaUserMapper;
    @Resource
    private MsgMiddleRpcService.Iface msgMiddleRpcService;


    public void didiUserSyncErrorNotify(String errorUserId, String errorMessage) {
        JSONObject param = new JSONObject();
        param.put("errorUserId", errorUserId);
        param.put("errorMessage", errorMessage);
        sendMessage(didiUserSyncErrorNotifyUserList, DIDI_USER_SYNC_ERROR_NOTIFY_MESSAGE_CODE, WX_MESSAGE_appName, param);
    }

    public void sendMessage(List<String> emailList, String messageCode, String appName, JSONObject param) {
        SendMsgRequest request = new SendMsgRequest();
        // 消息中台模版code
        request.setCode(messageCode);
        // 消息中台的应用名
        request.setSysPertain(appName);
        // 幂等性校验字段
        request.setUniqueKey(UUID.randomUUID().toString());
        Map<String, String> paramMap = new HashMap<>();
        param.keySet().forEach(i -> paramMap.put(i, param.getString(i)));
        // 可变参数
        request.setParams(paramMap);
        // 收件人
        request.setRecipients(emailList);
        log.info("sengMsg - request：{}", JSONObject.toJSONString(request));
        // 调用
        MsgInfoResponse response;
        try {
            response = msgMiddleRpcService.sengMsg(new Context(), request);
            log.info("sengMsg - response = {}", JSONObject.toJSONString(response));
        } catch (Exception e) {
            log.error("调用企业微信中台失败, {}", e.getMessage(), e);
            throw new BusinessException("调用企业微信中台失败");
        }
        if (!response.success) {
            log.error("调用企业微信中台失败, {}", response.getMessage());
            throw new BusinessException("调用企业微信中台失败");
        }
    }
}
