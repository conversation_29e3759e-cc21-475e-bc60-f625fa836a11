package com.xhs.oa.travel_apply.rpc;

import com.google.common.base.Stopwatch;
import com.xhs.cache.RedisClient;
import corp.openapicalls.contract.ordercontroller.ticket.OrderSearchTicketResponse;
import corp.openapicalls.service.ticket.CorpTicketService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public abstract class AbstractCtripService {

	private static final String ORDER_TICKET_REDIS_KEY = "ctrip_order_ticket";

	private static final int TICKET_REDIS_EXPIRE = 3600;

	protected String appKey = "obk_xiaohongshu";
	protected String appSecurity = "vC5~JZF~LFkl^iZb@528csXu";
	protected String corpId = "xiaohongshu";
	protected String subAccountName="xiaohongshu_\\u63D0\\u524D\\u5BA1\\u6279";
	protected String version="1.0";
	@Resource
	private RedisClient redisClient;

	/**
	 * 获取Ticket，订单相关通用，人事批量开卡
	 *
	 * @return ticket 异常返空，携程错误信息落业务日志
	 */
	protected Optional<String> getOrderTicket() {
		Object o = redisClient.get(ORDER_TICKET_REDIS_KEY);
		if (o instanceof String) {
			String ticket = (String) o;
			redisClient.expair(ORDER_TICKET_REDIS_KEY, TICKET_REDIS_EXPIRE);
			return Optional.of(ticket);
		}

		Stopwatch stopwatch = Stopwatch.createStarted();
		try {
			OrderSearchTicketResponse ticketResponse = CorpTicketService.getOrderSearchTicket(appKey, appSecurity, version);
			if (ticketResponse == null || ticketResponse.getStatus() == null) {
				log.error("getOrderTicket response empty");
				return Optional.empty();
			}
			if (!ticketResponse.getStatus().getSuccess()) {
				log.error("getOrderTicket error, msg :{}", ticketResponse.getStatus().getMessage());
				return Optional.empty();
			}
			String ticket = ticketResponse.getTicket();
			redisClient.set(ORDER_TICKET_REDIS_KEY, ticket, TICKET_REDIS_EXPIRE);
			return Optional.of(ticket);
		} finally {
			log.info("getOrderTicket total costTime:{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));
		}
	}

}
