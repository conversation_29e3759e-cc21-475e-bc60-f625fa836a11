package com.xhs.oa.travel_apply.model.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date :2025/05/08 - 下午3:06
 * @description : 字段名均与滴滴文档保持一致
 */
@Data
public class DidiUserDto {
    // 滴滴系统中的userId
    private String member_id;

    // 滴滴系统中的手机号
    private String phone;

    // 滴滴系统中的真名
    private String realname;

    // 小红书系统中的userId
    private String employee_number;

    // 小红书系统中的邮箱
    private String email;

    // 系统角色
    private Integer system_role;

    // 系统角色
    private String role_ids;

    // base地
    private String residentsname;

    // 预算中心ID
    private String budget_center_id;

    // 用户所拥有的制度列表
    private List<String> regulation_id;

    // 项目ID列表
    private String project_ids;
}
