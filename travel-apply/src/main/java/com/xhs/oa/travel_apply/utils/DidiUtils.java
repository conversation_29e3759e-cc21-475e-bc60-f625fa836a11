package com.xhs.oa.travel_apply.utils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.xhs.oa.office.exception.BusinessException;
import com.xhs.oa.office.utils.RetryUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.*;

/**
 * <AUTHOR>
 * @date :2025/03/22 - 上午10:50
 * @description :
 */
@Slf4j
@Component
public class DidiUtils {
    private static DidiSyncSwitch didiSyncSwitch;

    @Autowired
    public void setDidiSyncControl(DidiSyncSwitch syncControl) {
        DidiUtils.didiSyncSwitch = syncControl;
    }
    
    // Java md5算法 - 来源滴滴SDK文档
    public static String md5(String plainText)
    {
        byte[] secretBytes = null;
        try {
            MessageDigest md  = MessageDigest.getInstance("MD5");
            md.update(plainText.getBytes("utf-8"));
            secretBytes = md.digest();
        }
        catch (Exception e) {
            log.error("DidiSignGenUtils - md5, {}", e.getMessage(), e);
            throw new BusinessException(e);
        }


        String md5code = new BigInteger(1, secretBytes).toString(16);
        int length = md5code.length();
        for (int i = 0; i < 32 - length; i++) {
            md5code = "0" + md5code;
        }
        return md5code;
    }

    // Java sha256算法 - 来源滴滴SDK文档
    public static String sha256Hex(String input) {
        try {
            // 创建一个MessageDigest实例，并指定使用SHA-256算法
            MessageDigest digest = MessageDigest.getInstance("SHA-256");

            // 将输入字符串转换为字节数组，并更新摘要
            byte[] hash = digest.digest(input.getBytes(StandardCharsets.UTF_8));

            // 将字节数组转换为十六进制字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) hexString.append('0');
                hexString.append(hex);
            }

            return hexString.toString();
        } catch (Exception e) {
            log.error("DidiSignGenUtils - sha256Hex, {}", e.getMessage(), e);
            throw new BusinessException(e);
        }
    }

    // 签名生成算法 - 来源滴滴SDK文档
    public static String genSign(HashMap<String, String> params, String signKey)
    {
        params.put("sign_key", signKey);
        String result = "";
        try {
            List<Map.Entry<String, String>> infoIds = new ArrayList<Map.Entry<String, String>>(params.entrySet());
            Collections.sort(infoIds, new Comparator<Map.Entry<String, String>>() {
                public int compare(Map.Entry<String, String> o1, Map.Entry<String, String> o2) {
                    return (o1.getKey()).toString().compareTo(o2.getKey());
                }
            });
            // 构造签名键值对的格式
            for (Map.Entry<String, String> item : infoIds) {
                if (item.getKey() != null || item.getKey() != "") {
                    String key = item.getKey();
                    String val = item.getValue().trim();
                    if (result == "") {
                        result += key + "=" + val;
                    } else {
                        result += "&" + key + "=" + val;
                    }
                }
            }
        } catch(Exception e) {
            log.error("DidiSignGenUtils - genSign, {}", e.getMessage(), e);
            throw new BusinessException(e);
        }
        return md5(result);
    }

    public static JSONObject sendPostRequest(String url, Map<String, String> headers, Map<String, String> body) {
        final int timeout = 5000; // 超时时间5秒

        return RetryUtil.executeWithRetry(() -> {
            try {
                // 创建 HttpClient 实例并配置超时
                RequestConfig requestConfig = RequestConfig.custom()
                        .setConnectTimeout(timeout)
                        .setSocketTimeout(timeout)
                        .setConnectionRequestTimeout(timeout)
                        .build();
                        
                CloseableHttpClient httpClient = HttpClients.custom()
                        .setDefaultRequestConfig(requestConfig)
                        .build();
                        
                HttpPost request = new HttpPost(url);

                // 添加请求头
                headers.forEach(request::addHeader);

                // 添加请求体
                String requestBody = JSONObject.toJSONString(body);
                request.setEntity(new StringEntity(requestBody, StandardCharsets.UTF_8));

                // 请求
                CloseableHttpResponse response = httpClient.execute(request);
                String data = EntityUtils.toString(response.getEntity());
                JSONObject result = JSON.parseObject(data);

                // 序列化
                log.info("sendPostRequest - 入参, url = {}, header = {}, body = {}", url, JSONObject.toJSONString(headers), JSONObject.toJSONString(body));
                log.info("sendPostRequest - 出参 = {}", result);
                return result;
            } catch (Exception e) {
                log.debug("sendPostRequest - 异常: {}", e.getMessage());
                throw new RuntimeException(e);
            }
        }, 1, 100, "滴滴POST请求");
    }

    public static JSONObject sendGetRequest(String url, Map<String, String> headers, Map<String, String> params) {
        final int timeout = 5000; // 超时时间5秒
        
        // 在Lambda外构建URL，因为要在Lambda中使用
        StringBuilder sb = new StringBuilder(url);
        sb.append("?");
        params.forEach((key, value) -> sb.append(key).append("=").append(value).append("&"));
        sb.deleteCharAt(sb.length() - 1);
        final String finalUrl = sb.toString();
        
        return RetryUtil.executeWithRetry(() -> {
            try {
                // 创建 HttpClient 实例并配置超时
                RequestConfig requestConfig = RequestConfig.custom()
                        .setConnectTimeout(timeout)
                        .setSocketTimeout(timeout)
                        .setConnectionRequestTimeout(timeout)
                        .build();
                        
                CloseableHttpClient httpClient = HttpClients.custom()
                        .setDefaultRequestConfig(requestConfig)
                        .build();
                        
                HttpGet request = new HttpGet(finalUrl);

                // 添加请求头
                headers.forEach(request::addHeader);

                // 请求
                CloseableHttpResponse response = httpClient.execute(request);
                String data = EntityUtils.toString(response.getEntity());
                JSONObject result = JSON.parseObject(data);

                // 序列化
                log.info("sendGetRequest - 入参, url = {}, header = {}, params = {}", finalUrl, JSONObject.toJSONString(headers), JSONObject.toJSONString(params));
                log.info("sendGetRequest - 出参 = {}", result);
                return result;
            } catch (Exception e) {
                log.debug("sendGetRequest - 异常: {}", e.getMessage());
                throw new RuntimeException(e);
            }
        }, 1, 100, "滴滴GET请求");
    }

}
