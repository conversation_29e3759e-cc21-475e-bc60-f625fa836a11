package com.xhs.oa.travel_apply.service;

import com.alibaba.fastjson2.JSON;
import com.xhs.oa.office.enums.ValidEnum;
import com.xhs.oa.travel_apply.enums.CtripOrderStatusEnum;
import com.xhs.oa.travel_apply.enums.PassengerType;
import com.xhs.oa.travel_apply.enums.TmcTypeEnum;
import com.xhs.oa.travel_apply.mapper.TravelSchedulePassengerMapper;
import com.xhs.oa.travel_apply.model.entity.EmployeeTravelTripEntity;
import com.xhs.oa.travel_apply.model.entity.TravelSchedulePassengerEntity;
import com.xhs.oa.travel_apply.model.request.CtripOrderStatusRequest;
import com.xhs.oa.travel_apply.model.response.CtripOrderStatusResponse;
import com.xhs.oa.travel_apply.mq.producer.TravelOrderStatusProducer;
import com.xhs.oa.travel_apply.office_mapper.EmployeeTravelTripMapper;
import com.xhs.oa.travel_apply.rpc.CtripOrderRpc;
import com.xhs.oa.travel_apply.utils.CtripSignUtils;
import corp.openapicalls.contract.ordercontroller.StandardItineraryEntity;
import corp.openapicalls.contract.ordercontroller.StandardSearchOrderResponse;
import corp.openapicalls.contract.ordercontroller.client.flight.BasicInfo;
import corp.openapicalls.contract.ordercontroller.client.flight.FlightOrderInfoEntity;
import corp.openapicalls.contract.ordercontroller.client.flight.TripRecordInfoEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 携程订单状态处理服务实现
 */
@Slf4j
@Service
public class CtripOrderStatusService {

    @Value("${ctrip.secret:}")
    private String ctripSecret;

    @Resource
    private CtripOrderRpc ctripOrderRpc;

    @Resource
    private TravelSchedulePassengerMapper travelSchedulePassengerMapper;

    @Resource
    private EmployeeTravelTripMapper employeeTravelTripMapper;

    @Resource
    private TravelOrderStatusProducer travelOrderStatusProducer;


    public CtripOrderStatusResponse handleOrderStatusChange(CtripOrderStatusRequest request) {
        log.info("收到携程订单状态变化回调，请求参数：{}", JSON.toJSONString(request));

        try {
            // 1. 参数校验
            CtripOrderStatusResponse validationResult = validateRequest(request);
            if (validationResult != null) {
                return validationResult;
            }

            // 2. 签名验证
            if (!verifySign(request)) {
                log.error("携程订单状态回调签名验证失败，orderId：{}", request.getOrderId());
                return CtripOrderStatusResponse.error("1001", "签名验证失败");
            }

            // 3. 处理订单状态变化
            processOrderStatusChange(request);

            log.info("携程订单状态变化处理成功，orderId：{}", request.getOrderId());
            return CtripOrderStatusResponse.success();

        } catch (Exception e) {
            log.error("处理携程订单状态变化失败，orderId：{}，错误信息：", request.getOrderId(), e);
            return CtripOrderStatusResponse.error("1000", "系统内部错误");
        }
    }

    /**
     * 参数校验
     */
    private CtripOrderStatusResponse validateRequest(CtripOrderStatusRequest request) {
        if (request == null) {
            log.error("携程订单状态回调请求参数为空");
            return CtripOrderStatusResponse.error("1002", "请求参数为空");
        }

        if (StringUtils.isBlank(request.getProductType())) {
            log.error("携程订单状态回调产品类型为空");
            return CtripOrderStatusResponse.error("1003", "产品类型不能为空");
        }

        if (StringUtils.isBlank(request.getOrderId())) {
            log.error("携程订单状态回调订单号为空");
            return CtripOrderStatusResponse.error("1004", "订单号不能为空");
        }

        if (StringUtils.isBlank(request.getOrderStatus())) {
            log.error("携程订单状态回调订单状态为空");
            return CtripOrderStatusResponse.error("1005", "订单状态不能为空");
        }

        if (StringUtils.isBlank(request.getCorpId())) {
            log.error("携程订单状态回调公司编号为空");
            return CtripOrderStatusResponse.error("1006", "公司编号不能为空");
        }

        if (StringUtils.isBlank(request.getSign())) {
            log.error("携程订单状态回调签名为空");
            return CtripOrderStatusResponse.error("1007", "签名不能为空");
        }

        return null;
    }

    /**
     * 签名验证
     */
    private boolean verifySign(CtripOrderStatusRequest request) {
        if (StringUtils.isBlank(ctripSecret)) {
            log.warn("携程Secret未配置，跳过签名验证");
            return true;
        }

        return CtripSignUtils.verifySign(
                request.getProductType(),
                request.getOrderId(),
                request.getOrderStatus(),
                request.getCorpId(),
                ctripSecret,
                request.getSign()
        );
    }

    /**
     * 处理订单状态变化
     */
    private void processOrderStatusChange(CtripOrderStatusRequest request) {
        log.info("开始处理订单状态变化，产品类型：{}，订单号：{}，订单状态：{}",
                request.getProductType(), request.getOrderId(), request.getOrderStatus());

        // 根据产品类型和订单状态进行不同的处理逻辑
        switch (request.getProductType()) {
            case "FlightInternational": // 国际机票
            case "FlightDomestic": // 国内机票
                handleFlightOrderStatus(request);
                break;
            default:
                log.warn("当前产品类型不需要处理：{}", request.getProductType());
                break;
        }
    }

    /**
     * 处理机票订单状态
     */
    private void handleFlightOrderStatus(CtripOrderStatusRequest request) {
        log.info("处理机票订单状态变化，订单号：{}，状态：{}", request.getOrderId(), request.getOrderStatus());

        try {
            // 1. 根据订单状态进行不同的处理逻辑，哪些状态需要过滤、哪些状态需要插入、哪些状态需要更新、哪些状态需要无效记录
            if (!CtripOrderStatusEnum.getNeedProcessStatus(request.getOrderStatus())) {
                log.info("订单状态不需要处理，跳过处理，订单号：{}，状态：{}", request.getOrderId(), request.getOrderStatus());
                return;
            }

            // 2. 更新订单状态到数据库，获取订单对应的行程信息，调用CtripOrderRpc.queryCtripOrderInfo方法
            StandardSearchOrderResponse orderResponse = ctripOrderRpc.queryCtripOrderInfo(request.getOrderId());
            if (orderResponse == null) {
                log.error("查询携程订单信息失败，订单号：{}", request.getOrderId());
                return;
            }

            // 提取journeyNO字段 对应我们本地的schedule_form_num
            List<String> journeyNoList = extractJourneyNo(orderResponse);
            if (CollectionUtils.isEmpty(journeyNoList)) {
                log.warn("未能提取到journeyNO字段，订单号：{}", request.getOrderId());
                return;
            }
            String journeyNo = journeyNoList.get(0);
            // journeyNO字段等于travel_schedule_passenger表中的schedule_form_num字段。查询表获取出行人。
            List<TravelSchedulePassengerEntity> passengers = travelSchedulePassengerMapper.queryByScheFormNumPassengerType(journeyNo, PassengerType.PASSENGER.getCode());
            if (CollectionUtils.isEmpty(passengers)) {
                log.warn("未找到对应的出行人信息，journeyNoList：{}", journeyNoList);
                return;
            }
            TravelSchedulePassengerEntity passengerEntity = passengers.get(0);
            // 提取航班信息
            List<TripRecordInfoEntity> flightInfoList = extractFlightInfo(orderResponse);
            if (CollectionUtils.isEmpty(flightInfoList)) {
                log.warn("未能提取到航班信息，订单号：{}", request.getOrderId());
                return;
            }
            // 更新或者插入本地表employee_travel_trip中
            ((CtripOrderStatusService) AopContext.currentProxy()).processEmployeeTravelTrip(request, passengerEntity, request.getOrderStatus(), flightInfoList);
        } catch (Exception e) {
            log.error("处理机票订单状态异常，订单号：{}，错误信息：", request.getOrderId(), e);
        }
    }

    /**
     * 提取journeyNO字段
     */
    private List<String> extractJourneyNo(StandardSearchOrderResponse orderResponse) {
        try {
            // 从StandardSearchOrderResponse中提取journeyNO字段
            // 这里需要根据实际的API响应结构来实现
            List<StandardItineraryEntity> itineraryList = orderResponse.getItineraryList();
            if (CollectionUtils.isEmpty(itineraryList)) {
                return Collections.emptyList();
            }
            return itineraryList.stream().map(StandardItineraryEntity::getFlightOrderInfoList)
                    .flatMap(Collection::stream)
                    .map(FlightOrderInfoEntity::getBasicInfo)
                    .map(BasicInfo::getJourneyID)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("提取journeyNO字段异常：", e);
        }
        return Collections.emptyList();
    }

    /**
     * 提取航班信息
     */
    private List<TripRecordInfoEntity> extractFlightInfo(StandardSearchOrderResponse orderResponse) {
        // 从StandardSearchOrderResponse中提取航班信息
        // 这里需要根据实际的API响应结构来实现
        List<StandardItineraryEntity> itineraryList = orderResponse.getItineraryList();
        if (CollectionUtils.isEmpty(itineraryList)) {
            return Collections.emptyList();
        }
        return itineraryList.stream().map(StandardItineraryEntity::getFlightOrderInfoList).flatMap(Collection::stream)
                .map(FlightOrderInfoEntity::getTripRecordInfoList).flatMap(Collection::stream).collect(Collectors.toList());
    }

    /**
     * 处理员工差旅行程记录
     */
    @Transactional(rollbackFor = Exception.class, transactionManager = "oaOfficeTransactionManager")
    public void processEmployeeTravelTrip(CtripOrderStatusRequest request, TravelSchedulePassengerEntity passenger, String orderStatus, List<TripRecordInfoEntity> flightInfoList) {
        try {
            log.info("开始处理员工差旅行程记录，订单号：{}，用户ID：{}", request.getOrderId(), passenger.getPassengerId());

            // 1. 根据orderId查询出全部有效记录
            List<EmployeeTravelTripEntity> existingRecords = employeeTravelTripMapper.queryByExternalSystemId(request.getOrderId());
            // 2. 删除现有记录 - 将isValid字段设置为无效
            int deletedCount = 0;
            if (!CollectionUtils.isEmpty(existingRecords)) {
                deletedCount = employeeTravelTripMapper.deleteByExternalSystemId(request.getOrderId());
                // 3. 为每个删除的记录发送删除事件
                for (EmployeeTravelTripEntity deletedRecord : existingRecords) {
                    deletedRecord.setIsValid(0);
                    travelOrderStatusProducer.sendMessage(deletedRecord);
                }
            }
            log.info("删除现有记录数量：{}，订单号：{}", deletedCount, request.getOrderId());
            // 4. 为每个航班信息创建记录（仅在特定状态下）
            if (orderStatus.equals(CtripOrderStatusEnum.Dealt.getCode()) || orderStatus.equals(CtripOrderStatusEnum.Rebooked.getCode())) {
                for (TripRecordInfoEntity flightInfo : flightInfoList) {
                    EmployeeTravelTripEntity inserted = insertEmployeeTravelTrip(request, passenger, flightInfo);
                    // 5. 发送插入消息
                    if (inserted != null) {
                        travelOrderStatusProducer.sendMessage(inserted);
                    }
                }
                log.info("处理员工差旅行程记录完成，订单号：{}，用户ID：{}", request.getOrderId(), passenger.getPassengerId());
            }
        } catch (Exception e) {
            log.error("处理员工差旅行程记录异常，订单号：{}，用户ID：{}，错误信息：", request.getOrderId(), passenger.getPassengerId(), e);
            throw e; // 重新抛出异常，让上层处理
        }
    }

    /**
     * 插入员工差旅行程记录
     */
    private EmployeeTravelTripEntity insertEmployeeTravelTrip(CtripOrderStatusRequest request, TravelSchedulePassengerEntity passenger, TripRecordInfoEntity flightInfo) {
        try {
            EmployeeTravelTripEntity newTrip = new EmployeeTravelTripEntity();
            newTrip.setTmcType(TmcTypeEnum.CTRIP.getCode());
            newTrip.setUserId(Long.parseLong(passenger.getPassengerId()));
            newTrip.setTripType("flight");
            newTrip.setExternalSystemId(request.getOrderId());
            newTrip.setExternalStatus(request.getOrderStatus());
            // 设置航班信息
            newTrip.setDeparturePlace(flightInfo.getDepartureCityName());
            newTrip.setArrivalPlace(flightInfo.getArrivalCityName());
            String takeOffTimeUTC = flightInfo.getTakeOffTime();
            newTrip.setDepartureTime(DateUtils.parseDate(takeOffTimeUTC, "yyyy-MM-dd HH:mm:ss.SSS"));
            String arrivalTimeUTC = flightInfo.getArrivalTime();
            newTrip.setArrivalTime(DateUtils.parseDate(arrivalTimeUTC, "yyyy-MM-dd HH:mm:ss.SSS"));
            newTrip.setIsValid(ValidEnum.VALID.getCode());
            newTrip.setCreateTime(new Date());
            newTrip.setUpdateTime(new Date());
            employeeTravelTripMapper.insert(newTrip);
            log.info("插入员工差旅行程记录成功，订单号：{}，用户ID：{}，航班号：{}", request.getOrderId(), passenger.getPassengerId(), flightInfo.getFlight());
            return newTrip;
        } catch (Exception e) {
            log.error("插入员工差旅行程记录失败，订单号：{}，用户ID：{}，错误信息：", request.getOrderId(), passenger.getPassengerId(), e);
            return null;
        }
    }

}
